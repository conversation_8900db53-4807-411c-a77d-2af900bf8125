import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/adhoctask.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/project/statusreport/statusreport.dart';
import 'package:tangoworkplace/models/project/statusreport/statusreportdetails.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/services/projectmanagement/project/project_statusreport_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../../../../common/component_utils.dart';

class StatusReportController extends GetxController {
  var isLoading = false.obs;
  RxList<StatusReport> statusreportsdata = <StatusReport>[].obs;
  Rx<StatusReport> statusreport = StatusReport().obs;
  var isdetailsloading = false.obs;
  var sr_mode = ''.obs;

  var labelsloading = false.obs;
  Future getlabels(LabelController talabel) async {
    try {
      await talabel.getlabels('TMCMOBILE_PROJECTS_STATUSREPORTS', 'projectstatusreport');
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future loadStatusReportsData(int? projectid) async {
    debugPrint('------------loadStatusReportsData--------$projectid');
    isLoading(true);
    List<StatusReport> reportRec;
    statusreportsdata.clear();
    Map<String, dynamic>? resMap;

    //var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);

    Map<String, dynamic> dynamicMap = {
      'a': ['project_id', 'EXACT', projectid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(statusreportshdrurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            reportRec = tagObjsJson.map((tagJson) => StatusReport.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${reportRec.length}');

            statusreportsdata.addAll(reportRec);
          }
        } else {
          statusreportsdata.clear();
        }
      } else {
        statusreportsdata.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isLoading(false);
  }

  var weekendingCtrl = TextEditingController();
  RxList<LookupValues> financeHealthlov = <LookupValues>[].obs;
  RxList<LookupValues> scheduleHealthlov = <LookupValues>[].obs;
  RxList<LookupValues> riskHealthlov = <LookupValues>[].obs;
  RxList<LookupValues> statusreportrisklov = <LookupValues>[].obs;
  Future setCurrentRow() async {
    weekendingCtrl.text = ComponentUtils.dateToString(statusreport.value.weekEnding);

    await getLovItems('FINANCIAL_HEALTH,SCHEDULE_HEALTH,RISK_HEALTH,STATUS_REPORT_RISK');
  }

  Future getLovItems(var lookupcodes) async {
    financeHealthlov.value.clear();
    scheduleHealthlov.value.clear();
    riskHealthlov.value.clear();
    statusreportrisklov.clear();
    try {
      var jsonObj = await CommonServices.fetchMultiLovs(lookupcodes);
      if (jsonObj != null) {
        var fh = jsonObj['FINANCIAL_HEALTH'] as List?;
        var sh = jsonObj['SCHEDULE_HEALTH'] as List?;
        var rh = jsonObj['RISK_HEALTH'] as List?;
        var srrh = jsonObj['STATUS_REPORT_RISK'] as List?;
        if ((financeHealthlov.value.isEmpty || financeHealthlov.value == null) && fh != null)
          financeHealthlov.value = fh.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
        financeHealthlov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

        if ((scheduleHealthlov.value.isEmpty || scheduleHealthlov.value == null) && sh != null)
          scheduleHealthlov.value = sh.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
        scheduleHealthlov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

        if ((riskHealthlov.value.isEmpty || riskHealthlov.value == null) && rh != null)
          riskHealthlov.value = rh.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
        riskHealthlov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

        if ((statusreportrisklov.value.isEmpty || statusreportrisklov.value == null) && srrh != null)
          statusreportrisklov.value = srrh.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
        statusreportrisklov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));
      }
    } catch (error) {
      debugPrint('$error');
    }
  }

  Future onSaveStatusReport() async {
    debugPrint('statusreport data    ${jsonEncode(statusreport.value)}');
    ProgressUtil.showLoaderDialog(Get.context!);
    try {
      var result;

      result = await StatusReportServices.saveStatusReport(statusreport.value, sr_mode.value);
      if (result != null && result > 0) {
        statusreport.value.statusId = result;

        debugPrint('${statusreport.value.statusId}--------id-----$result');
        sr_mode.value = 'edit';

        ComponentUtils.showsnackbar(text: "Data saved successfully...");
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      }
    } catch (e) {
      ProgressUtil.closeLoaderDialog(Get.context!);
      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      debugPrint('${e}');
    }
    ProgressUtil.closeLoaderDialog(Get.context!);
  }

  var detdataloading = false.obs;
  RxList<StatusReportDetailsData> srdetails = <StatusReportDetailsData>[].obs;
  RxList<StatusReportDetailsData> srdetchangeList = <StatusReportDetailsData>[].obs;
  var projectedCompliDateCtrl = TextEditingController();
  var actualCompliDateCtrl = TextEditingController();
  var detRisk = ''.obs;
  var updateDetflag = false.obs;

  Future loadStatusreportDetailsData(int? projectid, int? statusid) async {
    debugPrint('--------loadStatusreportDetailsData-------');
    detdataloading(true);
    List<StatusReportDetailsData> drdetRec;
    srdetails.clear();
    Map<String, dynamic>? resMap;

    //var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);

    Map<String, dynamic> dynamicMap = {
      'a': ['project_id', 'EXACT', projectid.toString()],
      'b': ['status_id', 'EXACT', statusid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(projectstatusreportdetailsurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            drdetRec = tagObjsJson.map((tagJson) => StatusReportDetailsData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${drdetRec.length}');

            srdetails.addAll(drdetRec);
          }
        } else {
          statusreportsdata.clear();
        }
      } else {
        statusreportsdata.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    detdataloading(false);
  }

  deleteDetItemfromlist(int? detid) {
    debugPrint('deletefilefromlist---------${detid}');
    // srdetails.removeWhere((item) => item.detailStatusId == detid);

    srdetails.removeWhere((item) {
      if (item.detailStatusId == detid) {
        item.actiontype = 'delete';
        srdetchangeList.removeWhere((element) => element.detailStatusId == detid);
        srdetchangeList.add(item);
        return true;
      } else
        return false;
    });
  }

  updateDetItemFromlist(StatusReportDetailsData srd, int index) {
    try {
      srd.actiontype = 'update';
      srd.actualCompletionDate = actualCompliDateCtrl.text ?? null;
      srd.projectedCompletionDate = projectedCompliDateCtrl.text ?? null;
      srd.milestoneRisk = detRisk.value;
      srdetails[index] = srd;
      debugPrint('update data -----${jsonEncode(srd)}');
      srdetchangeList.add(srd);
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future saveSrDetData(int? projectid, int? statusid) async {
    try {
      var result = 'success';
      if (srdetchangeList.value.isNotEmpty && srdetchangeList.value != null) {
        debugPrint('updating data    ${jsonEncode(srdetchangeList.value)}');
        Map m = {
          'project_id': projectid,
          'status_id': statusid,
          'details': srdetchangeList.value,
        };

        result = await (StatusReportServices.saveSrDetailsdata(m) as Future<String>);
      }
      if (result == 'success') {
        Get.back();
        ComponentUtils.showsnackbar(text: "Data saved successfully...");
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      }
    } catch (e) {
      debugPrint('$e');
    }
  }
}
