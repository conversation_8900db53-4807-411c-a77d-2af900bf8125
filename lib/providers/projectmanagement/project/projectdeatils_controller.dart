import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/services/projectmanagement/project/project_search_services.dart';

class ProjectDetailsController extends GetxController {
  static ProjectDetailsController instance = Get.find();
  dynamic args = Get.arguments;

  var projectsView = [].obs;

  @override
  void onInit() {
    //debugPrint('project id>>>>${args[0]}');
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    debugPrint('------------onClose--------');
    super.onClose();
  }
}
