import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/budget/budget.dart';
import 'package:tangoworkplace/models/project/budget/budgetoverview.dart';
import 'package:tangoworkplace/models/project/budget/cohdr.dart';
import 'package:tangoworkplace/models/project/budget/pohdr.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

class CoController extends GetxController {
  RxList<CoHdr> codata = <CoHdr>[].obs;
  var ishdrloading = false.obs;
  var tblflag = false.obs;

  loadCOheaderData({int? projectid}) async {
    debugPrint('------------loadCOheaderData--------$projectid');
    try {
      ishdrloading.value = true;
      List<CoHdr> coh;

      Map<String, dynamic>? resMap;
      var filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['p_user_name', 'EXACT', UserContext.info()!.userName],
        'b': ['p_isContractor', 'EXACT', UserContext.info()!.isContractor],
        'c': ['project_id', 'EXACT', projectid.toString()],
      };
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      //}
      resMap = await CommonServices.fetchDynamicApi(cohdrurl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            coh = tagObjsJson.map((tagJson) => CoHdr.fromJson(tagJson)).toList();
            debugPrint('co header Count------ ${coh.length}');
            //codata.value = (coh.isNotEmpty && coh.length > 0) ? coh : null;
            codata.value = coh;
          }
        } else {
          codata.value.clear();
        }
      } else {
        codata.value.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    ishdrloading.value = false;
  }
}
