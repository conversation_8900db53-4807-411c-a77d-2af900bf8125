import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/project/budget/budget.dart';
import 'package:tangoworkplace/models/project/budget/budgetoverview.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/constvariables.dart';

import '../../../../common/component_utils.dart';
import '../../../../models/common/budget/budgetRecord.dart';
import '../../../../models/common/budget/budgetResponse.dart';
import '../../../../models/common/utils/paginationInfo.dart';

class BudgetController extends GetxController {
  static BudgetController instance = Get.find();
  LabelController talabel = Get.find<LabelController>();
  dynamic args = Get.arguments;

  Rx<BudgetOverview?> budgetsumsdata = BudgetOverview().obs;
  var isloading = false.obs;
  var labelsloading = true.obs;
  TextEditingController bdgtSrchTxt = TextEditingController();

  getlabels(LabelController talabel, {String? filtertype, String? key, String? refr}) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_PROJECTS_BUDGET', 'budget');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  loadBudgetSnapshot(int? entityId) async {
    debugPrint('------------loadBudgetSnapshot--------$entityId');
    try {
      isloading.value = true;
      List<BudgetOverview> bo;
      budgetsumsdata.value = new BudgetOverview();
      Map<String, dynamic>? resMap;
      var filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['project_id', 'EXACT', entityId.toString()],
      };
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      //}
      resMap = await CommonServices.fetchDynamicApi(budgetsnapshoturl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            bo = tagObjsJson.map((tagJson) => BudgetOverview.fromJson(tagJson)).toList();
            debugPrint('budgetsnapshot Count------ ${bo.length}');
            if (bo.length == 1) budgetsumsdata.value = bo.first;
          }
        } else {
          budgetsumsdata.value = null;
        }
      } else {
        budgetsumsdata.value = null;
      }
    } catch (e) {
      debugPrint('$e');
    }
    isloading.value = false;
  }

//budget details---------------------------------------------------------------
  RxList<BudgetRecord> budgetRecords = <BudgetRecord>[].obs;
  RxList<Budget> budgets = <Budget>[].obs;
  var columnList = <String>['Budgetid'].obs;
  var cellsList = <DataCell>[DataCell(Container())].obs;
  var isload = false.obs;
  var tblflag = false.obs;

  final ScrollController scrollController = ScrollController();
  var paginationInfo = PaginationInfo(
          total: ConstHelper.pageTotal,
          offset: ConstHelper.pageOffset,
          size: ConstHelper.pageSize,
          start: ConstHelper.pageStart,
          end: ConstHelper.pageEnd)
      .obs;
  var isLoadingMore = false.obs;
  bool get canLoadMore => paginationInfo.value.end < paginationInfo.value.total;

  Future<void> fetchBudgetRecords(
      {String? init, int? entityid, String? entitytype, String? searchtext, bool isLoadMore = false}) async {
    debugPrint('------------fetchBudgetRecords--------$entityid');
    try {
      setColumnsData(init);

      if (isLoadMore) {
        isLoadingMore.value = true;
      } else {
        isload.value = true;
        budgetRecords.clear();
      }
      await Future.delayed(const Duration(seconds: 1));
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityid.toString()],
        'b': ['entity_type', 'EXACT', entitytype],
      };
      if (bdgtSrchTxt.text != null && bdgtSrchTxt.text.isNotEmpty && bdgtSrchTxt.text != '') {
        dynamicMap['xy'] = ['all_columns', 'CONTAINS', bdgtSrchTxt.text];
      }
      // if (searchtext != null && searchtext != '') {
      //   dynamicMap['xy'] = ['all_columns', 'CONTAINS', searchtext];
      // }
      var filter = CommonServices.getFilter(dynamicMap,
          offset: isLoadMore ? paginationInfo.value.offset + 1 : 1, size: ConstHelper.pageSize);
      debugPrint('filter >>>>$filter');

      Map<String, dynamic>? resMap = await CommonServices.fetchDynamicApi(budgeturl, payloadObj: filter);
      if (resMap?.isNotEmpty == true) {
        final budgetResponse = BudgetResponse.fromJson(resMap!);
        if (isLoadMore) {
          budgetRecords.addAll(budgetResponse.records);
        } else {
          budgetRecords.assignAll(budgetResponse.records);
        }
        paginationInfo.value = budgetResponse.paginationInfo;
      }
    } catch (e) {
      debugPrint('$e');
      ComponentUtils.showsnackbar(heading: 'Error', text: 'Failed to fetch data');
    } finally {
      isload.value = false;
      isLoadingMore.value = false;
    }
  }

  setColumnsData(String? init) async {
    if (init == 'init') {
      columnList.clear();
      cellsList.clear();

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM')!.value ?? 'Task Number');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE')!.value ?? 'Task Type');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION')!.value ?? 'Division');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME')!.value ?? 'Sub Task Name');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET')!.value ?? 'Initial Budget');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET')!.value ?? 'Approved Budget');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST')!.value ?? 'Estimated Cost');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT')!.value ?? 'Commitment');
        cellsList.add(DataCell(Container()));
      }

      if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST') != null) {
        columnList.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST')!.value ?? 'Actual Cost');
        cellsList.add(DataCell(Container()));
      }

      debugPrint('col list>>>>>>>>>${columnList.toString()}');

      if (cellsList.isNotEmpty) {
        cellsList[0] = DataCell(Obx(() => isLoadingMore.value ? const Text('Loading...') : Container()));
      }
    }
  }

  loadEntityBudget({int? entityid, String? entitytype, String? searchtext}) async {
    debugPrint('------------loadEntityBudget--------$entityid');
    try {
      isload.value = true;
      List<Budget> bdg;
      budgets.value = <Budget>[];
      Map<String, dynamic>? resMap;
      var filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityid.toString()],
        'b': ['entity_type', 'EXACT', entitytype],
      };
      if (searchtext != null && searchtext != '') {
        dynamicMap['xy'] = ['all_columns', 'CONTAINS', searchtext];
      }
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      //}
      resMap = await CommonServices.fetchDynamicApi(budgeturl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            bdg = tagObjsJson.map((tagJson) => Budget.fromJson(tagJson)).toList();
            debugPrint('budgetsnapshot Count------ ${bdg.length}');
            budgets.value = bdg;
          }
        } else {
          budgets.value.clear();
        }
      } else {
        budgets.value.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isload.value = false;
  }
}
