import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/services/projectmanagement/project/project_search_services.dart';

import '../../../models/ta_admin/entitystatus.dart';
import '../../ta_admin/label_controller.dart';

class ProjectSearchController extends GetxController {
  static ProjectSearchController instance = Get.find();
  LabelController talabel = Get.find<LabelController>();
  //final Rx<TextEditingController> searchController = TextEditingController().obs;
  //TextEditingController searchController = TextEditingController();

  RxList<ProjectsView> projects = <ProjectsView>[].obs;
  var isLoading = false.obs;
  var tabroles = Map<dynamic, dynamic>().obs;
  var allmyfilter = 'My'.obs;
  var ongngcmpltd = 'Ongoing'.obs;
  var searchCtrl = TextEditingController();
  var statusTypeRadio = ''.obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var status = ''.obs;
  RxList<EntityStatus> projectTypeLov = <EntityStatus>[].obs;
  var projectTypeFilter = ''.obs;
  RxList<EntityStatus> sortByLov = <EntityStatus>[].obs;
  var sortBy = ''.obs;
  var filterList = [].obs; //['My', 'Ongoing'].obs;
  var filterwidgetloading = false.obs;

  @override
  void onInit() {
    super.onInit();
    debugPrint('------------onInit--------');
  }

  @override
  void onReady() {
    super.onReady();
    debugPrint('------------onReady--------');
  }

  @override
  void onClose() {
    debugPrint('------------onClose--------');
    super.onClose();
  }

  Future fetchProjects({String? searchText, String? action}) async {
    var user = UserContext.info()!.userName;
    var iscontractor = UserContext.info()!.isContractor;
    bool filterFlag = false;
    try {
      debugPrint('---fetchProjects------');
      isLoading(true);

      projects.clear();
      List<ProjectsView> proj;
      Map<String, dynamic>? resMap;
      Map<String, dynamic> dynamicMap;
      var filter;

      if (searchCtrl.text != null &&
          searchCtrl.text.isNotEmpty &&
          searchCtrl.text != '') {
        dynamicMap = {
          'a': ['all_columns', 'CONTAINS', searchCtrl.text],
          'b': ['p_user_name', 'EXACT', user!.toUpperCase()],
          'c': ['p_isContractor', 'EXACT', iscontractor!.toUpperCase()],
        };
        filterFlag = true;
      } else {
        dynamicMap = {
          'b': ['p_user_name', 'EXACT', user!.toUpperCase()],
          'c': ['p_isContractor', 'EXACT', iscontractor!.toUpperCase()],
        };
      }
      dynamicMap['mpf'] = [
        'p_myProjectsFlag',
        'EXACT',
        (allmyfilter.value == 'My' && projectTypeFilter.value != 'allprojects')
            ? 'Y'
            : 'N'
      ];
      dynamicMap['oncm'] = [
        'status_type',
        ongngcmpltd.value == 'Completed' ? 'EXACT' : 'NOTEXACT',
        'OP'
      ];
      if (status.value != null && status.value != '') {
        dynamicMap['stst'] = ['status', 'EXACT', status.value];
        filterFlag = true;
      }
      //projecttype filter
      if (projectTypeFilter.value == 'initiatives') {
        dynamicMap['ini'] = ['initiative_flag', 'EXACT', 'TRUE'];
      } else if (projectTypeFilter.value == 'masterprojects') {
        dynamicMap['imp'] = ['is_master_project', 'EXACT', 'Y'];
      } else if (projectTypeFilter.value == 'rollouts') {
        dynamicMap['et'] = ['entity_type', 'EXACT', 'MULTIPLE'];
      } else if (projectTypeFilter.value == 'regularprojects') {
        dynamicMap['ini'] = ['initiative_flag', 'NOTEXACT', 'TRUE'];
        dynamicMap['imp'] = ['is_master_project', 'NOTEXACT', 'Y'];
        dynamicMap['et'] = ['entity_type', 'NOTEXACT', 'MULTIPLE'];
      }
      // else if (projectTypeFilter.value == 'allprojects') {
      //   allmyfilter.value = 'All';
      // }
      if (projectTypeFilter.value.isNotEmpty) filterFlag = true;
      //------------------------------
      if (sortBy.value != null && sortBy.value != '') {
        filter = CommonServices.getFilter(dynamicMap,
            size: 100, sortby: sortBy.value);
      } else {
        filter = CommonServices.getFilter(dynamicMap, size: 100);
      }
      debugPrint('filter---------' + filter);
      resMap =
          await ProjectSearchServices.fetchProjectsView(payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            proj = tagObjsJson
                .map((tagJson) => ProjectsView.fromJson(tagJson))
                .toList();
            debugPrint('projectCount------ ${proj.length}');
            //projects.value = proj;
            isLoading.value = false;
            projects.addAll(proj);
          }
        } else {
          projects.clear();
        }
      } else {
        projects.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    update();
    loadFilterLovData(action);
    setFilterChips(filterFlag);
  }

  loadFilterLovData(String? action) {
    if (action == 'onload') {
      getStatusLovs();
      projectTypeLov.value.clear();
      sortByLov.value.clear();
      projectTypeLov.insert(0, EntityStatus(statusCode: '', status: 'Select'));
      sortByLov.insert(0, EntityStatus(statusCode: '', status: 'Select'));
      //setting project type lov list--------------------------------------------------------------------------
      if (talabel
              .get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_ALLPROJECTS') !=
          null)
        projectTypeLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_ALLPROJECTS')
                    ?.value ??
                'allprojects',
            statusCode: 'allprojects'));
      if (talabel
              .get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_REGPROJECTS') !=
          null)
        projectTypeLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_REGPROJECTS')
                    ?.value ??
                'regularprojects',
            statusCode: 'regularprojects'));

      if (talabel
              .get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_INITIATIVES') !=
          null)
        projectTypeLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_INITIATIVES')
                    ?.value ??
                'initiatives',
            statusCode: 'initiatives'));

      if (talabel.get(
              'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_MASTERPROJECTS') !=
          null)
        projectTypeLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_MASTERPROJECTS')
                    ?.value ??
                'masterprojects',
            statusCode: 'masterprojects'));

      if (talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_ROLLOUTS') !=
          null)
        projectTypeLov.value.add(EntityStatus(
            status: talabel
                    .get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE_ROLLOUTS')
                    ?.value ??
                'rollouts',
            statusCode: 'rollouts'));

      //setting sort By lov list--------------------------------------------------------------------------
      if (talabel
              .get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTID') !=
          null)
        sortByLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTID')
                    ?.value ??
                'Project Id',
            statusCode: 'Project_Id'));
      if (talabel.get(
              'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTNUMBER') !=
          null)
        sortByLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTNUMBER')
                    ?.value ??
                'Project Number',
            statusCode: 'Project_Number'));

      if (talabel.get(
              'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTNAME') !=
          null)
        sortByLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTNAME')
                    ?.value ??
                'Project Name',
            statusCode: 'Project_Name'));

      if (talabel.get(
              'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTSTATUS') !=
          null)
        sortByLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTSTATUS')
                    ?.value ??
                'Status',
            statusCode: 'Status'));

      if (talabel
              .get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_ENTITYTYPE') !=
          null)
        sortByLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_ENTITYTYPE')
                    ?.value ??
                'Entity Type',
            statusCode: 'Entity_Type'));

      if (talabel.get(
              'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTTYPE') !=
          null)
        sortByLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_PROJECTTYPE')
                    ?.value ??
                'Project Type',
            statusCode: 'Project_Type'));

      if (talabel.get(
              'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_OWNERSHIPTYPE') !=
          null)
        sortByLov.value.add(EntityStatus(
            status: talabel
                    .get(
                        'TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY_OWNERSHIPTYPE')
                    ?.value ??
                'Ownership Type',
            statusCode: 'Ownership_Type'));
    }

    // projectTypeLov.value.forEach((e) {
    //   debugPrint('type data>>>> ${e.status}-- ${e.statusCode}');
    // });
  }

  Future getStatusLovs() async {
    try {
      statuslov.value = await CommonServices.getStatuses('PROJECT');
    } catch (e) {
      debugPrint('getStatusLovs>>>>>>>$e');
    }
  }

  setFilterChips(bool flag) {
    Future.delayed(const Duration(seconds: 0), () {
      filterList.clear();
      //filterList.add(allmyfilter.value);

      filterList.add(GestureDetector(
        child: Chip(
          label: Text(isLoading.value ? '' : allmyfilter.value),
        ),
        onTap: () async {
          if (isLoading.value) {
            debugPrint('------');
          } else {
            if (allmyfilter.value == 'My') {
              allmyfilter.value = 'All';
            } else if (allmyfilter.value == 'All') {
              allmyfilter.value = 'My';
            }

            await fetchProjects();
          }
        },
      ));

      filterList.add(GestureDetector(
        child: Chip(
          label: Text(isLoading.value ? '' : ongngcmpltd.value),
        ),
        onTap: () async {
          if (isLoading.value) {
            debugPrint('------');
          } else {
            if (ongngcmpltd.value == 'Ongoing') {
              ongngcmpltd.value = 'Completed';
            } else if (ongngcmpltd.value == 'Completed') {
              ongngcmpltd.value = 'Ongoing';
            }

            await fetchProjects();
          }
        },
      ));

      if (flag) {
        if (searchCtrl.text != null &&
            searchCtrl.text.isNotEmpty &&
            searchCtrl.text != '') {
          filterList.add(Chip(
            avatar: const Icon(Icons.search, size: 20),
            label: Text(searchCtrl.text),
            onDeleted: () async {
              searchCtrl.text = '';
              await fetchProjects();
            },
          ));
        }
        if (projectTypeFilter.value != null && projectTypeFilter.value != '') {
          var projType = projectTypeLov
                  .firstWhere(
                      (EntityStatus s) =>
                          s.statusCode == projectTypeFilter.value,
                      orElse: () => EntityStatus(status: ''))
                  .status ??
              '';

          filterList.add(Chip(
            avatar: const Icon(Icons.bookmark, size: 20),
            label: Text(projType),
            onDeleted: () async {
              projectTypeFilter.value = '';
              await fetchProjects();
            },
          ));
        }

        if (status.value != null && status.value != '') {
          var statusname = statuslov
                  .firstWhere((EntityStatus s) => s.statusCode == status.value,
                      orElse: () => EntityStatus(status: ''))
                  .status ??
              '';

          filterList.add(Chip(
            avatar: const Icon(Icons.flag, size: 20),
            label: Text(statusname),
            onDeleted: () async {
              status.value = '';
              await fetchProjects();
            },
          ));
        }
      }
    });
  }

  Future<String> getUserEntityEditAccess(
      {required String entitytype,
      required String entityid,
      String? roFlag}) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    Map<dynamic, dynamic>? vmap;
    String res = 'view';
    tabroles.value = Map<dynamic, dynamic>();
    try {
      vmap = await CommonServices.userEntityEditAccessService(
          entityid: entityid, entitytype: entitytype);
      debugPrint('getUserEntityEditAccess >>>>>>>' + roFlag.toString());
      if (vmap != null) {
        tabroles.value = vmap;
        if (vmap['result'] != null &&
            vmap['result'] == 'Y' &&
            vmap['role_flag'] != null &&
            vmap['role_flag'] == 'Y')
          res = 'Y';
        else if (vmap['result'] != null && vmap['result'] == 'CN')
          res = 'error';

        if (res != 'error')
          res = (res?.toUpperCase() == 'Y' &&
                  (roFlag?.toUpperCase() == 'N' || roFlag == null))
              ? 'edit'
              : 'view';
      }
    } catch (e) {
      debugPrint('getUserEntityEditAccess Exception >>>>>>>>>>>$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    debugPrint('getUserEntityEditAccess >>>>>>>' + res);
    return res;
  }
}
