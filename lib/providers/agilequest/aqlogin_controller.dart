import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/agilequest/auth/aq_languagecodes_data.dart';
import 'package:tangoworkplace/screens/agilequest/home/<USER>';
import 'package:tangoworkplace/services/agilequest/aq_api_client.dart';

import '../../services/agilequest/backendData/response/aq_language_tags_response.dart';
import '../../utils/labels/dbdata_utils.dart';
import '../ta_admin/label_controller.dart';
import 'aqhome_controller.dart';

class AqLoginController extends GetxController {
  AqHomeController aqHomeCtrl = Get.find<AqHomeController>();
  static const int TIME_OUT_DURATION = 240;

  var username = ''.obs;
  var password = ''.obs;
  var isFormLoading = false.obs;
  var aqsigninerror = ''.obs;

  Future aqApploginSetup() async {
    isFormLoading.value = true;
    var aqauthtoken = await SharedPrefUtils.readPrefStr(ConstHelper.aqusertoken);

    if (aqauthtoken != null && aqauthtoken != '') {
      isFormLoading.value = false;
      Get.to(AqHome());
    } else {
      try {
        await AqApiClient.loginApp();
        username.value = await SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
        password.value = '';
        // username.value = '<EMAIL>';
        // password.value = 'Thth@123';
        //username.value = '<EMAIL>';
        //password.value = 'Welcome123!';
      } finally {
        isFormLoading.value = false;
      }
    }
  }

  Future aqUserLoginSetup() async {
    aqsigninerror.value = '';
    bool credFlag = true;
    ProgressUtil.showLoaderDialog(Get.context!);

    AqHomeController aqHomeCtrl = Get.find<AqHomeController>();
    try {
      final userData = await AqApiClient.loginUser(username.value, password.value);
      aqHomeCtrl.aqUserData.value = userData.userData;

      aqHomeCtrl.userRules.value = userData.userData.userAuthRules ?? [];
      //aqHomeCtrl.aqAuthRules.value = userData.customRules;
      credFlag = false;
    } catch (e) {
      debugPrint('aqUserLoginSetup>>>>>>>>>>$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }

    debugPrint('user token -----------${aqHomeCtrl.aqUserData.value.authToken}');

    if (credFlag) {
      aqsigninerror.value = 'Authorization Error..';
    } else {
      getApplicationProperties();
      getAqLanguageCodes();
      getAqCurrencyType();
      Get.to(() => AqHome());
    }
  }

  Future getApplicationProperties() async {
    try {
      debugPrint('---getApplicationProperties------');

      var applicationPropertiesResponse = await AqApiClient.appProperties();
      aqHomeCtrl.aqAppProperties.clear();

      if (applicationPropertiesResponse.mappings.isNotEmpty) {
        aqHomeCtrl.aqAppProperties.addAll(applicationPropertiesResponse.mappings);
      } else {}
    } catch (e) {
      debugPrint('getApplicationProperties>>>>>>>$e');
    } finally {}
  }

  Future getAqLanguageCodes() async {
    try {
      debugPrint('---getAaLanguageCodes------');

      AqLanguageTagsResponse languageTagsResponse = await AqApiClient.languageTags(
        (aqHomeCtrl.aqUserData.value.sysidLanguage ?? 1).toString(),
      );

      if (languageTagsResponse.languageTags.isNotEmpty) {
        //await DBDataProvider.db.loadaqlangcodestodb(languageTagsResponse.mappings);
        LabelController talabel = Get.find<LabelController>();
        talabel.aqLangcodes.addAll(languageTagsResponse.languageTags);
        //String ss = await talabel.getAqLabel(key: 'aq.mobile.navigation.menu.manageReservations', defaultTxt: 'xyz');
        // AqLanguageCodesData? ss = talabel.getAq(key: 'aq.mobile.navigation.menu.manageReservations', defaultTxt: 'xyz');
        //debugPrint('ss>>>>>${ss?.tag}');
      } else {}
    } catch (e) {
      debugPrint('AqLanguageCodes>>>>>>>$e');
    } finally {}
  }

  Future getAqCurrencyType() async {
    try {
      debugPrint('---getAaLanguageCodes------');
      var currencyTypesResponse = await AqApiClient.currencyTypes();

      aqHomeCtrl.aqCurrencyTypes.clear();

      if (currencyTypesResponse.types.isNotEmpty) {
        aqHomeCtrl.aqCurrencyTypes.addAll(currencyTypesResponse.types);
      } else {}
    } catch (e) {
      debugPrint('getAqCurrencyType>>>>>>>$e');
    } finally {}
  }
}
