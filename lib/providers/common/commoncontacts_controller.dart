import 'dart:core';
import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/services/common/entitycomments_services.dart';

import '../../models/common/contacts/commoncontactsdata.dart';

class CommonContactsController extends GetxController {
  static CommonContactsController instance = Get.find();
  dynamic args = Get.arguments;

  RxList<CommonContactsData> contacts = <CommonContactsData>[].obs;
  var isLoading = false.obs;
  var addCommentCtrl = TextEditingController();
  var commentType = ''.obs;

  Future getContactsData({String? searchText, int? entityid, String? entitytype}) async {
    debugPrint('------------getContactsData--------');
    contacts.clear();
    isLoading.value = true;
    List<CommonContactsData> cntctdata;
    var payloadObj;

    Map<String, dynamic> dynamicMap = {
      'a': ['entity_id', 'EXACT', entityid.toString()],
      'b': ['entity_type', 'EXACT', entitytype],
    };
    if (searchText != null) {
      dynamicMap['d'] = ['all_columns', 'CONTAINS', searchText];
    }

    payloadObj = CommonServices.getFilter(dynamicMap);
    try {
      var resMap = await CommonServices.fetchDynamicApi(entitycontactsurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            cntctdata = tagObjsJson.map((tagJson) => CommonContactsData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${cntctdata.length}');
            contacts.addAll(cntctdata);
          }
        } else {
          contacts.clear();
        }
      } else {
        contacts.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isLoading.value = false;
    }
  }
}
