import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_attendee.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_followup.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_minute.dart';
import 'package:tangoworkplace/models/common/utils/suppliersitecontact.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common/meetingminutes_services.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../models/common/utils/contact.dart';
import '../../../models/common/utils/systemuser.dart';
import '../../../models/lookup_values.dart';
import '../../../utils/constvariables.dart';
import '../../../utils/preferences_utils.dart';

class MeetingMinutesController extends GetxController {
  var isLoading = false.obs;
  RxList<MeetingMinute> meetinminutesdata = <MeetingMinute>[].obs;
  Rx<MeetingMinute> meetingminute = MeetingMinute().obs;
  var isdetailsloading = false.obs;
  var mm_mode = ''.obs;
  RxList<LookupValues> meetingTypelov = <LookupValues>[].obs;

  var labelsloading = false.obs;
  Future getlabels(LabelController talabel) async {
    try {
      await talabel.getlabels('TMCMOBILE_COMMON_MEETINGMINUTES', 'entitymeetingminutes');
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future loadMeetingMinutesData({int? entityid, String? entitytype}) async {
    debugPrint('------------loadMeetingMinutesData--------$entityid');
    isLoading(true);
    List<MeetingMinute> mmRec;
    meetinminutesdata.clear();
    Map<String, dynamic>? resMap;

    //var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);

    Map<String, dynamic> dynamicMap = {
      'a': ['entity_id', 'EXACT', entityid.toString()],
      'b': ['entity_type', 'EXACT', entitytype.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(meetingminutesurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            mmRec = tagObjsJson.map((tagJson) => MeetingMinute.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${mmRec.length}');

            meetinminutesdata.addAll(mmRec);
          }
        } else {
          meetinminutesdata.clear();
        }
      } else {
        meetinminutesdata.clear();
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      isLoading(false);
    }
  }

  var meetingDateCtrl = TextEditingController();
  var fromTimeCtrl = TextEditingController();
  var toTimeCtrl = TextEditingController();
  var distributionDateCtrl = TextEditingController();

  Future setCurrentRow() async {
    debugPrint('meetingminute data    ${jsonEncode(meetingminute.value)}');
    if (mm_mode.value == 'create') {
      fromTimeCtrl.text = '';
      toTimeCtrl.text = '';
      meetingDateCtrl.text = '';
      distributionDateCtrl.text = '';
    } else {
      fromTimeCtrl.text = meetingminute.value.startTime ?? '';
      toTimeCtrl.text = meetingminute.value.endTime ?? '';
      meetingDateCtrl.text = ComponentUtils.dateToString(meetingminute.value.meetingDate);
      distributionDateCtrl.text = ComponentUtils.dateToString(meetingminute.value.distributionDate);
    }
    await getLovItems();
  }

  Future onSaveMeetingMinute({var action, var projectname}) async {
    debugPrint('meetingminute data    ${jsonEncode(meetingminute.value)}');
    ProgressUtil.showLoaderDialog(Get.context!);
    bool flag = false;
    try {
      var result;
      debugPrint('action    $action');

      if (action != null) {
        meetingminute.value.action = action;
        meetingminute.value.entityName = projectname;
        if (action == 'sendemail') {
          meetingminute.value.hostnm = await SharedPrefUtils.readPrefStr(ConstHelper.hostNameVar);
        }
      }

      result = await MeetingMinutesServices.saveMeetingMinute(meetingminute.value, mm_mode.value);
      debugPrint('result    $result');

      var status = result['statusmessage'] as String?;
      if (status == 'SUCCESS') {
        meetingminute.value.meetingId = result['id'];
        if (mm_mode.value == 'create') {
          Map? rparams = result['params'] as Map?;
          meetingminute.value.docFolderId =
              rparams != null ? (rparams['docParentFoldId'] != null ? int.tryParse(rparams['docParentFoldId']) : null) : null;
        }
        debugPrint('docFolderId    ${meetingminute.value.docFolderId}');
        mm_mode.value = 'edit';

        flag = true;
      }
      // else {
      //   ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      // }
    } catch (e) {
      debugPrint('${e}');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
      if (flag) {
        if (action == 'sendemail') {
          ComponentUtils.showpopup(msg: "Email sent to Attendees...");
        } else {
          ComponentUtils.showsnackbar(text: "Data saved successfully...");
        }
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      }
    }
  }

  RxList<MeetingAttendee> meetingattendeesdata = <MeetingAttendee>[].obs;
  var attendeesloading = false.obs;

  Future loadMeetingAttendeesData({int? meetingid}) async {
    debugPrint('------------loadMeetingAttendeesData--------$meetingid');
    attendeesloading.value = true;
    List<MeetingAttendee> maRec;
    meetingattendeesdata.clear();
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['meeting_id', 'EXACT', meetingid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(meetingattendeesurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            maRec = tagObjsJson.map((tagJson) => MeetingAttendee.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${maRec.length}');

            meetingattendeesdata.addAll(maRec);
          }
        } else {
          meetingattendeesdata.clear();
        }
      } else {
        meetingattendeesdata.clear();
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      attendeesloading.value = false;
    }
  }

  var isContactsLoading = false.obs;
  var isSupplierSiteContactLoading = false.obs;
  RxList<Contact> contacts = <Contact>[].obs;
  RxList<SupplierSiteContact> suppliersitecontacts = <SupplierSiteContact>[].obs;

  Future fetchContacts(int? entityid, String? entitytype) async {
    isContactsLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityid?.toString()],
        'b': ['entity_type', 'EXACT', entitytype],
      };
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(contactsurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        contacts.value = tagObjsJson.map((tagJson) => Contact.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchContatcs>>>>>>>' + '$e');
    } finally {
      isContactsLoading.value = false;
    }
  }

  Future fetchSupplierSiteContacts({var searchText}) async {
    isSupplierSiteContactLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      var filter;
      if (searchText != null) {
        Map<String, dynamic> dynamicMap = {
          'a': ['search_text', 'CONTAINS', searchText],
          // 'b': ['entity_type', 'EXACT', entitytype],
        };
        filter = CommonServices.getFilter(dynamicMap);
      }
      resMap = await CommonServices.fetchDynamicApi(
        suppliersitecontactsurl,
        payloadObj: filter,
      );
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        suppliersitecontacts.value = tagObjsJson.map((tagJson) => SupplierSiteContact.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchSupplierSiteContacts>>>>>>>' + '$e');
    } finally {
      isSupplierSiteContactLoading.value = false;
    }
  }

  Future saveAttendees(int? meetingid) async {
    bool flag = false;
    ProgressUtil.showLoaderDialog(Get.context!);
    try {
      Map m = {
        'meetingid': meetingid,
        'attendees': meetingattendeesdata.value,
      };
      debugPrint('m----------${jsonEncode(m)}');
      var result;

      result = await MeetingMinutesServices.saveAttendees(m);
      if (result != null && result == 'success') {
        flag = true;
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);

      if (flag) {
        Get.back();
        ComponentUtils.showsnackbar(text: "Attendees Data saved successfully...");
      } else
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }

  var isFollowupsLoading = false.obs;
  RxList<MeetingFollowup> followupsdata = <MeetingFollowup>[].obs;

  RxList<LookupValues> fuissueTypelov = <LookupValues>[].obs;
  RxList<LookupValues> fuRiskLevellov = <LookupValues>[].obs;
  RxList<LookupValues> yesnolov = <LookupValues>[].obs;

  Rx<MeetingFollowup> meetingFollowup = MeetingFollowup().obs;
  var fuFollowupDate = TextEditingController();

  Future fetchMeetingFollowups(int? meetingid) async {
    isFollowupsLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['meeting_id', 'EXACT', meetingid?.toString()],
      };
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(meetingfollowupsurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        followupsdata.value = tagObjsJson.map((tagJson) => MeetingFollowup.fromJson(tagJson)).toList();
      }
    } catch (e) {
      followupsdata.value.clear();
      debugPrint('fetchDynamicApi fetchMeetingFollowups>>>>>>>' + '$e');
    } finally {
      isFollowupsLoading.value = false;
    }
  }

  var fetchlovs = true.obs;
  Future getLovItems() async {
    if (fetchlovs.value) {
      meetingTypelov.value.clear();
      fuRiskLevellov.value.clear();
      fuissueTypelov.value.clear();
      yesnolov.value.clear();
      try {
        var jsonObj = await CommonServices.fetchMultiLovs('MEETING_TYPE,ISSUE_TYPE,FOLLOWUPS_RISKLEVEL,YES/NO');
        if (jsonObj != null) {
          debugPrint('jsonObj ----- $jsonObj');
          var mtt = jsonObj['MEETING_TYPE'] as List?;
          var fit = jsonObj['ISSUE_TYPE'] as List?;
          var ffr = jsonObj['FOLLOWUPS_RISKLEVEL'] as List?;
          var myn = jsonObj['YES/NO'] as List?;

          if ((meetingTypelov.value.isEmpty || meetingTypelov.value == null) && mtt != null)
            meetingTypelov.value = mtt.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
          meetingTypelov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

          if ((fuRiskLevellov.value.isEmpty || fuRiskLevellov.value == null) && ffr != null)
            fuRiskLevellov.value = ffr.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
          fuRiskLevellov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

          if ((fuissueTypelov.value.isEmpty || fuissueTypelov.value == null) && fit != null)
            fuissueTypelov.value = fit.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
          fuissueTypelov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

          if ((yesnolov.value.isEmpty || yesnolov.value == null) && myn != null)
            yesnolov.value = myn.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
          yesnolov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

          fetchlovs.value = false;
        }
      } catch (error) {
        debugPrint('$error');
      }
    }
  }

  Future onSaveFollowup(int? meetingid) async {
    meetingFollowup.value.meetingId = meetingid;
    debugPrint('followup data    ${jsonEncode(meetingFollowup.value)}');
    ProgressUtil.showLoaderDialog(Get.context!);
    bool flag = false;

    try {
      var result;

      result = await MeetingMinutesServices.saveFollowup(meetingFollowup.value);
      if (result != null && result > 0) {
        followupsdata.insert(0, meetingFollowup.value);
        flag = true;
      }
    } catch (e) {
      debugPrint('${e}');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
      Get.back();
      if (flag)
        ComponentUtils.showsnackbar(text: "Data saved successfully...");
      else
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }
}
