import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/common/demographic.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../../utils/request_api_utils.dart';

class EntityDemographicsController extends GetxController {
  // var dataloaded = false.obs;
  var isloading = false.obs;
  var labelsloading = false.obs;
  var rowcnt = 0.obs;
  var clmnCnt = 0.obs;

  RxList<Demographic> dmglist = <Demographic>[].obs;

  RxList<String> tColumns = <String>[].obs;
  var cbsaclass = ''.obs;
  var storeclass = ''.obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_TARGET_DEMOGRAPHIC', 'target_demographic');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  Future? cleardata() {
    rowcnt.value = 0;
    clmnCnt.value = 0;
    dmglist.value.clear();
    tColumns.value.clear();
    cbsaclass.value = '';
    storeclass.value = '';
  }

  Future loadEntityDemographics(String entitytype, String entityid) async {
    String apival = entitydemographicurl + '?entitytype=' + entitytype + '&entityid=' + entityid;
    isloading.value = true;
    List<Demographic> tList;
    try {
      // if (!dataloaded.value) {
      await cleardata();
      var resMap = await ApiService.get(apival);
      resMap = jsonDecode(resMap);
      debugPrint('$resMap');
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var dmgRec = resMap['demographicdata']['records'] as List?;

          if (dmgRec != null) {
            tList = dmgRec.map((tagJson) => Demographic.fromJson(tagJson)).toList();
            debugPrint('dmg count   ${tList.length}');
            dmglist.value = tList;

            if (tList != null && tList.length > 0) {
              var d = tList.first;
              if (d.dbColumn == 'TA_TYPE_DESC') {
                tColumns.value.add('Demographics');
                var rList = d.rows!;
                rList.forEach((e) {
                  tColumns.value.add(e ?? '');
                });
                dmglist.value.removeAt(0);
              }
            }
            var oiRec = resMap['demographicdata']['otherinfo'] as Map?;
            cbsaclass.value = oiRec != null && oiRec['cbsa_class'] != null ? oiRec['cbsa_class'] : '';
            storeclass.value = oiRec != null && oiRec['store_class'] != null ? oiRec['store_class'] : '';
          }
        }
        // }
      }
      //var recMap =
    } finally {
      //dataloaded.value = true;
      isloading.value = false;
    }
  }
}
