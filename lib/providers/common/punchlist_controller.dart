import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/punchlist.dart';
import 'package:tangoworkplace/models/common/template.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common/punchlist_services.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../../common/component_utils.dart';
import '../../models/lookup_values.dart';

class EntityPunchlistController extends GetxController {
  static EntityPunchlistController instance = Get.find();
  dynamic args = Get.arguments;
  var listflag = true.obs;
  var templateflag = false.obs;
  RxList<Punchlist> punchlistdata = <Punchlist>[].obs;
  var isloading = false.obs;
  var allnotapplicableflag = false.obs;
  var allcextattr1 = false.obs;
  RxList<Template> templatelov = <Template>[].obs;
  var selectedtemplate = ''.obs;
  var generatebtnbinding = false.obs;
  var completedFilter = 'ALL'.obs;
  final searchTextCtrl = TextEditingController();
  var searchflag = false.obs;

  final _templateCtrl = TextEditingController();
  var punchlistScrollCtrl = ScrollController();

  var labelsloading = false.obs;
  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_COMMON_PUNCHLIST', 'punchlist', filtertype: 'CONTAINS');
      await getLovItems();
    } catch (e) {
      debugPrint('$e');
    }

    labelsloading.value = false;
  }

  gettemplateslov({String? entityType, int? entityId}) async {
    var filter;
    List<Template> temprec;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_type', 'EXACT', entityType],
      };

      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      //}
      isloading.value = true;
      resMap = await PunchlistServices.fetchDynamicApi(payloadObj: filter, url: templateurl);

      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            temprec = tagObjsJson.map((tagJson) => Template.fromJson(tagJson)).toList();
            debugPrint('Punchlist Count------ ${temprec.length}');

            templatelov.value = temprec;
          }
        }
      }
    } catch (e) {
      debugPrint('$e');
    }
    isloading.value = false;
  }

  Future? indexingCurrentRecord(int? id) {
    final index = punchlistdata.value.indexWhere((element) => element.scopePunchListId == id);
    if (index >= 0) {
      debugPrint('indexingCurrentRecord--------$index');
      ComponentUtils.scrollToIndex(index, punchlistScrollCtrl);
    }
  }

  Future<void> loadEntityPunchlist(String? entityType, int? entityId, {String? searchText, String? statusfilter}) async {
    debugPrint('------------loadEntityPunchlist--------$entityType');
    try {
      isloading(true);
      punchlistdata.clear();
      List<Punchlist> punchlistRec;
      Map<String, dynamic>? resMap;
      var filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityId.toString()],
        'b': ['entity_type', 'EXACT', entityType],
      };
      if (statusfilter == 'Not Completed') {
        dynamicMap['c'] = ['c_ext_attr1', 'EXACT', 'FALSE'];
      }
      if (statusfilter == 'N/A') {
        dynamicMap['d'] = ['not_applicable_flag', 'EXACT', 'TRUE'];
      } else {
        dynamicMap['d'] = ['not_applicable_flag', 'EXACT', 'FALSE'];
      }
      if (searchText != null) {
        dynamicMap['g'] = ['scope_punch_name', 'CONTAINS', searchText];
      }
      filter = CommonServices.getFilter(dynamicMap, size: 600);
      debugPrint('filter---------' + filter);
      //}
      resMap = await PunchlistServices.fetchDynamicApi(payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            punchlistRec = tagObjsJson.map((tagJson) => Punchlist.fromJson(tagJson)).toList();
            debugPrint('Punchlist Count------ ${punchlistRec.length}');

            if ((punchlistRec.isEmpty || punchlistRec.length < 1) && templateflag.isFalse) {
              gettemplateslov(entityType: entityType);
              showtemplateform();
            } else {
              listflag.value = true;
              templateflag.value = true;
            }
            punchlistdata.addAll(punchlistRec);
          }
        } else {
          punchlistdata.clear();
        }
      } else {
        punchlistdata.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();

    if (statusfilter == null && searchText == null) {
      searchflag.value = false;
      searchTextCtrl.text = '';
      completedFilter.value = 'ALL';
    }
    return;
  }

  showtemplateform() {
    listflag.value = false;
  }

  generateItemsFromTemplate({int? entityId, String? entityType}) async {
    debugPrint('selectedtemplate        ${selectedtemplate.value}');
    try {
      Map m = {
        'entityId': entityId,
        'templateId': selectedtemplate.value,
      };

      ProgressUtil.showLoaderDialog(Get.context!);
      var status = await PunchlistServices.addPunchlistItem(m, url: generateitemsurl);
      Get.back();
      if (status['res'] != null && status['res'] == 'success') {
        loadEntityPunchlist(entityType, entityId);
      } else {
        ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      }
    } catch (e) {
      ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      debugPrint('$e');
    }
  }

  //---------Add item -------------------

  var addItemSeqCtrl = TextEditingController();
  var addItemNameCtrl = TextEditingController();
  var addDivisionCtrl = TextEditingController();
  var addItemTypeCtrl = TextEditingController();
  var addDescrpitionCtrl = TextEditingController();

  clearaddfieldsvalue() {
    addItemSeqCtrl.text = '';
    addDescrpitionCtrl.text = '';
    addItemNameCtrl.text = '';
    addDivisionCtrl.text = '';
    addItemTypeCtrl.text = '';
    addDescrpitionCtrl.text = '';
  }

  addPunchlistItem({String? entitytype, int? entityid}) async {
    try {
      Map m = {
        'entityid': entityid,
        'entitytype': entitytype,
        'lineitemseq': addItemSeqCtrl.text,
        'punchlistname': addItemNameCtrl.text,
        'division': addDivisionCtrl.text,
        'itemtype': addItemTypeCtrl.text,
        'description': addDescrpitionCtrl.text
      };
      clearaddfieldsvalue();
      Get.back();
      ProgressUtil.showLoaderDialog(Get.context!);
      Map<String, dynamic> rMap = await PunchlistServices.addPunchlistItem(m);
      int? id = 0;
      var res = rMap['res'] as String?;
      if (res == 'success') {
        Get.back();
        id = rMap['id'];
        await loadEntityPunchlist(entitytype, entityid);
        Future.delayed(Duration(seconds: 1), () async {
          await indexingCurrentRecord(id);
        });
      } else {
        Get.back();
      }
      ComponentUtils.showsnackbar(text: rMap['msg']);
    } catch (e) {
      Get.back();
      ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      debugPrint('$e');
    }
  }

  applyAllFlagActions({String? entitytype, int? entityid}) async {
    try {
      Map m = {
        'entityid': entityid,
        'notApplicableFlag': allnotapplicableflag.value,
        'cextattr1': allcextattr1.value,
      };
      allnotapplicableflag.value = false;
      allcextattr1.value = false;
      Get.back();
      ProgressUtil.showLoaderDialog(Get.context!);
      var status = await PunchlistServices.applyAllFlagActions(m);
      if (status == 'success') {
        Get.back();
        loadEntityPunchlist(entitytype, entityid);
      } else {
        Get.back();
        ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      }
    } catch (e) {
      Get.back();
      ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      debugPrint('$e');
    }
  }

  //-----------------------------

  setCurrentRow(Punchlist p) async {
    cextattr1.value = (p.cExtAttr1 == 'TRUE' || p.cExtAttr1 == 'true') ? true : false;
    notapplicableflag.value = (p.notApplicableFlag == 'TRUE' || p.notApplicableFlag == 'true') ? true : false;
    scopepunchName.value = p.scopePunchName ?? '';
  }

  var fetchlovs = true.obs;
  RxList<LookupValues> cextlov1lov = <LookupValues>[].obs;
  Future getLovItems() async {
    if (fetchlovs.value) {
      cextlov1lov.value.clear();

      try {
        var jsonObj = await CommonServices.fetchMultiLovs('SCOPEPUNCHLIST_LOV1');
        if (jsonObj != null) {
          debugPrint('jsonObj ----- $jsonObj');
          var cxt1 = jsonObj['SCOPEPUNCHLIST_LOV1'] as List?;

          if ((cextlov1lov.value.isEmpty || cextlov1lov.value == null) && cxt1 != null)
            cextlov1lov.value = cxt1.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
          cextlov1lov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

          fetchlovs.value = false;
        }
      } catch (error) {
        debugPrint('$error');
      }
    }
  }

  getTemplatedata() {}
  var duedateCtrl = TextEditingController();
  var comments = ''.obs;
  var scopepunchName = ''.obs;
  var description = ''.obs;
  var assignee = ''.obs;
  var duedate = ''.obs;
  var notapplicableflag = false.obs;
  var cextattr1 = false.obs;
  var cextlov1 = 'Select'.obs;
  final Rx<Punchlist> punchRow = Punchlist().obs;
  var operStatue = 'no'.obs;

  // setCurrentRow(Punchlist p) {
  //   punchRow.value = p;
  // }

  onDetFormsave(Punchlist p) async {
    debugPrint('scopePunchListId --------' + p.scopePunchListId.toString());
    debugPrint('entityType --------' + p.entityType.toString());
    debugPrint('entityId --------' + p.entityId.toString());

    debugPrint('scopepunchName --------' + scopepunchName.value);

    Map m = {
      'scopePunchListId': p.scopePunchListId,
      'scopepunchname': scopepunchName.value ?? '',
      'entityType': p.entityType,
      'entityId': p.entityId,
      'description': description.value ?? '',
      'assignee': assignee.value ?? '',
      'dueDate': duedate.value ?? '',
      'comments': comments.value ?? '',
      'notApplicableFlag': notapplicableflag.value,
      'cExtAttr1': cextattr1.value,
      'cExtLov1': p.cExtLov1 ?? '',
    };

    String result;

    result = await PunchlistServices.updatePunchlistDetDataImpl(m);
    if (result == 'success') {
      Get.back();
      await loadEntityPunchlist(p.entityType, p.entityId);
      debugPrint('save--------${p.lineItemSeq}');
      Future.delayed(Duration(seconds: 1), () async {
        await indexingCurrentRecord(p.scopePunchListId);
      });

      ComponentUtils.showsnackbar(text: "Data saved successfully...");
    } else {
      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }

  onDeleteDetRecord({int? id, String? entitytype, int? entityid}) async {
    try {
      debugPrint('punchlist id --------' + id.toString());

      String result;
      Get.back();
      ProgressUtil.showLoaderDialog(Get.context!);
      result = await PunchlistServices.deletePunchlistDetDataImpl(id, entityid);
      Get.back();
      if (result == 'success') {
        Get.back();
        loadEntityPunchlist(entitytype, entityid);
        ComponentUtils.showsnackbar(text: "Data deleted successfully...");
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while deleting data...");
      }
    } catch (e) {
      Get.back();

      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while deleting data...");
    }
  }
}
