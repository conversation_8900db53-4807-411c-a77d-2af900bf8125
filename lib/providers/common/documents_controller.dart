import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/common/document.dart';
import 'package:tangoworkplace/services/common/documents_services.dart';

class DocumentController extends GetxController {
  static DocumentController instance = Get.find();
  RxList<Document> doclist = <Document>[].obs;
  var isLoading = false.obs;
  var rootfolds = [].obs;
  var currentfoldId = 0.obs;
  var listviewflag = false.obs;
  var docScrollCtrl = ScrollController();

  loadDocuments({int? folderid, Document? doc, bool? loadmore, String? opertype}) async {
    debugPrint('------------loadDocuments--------');

    try {
      isLoading(true);
      doclist.clear();
      List<Document> docs;
      Map<String, dynamic>? resMap;
      int? foldid = folderid;
      if (folderid == null) {
        foldid = (opertype == 'fileuploads') ? currentfoldId.value : doc!.id;
        if (opertype != 'fileuploads') {
          foldernavigation(doc!.parentFolderId);
        }
        if (rootfolds != null && rootfolds.isNotEmpty) {
          Document dd = Document(docType: 'SUB', id: rootfolds.last);
          doclist.add(dd);
          // foldid = doc.parentFolderId;
        }
      } else {
        rootfolds.clear();
      }
      currentfoldId.value = foldid!;
      resMap = await DocumentsServices.fetchDocuments(foldid);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            docs = tagObjsJson.map((tagJson) => Document.fromJson(tagJson)).toList();
            debugPrint('Documents Count------ ${docs.length}');
            // if (docs != null && docs.isNotEmpty) {
            //   docs.sort((a, b) => a.docType.compareTo(b.docType));
            //   doclist.addAll(docs.reversed);
            // } else {
            //   doclist.addAll(docs);
            // }
            doclist.addAll(docs);
          }
        } else {
          doclist.clear();
        }
      } else {
        doclist.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    update();
    isLoading(false);
  }

  foldernavigation(int? subfolderid) {
    debugPrint('rootfolds----------$rootfolds');
    if (subfolderid != null) {
      if (rootfolds.contains(subfolderid)) {
        rootfolds.remove(subfolderid);
      } else {
        rootfolds.add(subfolderid);
      }
    } else {
      rootfolds.removeLast();
    }

    debugPrint('rootfolds----------$rootfolds');
  }

  uploadFiles(List<File> files, String? entityType, int? entityId) async {
    try {
      print('Inside upload $entityId');
      File f;
      List<String> paths = [];
      for (var i = 0; i < files.length; i++) {
        f = files[i];
        debugPrint('f    ${f}');
        paths.add(f.path);
      }

      String res =
          await DocumentsServices.uploadFiles(entityType: entityType, entityId: entityId, folderId: currentfoldId.value, paths: paths);
      if (res == 'success') {
        Future.delayed(const Duration(seconds: 8), () {
          loadDocuments(
            opertype: 'fileuploads',
            //folderid: currentfoldId.value
          );
          Get.back();
        });
      } else {
        Get.back();
      }
    } catch (e) {
      debugPrint('$e');
    }
  }
}
