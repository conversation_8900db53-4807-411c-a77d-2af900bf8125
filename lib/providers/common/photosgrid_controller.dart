import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tangoworkplace/common/common_import.dart';
import '../../common/component_utils.dart';
import '../../models/common/photo.dart';
import '../../services/common/pictures_services.dart';

class PhotosGridController extends GetxController {
  RxList<Photo> photos = <Photo>[].obs;
  List<int> indexesBig = [];
  Rx<bool> isLoading = false.obs;
  Rx<bool> isGrid = false.obs;
  String? extensionValue;
  RxList<int?> templist = <int?>[].obs;

  final namePhotoTextController = TextEditingController();
  final descriptionPhotoTextController = TextEditingController();
  final dateController = TextEditingController();
  final createdByController = TextEditingController();
  

  Future<void> loadPhotos(
      {required String entityType,
      required int? entityId,
      bool? loadmore}) async {
    isLoading(true);
    templist.clear();
    if (loadmore == null || !loadmore) photos.clear();
    int indexCell = 0;
    int step = 4;
    try {
      photos.clear();
      indexesBig.clear();
      List<Photo> photosUploaded =
          await PhotoServices.fetchPhotos(entityType, entityId);
      photosUploaded.forEachIndexed((index, element) {
        if (index == 0) {
          indexesBig.add(0);
        }
        if (indexCell == index) {
          indexesBig.add(index);
          indexCell += step;
          step = step == 4 ? 2 : 4;
        }
      });
      photos.addAll(photosUploaded);
    } catch (e) {
      debugPrint('$e');
    }
    isLoading(false);
  }

  void openDetails({Photo? photoToEdit}) {
    namePhotoTextController.clear();
    descriptionPhotoTextController.clear();
    dateController.clear();
    createdByController.clear();

    if (photoToEdit != null) {
      final indexOfExtension = photoToEdit.picName?.lastIndexOf('.');
      final name = indexOfExtension != -1 ? photoToEdit.picName?.substring(0, indexOfExtension) : photoToEdit.picName;
      extensionValue = indexOfExtension != -1 ? photoToEdit.picName?.substring(indexOfExtension ?? 0, (photoToEdit.picName?.length ?? 0)) : null;
      
      namePhotoTextController.text = name ?? '';
      descriptionPhotoTextController.text = photoToEdit.picDescription ?? '';
      dateController.text = photoToEdit.creationDate ?? '';
      createdByController.text = photoToEdit.createdBy ?? '';
    } else {
      namePhotoTextController.text = '';
      extensionValue = '';
      descriptionPhotoTextController.text = '';
      dateController.text = '';
      createdByController.text = '';
    }
  }

  void switchUI() {
    final changedValue = !isGrid.value;
    isGrid(changedValue);
  }

  String getPicUrl(int? picId) {
    String pictureId = picId != null ? picId.toString() : '0';
    String url = '$imageapiurl?picId=$pictureId';
    return ApiService.getRestPathurl(url);
  }

  Future<void> updatePhoto({
    int? picId,
    required String entityType,
    required int? entityId,
  }) async {
    try {
      ProgressUtil.showLoaderDialog(Get.context!);
      String? nameVal = namePhotoTextController.text.isNotEmpty
          ? '${namePhotoTextController.text}${extensionValue ?? ''}'
          : null;
      String? descriptionVal = descriptionPhotoTextController.text.isNotEmpty
          ? descriptionPhotoTextController.text
          : null;
      final res = await PhotoServices.updatePhotoData(
        picId ?? 0,
        name: nameVal,
        description: descriptionVal,
      );
      if (res == 'success') {
        await Future.delayed(const Duration(seconds: 3));
        await loadPhotos(entityType: entityType, entityId: entityId);
      }
    } catch (e) {
      debugPrint('$e');
    }
    Get.back();
  }

  Future<void> uploadPhotos({
    required List<String> photosPath,
    required String entityType,
    required int? entityId,
    XFile? photo,
  }) async {
    try {
      ProgressUtil.showLoaderDialog(Get.context!);
      String? extensionFile = photo?.path.split('.').lastOrNull ?? 'jpg';
      String? nameVal = namePhotoTextController.text.isNotEmpty
          ? '${namePhotoTextController.text}.$extensionFile'
          : null; 
      String? descriptionVal = descriptionPhotoTextController.text.isNotEmpty
          ? descriptionPhotoTextController.text
          : null;
      final res = await PhotoServices.addPhotos(
        entityType: entityType,
        entityId: entityId,
        pathFiles: photosPath,
        name: nameVal,
        description: descriptionVal,
      );
      if (res == 'success') {
        await loadPhotos(entityType: entityType, entityId: entityId);
      }
    } catch (e) {
      debugPrint('$e');
    }
    Get.back();
  }

  void markToDelete(int? picId) {
    photos.removeWhere((element) => element.picId == picId);
    if(!templist.contains(picId)) {
      templist.add(picId);
    }
  }

  Future<void> deletePhotos() async {
    if (templist.isNotEmpty && templist.isNotEmpty) {
      ProgressUtil.showLoaderDialog(Get.context!);
      try {
        String result = 'error';

        Map m = {
          'fileids': templist.join(','),
        };
       
        result = await PhotoServices.deletePhotos(m);

        templist.clear();

        if (result == 'success') {
           Get.back();
          ComponentUtils.showsnackbar(text: "Data upadated successfully...");
        } else {
           Get.back();
          ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while updating data...");
        }
      } catch (e) {
         Get.back();
        debugPrint(' saveDocuments>>>>>>>' + '$e');
      } 
    } 
  }
}
