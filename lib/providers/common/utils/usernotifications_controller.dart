import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/common/utils/adhoctasknotifuser.dart';
import 'package:tangoworkplace/models/common/utils/contact.dart';
import 'package:tangoworkplace/models/common/utils/systemuser.dart';
import 'package:tangoworkplace/providers/common/adhoctasks_controller.dart';
import 'package:tangoworkplace/services/common/adhoctasks_services.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

class UserNotificationsController extends GetxController {
  EntityAdhicTasksController adhoctaskCtrl = Get.find<EntityAdhicTasksController>();
  var isLoading = false.obs;
  var isContactsLoading = false.obs;
  var isSystemUserLoading = false.obs;
  RxList<AdhocTaskNotifUser> adhocnotifyusers = <AdhocTaskNotifUser>[].obs;
  RxList<Contact> contacts = <Contact>[].obs;
  RxList<SystemUser> systemusers = <SystemUser>[].obs;
  TextEditingController searchController = TextEditingController();

  Future fetchAdhocNotifUsers(int? adhoctaskid) async {
    isLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['adhoc_task_id', 'EXACT', adhoctaskid?.toString()],
      };
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(adhocnotifyusersurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        adhocnotifyusers.value = tagObjsJson.map((tagJson) => AdhocTaskNotifUser.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchAdhocNotifUsers>>>>>>>' + '$e');
    }
    isLoading.value = false;
  }

  Future fetchContacts(int? entityid, String? entitytype) async {
    isContactsLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityid?.toString()],
        'b': ['entity_type', 'EXACT', entitytype],
      };
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(contactsurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        contacts.value = tagObjsJson.map((tagJson) => Contact.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchContatcs>>>>>>>' + '$e');
    }
    isContactsLoading.value = false;
  }

  Future fetchSytemUsers({var searchText}) async {
    isSystemUserLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      var filter;
      if (searchText != null) {
        Map<String, dynamic> dynamicMap = {
          'a': ['search_text', 'CONTAINS', searchText],
          // 'b': ['entity_type', 'EXACT', entitytype],
        };
        filter = CommonServices.getFilter(dynamicMap);
      }
      resMap = await CommonServices.fetchDynamicApi(
        systemusersurl,
        payloadObj: filter,
      );
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        systemusers.value = tagObjsJson.map((tagJson) => SystemUser.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchSytemUsers>>>>>>>' + '$e');
    }
    isSystemUserLoading.value = false;
  }

  Future saveNotifyUsers(int? entityid, String? entitytype, int? adhoctaskid) async {
    try {
      Map m = {
        'entityid': entityid,
        'entitytype': entitytype,
        'adhoctaskid': adhoctaskid,
        'notifyusers': adhocnotifyusers.value,
      };
      debugPrint('m----------${jsonEncode(m)}');
      var result = await AdhocTaskServices.saveNotificationUsers(m);
      // if (result == 'success') {
      //   Get.back();

      //  // ComponentUtils.showsnackbar(text: "Data saved successfully...");
      // } else {
      //   ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      // }
    } catch (e) {
      debugPrint('fetchDynamicApi saveNotifyUsers>>>>>>>' + '$e');
    }
  }
}
