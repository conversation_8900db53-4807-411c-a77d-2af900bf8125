import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/common/utils/dmsfile.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/services/utils/docattachment_services.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../services/common/documents_services.dart';
import '../../../utils/common_utils.dart';

class DmsFilesController extends GetxController {
  var isLoading = false.obs;
  RxList<DmsFile> filesdata = <DmsFile>[].obs;
  RxList<int?> tempfiles = <int>[].obs;

  Future fetchDmsFiles({int? entityid, String? entitytype, int? subentityid, String? subentitytype}) async {
    isLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      var user = await CommonUtils.getUserdata();
      var username = user?.userName;
      Map<String, dynamic> dynamicMap;
      tempfiles.clear();
      dynamicMap = {
        'a': ['sub_entity_id', 'EXACT', subentityid?.toString()],
        'b': ['sub_entity_type', 'EXACT', subentitytype],
        'c': ['p_user', 'EXACT', username]
      };
      List<DmsFile> fileRec;
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(dmsfilesurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        fileRec = tagObjsJson.map((tagJson) => DmsFile.fromJson(tagJson)).toList();
        filesdata.clear();
        if (fileRec.isNotEmpty && fileRec != null) filesdata.addAll(fileRec);
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchDmsFiles>>>>>>>' + '$e');
    } finally {
      isLoading.value = false;
    }
  }

  Future uploadFiles(List<File> files, {String? entityType, int? entityId, int? subentityid, String? subentitytype}) async {
    try {
      print('Inside upload $entityId');
      File f;
      List<String> paths = [];
      for (var i = 0; i < files.length; i++) {
        f = files[i];
        debugPrint('f    ${f}');
        paths.add(f.path);
      }

      String res = await DocAttachmentServices.uploadFiles(
          entityId: entityId, entityType: entityType, subentitytype: subentitytype, subentityid: subentityid, paths: paths);
      if (res == 'success') {
        Future.delayed(const Duration(seconds: 8), () {
          fetchDmsFiles(subentityid: subentityid, subentitytype: subentitytype, entityid: entityId, entitytype: entityType);
          Get.back();
        });
      } else {
        Get.back();
      }
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future saveDocuments() async {
    if (tempfiles.value.isNotEmpty && tempfiles.value.length > 0) {
      try {
        String result = 'success';

        Map m = {
          'fileids': tempfiles.value.join(','),
        };
        debugPrint('files-----------$m');

        result = await DocumentsServices.deletefilesbyid(m);

        if (result == 'success') {
          Get.back();
          ComponentUtils.showsnackbar(text: "Data upadated successfully...");
        } else
          ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while updating data...");
      } catch (e) {
        debugPrint(' saveDocuments>>>>>>>' + '$e');
      } finally {}
    } else {
      Get.back();
    }
  }
}
