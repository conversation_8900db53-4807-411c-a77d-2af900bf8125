import 'dart:core';
import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/services/common/entitycomments_services.dart';

import '../../models/common/contacts/commoncontactsdata.dart';
import '../../models/common/suppliers/ProjectSuppliersData.dart';

class ProjectSuppliersController extends GetxController {
  static ProjectSuppliersController instance = Get.find();
  dynamic args = Get.arguments;

  RxList<ProjectSuppliersData> suppliers = <ProjectSuppliersData>[].obs;
  var isLoading = false.obs;
  var addCommentCtrl = TextEditingController();
  var commentType = ''.obs;

  Future getProjectSuppliersData({String? searchText, int? entityid}) async {
    debugPrint('------------getProjectSuppliersData--------');
    suppliers.clear();
    isLoading.value = true;
    List<ProjectSuppliersData> suppdata;
    var payloadObj;

    Map<String, dynamic> dynamicMap = {
      'a': ['project_id', 'EXACT', entityid.toString()],
      'b': ['status_type', 'EXACT', 'AC'],
      'c': ['p_user_name', 'EXACT', UserContext.info()!.userName],
      'd': ['p_isContractor', 'EXACT', UserContext.info()!.isContractor],
    };
    if (searchText != null) {
      dynamicMap['d'] = ['all_columns', 'CONTAINS', searchText];
    }

    payloadObj = CommonServices.getFilter(dynamicMap);
    try {
      var resMap = await CommonServices.fetchDynamicApi(projectsuppliersurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            suppdata = tagObjsJson.map((tagJson) => ProjectSuppliersData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${suppdata.length}');
            suppliers.addAll(suppdata);
          }
        } else {
          suppliers.clear();
        }
      } else {
        suppliers.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isLoading.value = false;
    }
  }
}
