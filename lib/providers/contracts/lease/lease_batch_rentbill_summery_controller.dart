import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/contracts/lease/LeaseBatchRentPaysummeryData.dart';
import '../../../models/contracts/lease/leasebatchrentbillsummerydata.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class LeaseBatchRentBillSummeryController extends GetxController {
  var isLoading = false.obs;
  RxList<LeaseBatchRentBillsummeryData> lbrpsList = <LeaseBatchRentBillsummeryData>[].obs;
  Future loadBatchRentBillSummery(int? batchid) async {
    debugPrint('------------loadBatchRentBillSummery--------$batchid');
    isLoading(true);
    List<LeaseBatchRentBillsummeryData> reportRec;
    lbrpsList.clear();
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['p_batchId', 'EXACT', batchid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(leasebatchrentbillsummeryurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            reportRec = tagObjsJson.map((tagJson) => LeaseBatchRentBillsummeryData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${reportRec.length}');

            lbrpsList.addAll(reportRec);
          }
        } else {
          lbrpsList.clear();
        }
      } else {
        lbrpsList.clear();
      }
    } catch (e) {
      debugPrint('loadBatchRentBillSummery>>>>>>>>$e');
    }
    isLoading(false);
  }
}
