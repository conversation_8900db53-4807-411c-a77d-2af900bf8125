import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../models/contracts/lease/leasesearchdata.dart';

class LeaseHomeController extends GetxController {
  TextEditingController searchController = TextEditingController();

  RxList<LeaseSearchData> leasesearchlist = <LeaseSearchData>[].obs;
  var isLoading = false.obs;

  Future getLeaseSearchData({String? searchText}) async {
    debugPrint('------------getLeaseSearchData--------');
    leasesearchlist.clear();
    isLoading.value = true;
    List<LeaseSearchData> leasedata;
    var payloadObj;
    Map<String, dynamic>? resMap;

    if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['all_columns', 'CONTAINS', searchText],
      };
      payloadObj = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + payloadObj);
    }

    try {
      resMap = await CommonServices.fetchDynamicApi(leasehomesearchviewurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            leasedata = tagObjsJson.map((tagJson) => LeaseSearchData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${leasedata.length}');
            leasesearchlist.addAll(leasedata);
          }
        } else {
          leasesearchlist.clear();
        }
      } else {
        leasesearchlist.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isLoading.value = false;
    }
  }
}
