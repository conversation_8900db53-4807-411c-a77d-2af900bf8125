import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/sitemanagement/site/siteswot.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../../utils/request_api_utils.dart';

class SiteSwotController extends GetxController {
  var labelsloading = false.obs;
  Rx<SiteSwot> siterec = SiteSwot().obs;
  var isloading = false.obs;
  var prevText = ''.obs;
  var currText = ''.obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_SITE_SWOT', 'site_swot');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  Future getSiteRecord(int? siteid) async {
    debugPrint('------------getSiteRecord--------');

    isloading.value = true;
    List<SiteSwot> rec;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap = {
      'a': ['site_id', 'EXACT', siteid?.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap, size: 500);

    try {
      resMap = await CommonServices.fetchDynamicApi(sitesbvourl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            rec = tagObjsJson.map((tagJson) => SiteSwot.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${rec.length}');
            if (rec.length > 0) {
              debugPrint(' siterec.value------ ${rec.first.siteId}');
              siterec.value = rec.first;
              debugPrint(' siterec.value------ ${siterec.value.weakness}');
            }
          }
        } else {}
      } else {}
    } finally {
      isloading.value = false;
    }
  }

  Future onSaveSwotRecord() async {
    debugPrint('swot data    ${jsonEncode(siterec.value)}');
    ProgressUtil.showLoaderDialog(Get.context!);
    var result = -1;
    try {
      result = await saveRecord(siterec.value);
    } catch (e) {
      debugPrint('${e}');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    if (result != null && result == 0) {
      ComponentUtils.showsnackbar(text: "Data saved successfully...");
    } else {
      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }

  static Future saveRecord(SiteSwot sb) async {
    int? status = -1;
    try {
      String bodyjson = json.encode(sb);

      debugPrint(bodyjson);

      var resMap = await ApiService.post(savesiteswoturl, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        status = resMap['status'] as int?;
        debugPrint('status>>>>>$status');
      }
    } catch (error) {}

    return status;
  }
}
