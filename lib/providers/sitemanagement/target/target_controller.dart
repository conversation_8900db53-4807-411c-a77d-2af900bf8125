import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../models/sitemanagement/target/targetview.dart';
import '../../../models/ta_admin/entitystatus.dart';
import '../../ta_admin/label_controller.dart';

class TargetSearchController extends GetxController {
  LabelController talabel = Get.find<LabelController>();
  TextEditingController searchController = TextEditingController();
  var filterList = [].obs;

  RxList<TargetView> targetlist = <TargetView>[].obs;
  var isLoading = false.obs;

  //search widget
  var filterwidgetloading = false.obs;
  var allmyfilter = 'My'.obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var status = ''.obs;
  var statusname = ''.obs;
  RxList<EntityStatus> sortByLov = <EntityStatus>[].obs;
  var sortBy = ''.obs;

  @override
  void onInit() {
    super.onInit();
    //fetchProjects();
  }

  @override
  void onReady() {
    super.onReady();
    //fetchProjects();
  }

  @override
  void onClose() {
    debugPrint('------------onClose--------');
    super.onClose();
  }

  Future getTargetSearchData({String? searchText, String? action}) async {
    debugPrint('------------getTargetSearchData--------');
    var user = UserContext.info()!.userName;
    bool filterFlag = false;
    targetlist.clear();
    isLoading.value = true;
    List<TargetView> trgt;
    var payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap;
    var filter;

    if (searchController.text != null && searchController.text.isNotEmpty && searchController.text != '') {
      dynamicMap = {
        'a': ['all_columns', 'CONTAINS', searchController.text],
        'b': ['p_user_name', 'EXACT', user!.toUpperCase()],
      };
      filterFlag = true;
    } else {
      dynamicMap = {
        'b': ['p_user_name', 'EXACT', user!.toUpperCase()],
      };
    }
    dynamicMap['mpf'] = ['p_myTargetsFlag', 'EXACT', (allmyfilter.value == 'My') ? 'Y' : 'N'];
    if (status.value != null && status.value != '') {
      dynamicMap['stst'] = ['status', 'EXACT', status.value];
      filterFlag = true;
    }
    if (sortBy.value != null && sortBy.value != '') {
      filter = CommonServices.getFilter(dynamicMap, size: 100, sortby: sortBy.value);
    } else {
      filter = CommonServices.getFilter(dynamicMap, size: 100);
    }
    debugPrint('filter---------$filter');

    try {
      resMap = await CommonServices.fetchDynamicApi(targetsearchviewurl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            trgt = tagObjsJson.map((tagJson) => TargetView.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${trgt.length}');
            targetlist.addAll(trgt);
          }
        } else {
          targetlist.clear();
        }
      } else {
        targetlist.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isLoading.value = false;
      setFilterChips(filterFlag);
     await loadFilterLovData(action);
    }
  }

 Future loadFilterLovData(String? action) async{
    if (action == 'onload') {
      getStatusLovs();
        await talabel.getlabels('TMCMOBILE_TARGETSEARCH', 'Target_search', filtertype: 'tab');
      sortByLov.value.clear();
      sortByLov.insert(0, EntityStatus(statusCode: '', status: 'Select'));

      //setting sort By lov list--------------------------------------------------------------------------
      if (talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_TARGETNUMBER') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_TARGETNUMBER')?.value ?? 'Target Number',
            statusCode: 'TARGET_NUMBER'));
      }
      if (talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_TARGETNAME') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_TARGETNAME')?.value ?? 'Target Name',
            statusCode: 'TARGET_NAME'));
      }

      if (talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_STATUS') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_STATUS')?.value ?? 'Status', statusCode: 'STATUS'));
      }

      if (talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_DMANAME') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_DMANAME')?.value ?? 'Dma Name', statusCode: 'DMA_NAME'));
      }

      if (talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_STATE') != null) {
        sortByLov.value.add(
            EntityStatus(status: talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_STATE')?.value ?? 'State', statusCode: 'STATE'));
      }
      if (talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_CITY') != null) {
        sortByLov.value.add(
            EntityStatus(status: talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_CITY')?.value ?? 'City', statusCode: 'CITY'));
      }
      if (talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_ZIPCODE') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_TARGETSEARCH_SEARCHFILTERS_SORTBY_ZIPCODE')?.value ?? 'Zipcode', statusCode: 'ZIP_CODE'));
      }

      debugPrint('sortby count ${sortByLov.value.length}');
    }
  }

  Future getStatusLovs() async {
    try {
      statuslov.value = await CommonServices.getStatuses('TARGET');
    } catch (e) {
      debugPrint('getStatusLovs>>>>>>>$e');
    }
  }

  setFilterChips(bool flag) {
    Future.delayed(const Duration(seconds: 0), () {
      filterList.clear();
      //filterList.add(allmyfilter.value);

      filterList.add(GestureDetector(
        child: Chip(
          label: Text(isLoading.value ? '' : allmyfilter.value),
        ),
        onTap: () async {
          if (isLoading.value) {
            debugPrint('------');
          } else {
            if (allmyfilter.value == 'My') {
              allmyfilter.value = 'All';
            } else if (allmyfilter.value == 'All') {
              allmyfilter.value = 'My';
            }

            await getTargetSearchData();
          }
        },
      ));

      if (flag) {
        if (searchController.text != null && searchController.text.isNotEmpty && searchController.text != '') {
          filterList.add(Chip(
            avatar: const Icon(Icons.search, size: 20),
            label: Text(searchController.text),
            onDeleted: () async {
              searchController.text = '';
              await getTargetSearchData();
            },
          ));
        }
        if (status.value != null && status.value != '') {
          var statusname =
              statuslov.firstWhere((EntityStatus s) => s.statusCode == status.value, orElse: () => EntityStatus(status: '')).status ?? '';

          filterList.add(Chip(
            avatar: const Icon(Icons.flag, size: 20),
            label: Text(statusname),
            onDeleted: () async {
              status.value = '';
              await getTargetSearchData();
            },
          ));
        }
      }
    });
  }
}
