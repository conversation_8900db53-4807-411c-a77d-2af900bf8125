import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import '../../../models/spacemgmt/buildings/buildingsview_data.dart';
import '../../../models/ta_admin/entitystatus.dart';
import '../../ta_admin/label_controller.dart';

class BuildingsSearchController extends GetxController {
  LabelController talabel = Get.find<LabelController>();
  TextEditingController searchController = TextEditingController();
  var tabroles = Map<dynamic, dynamic>().obs;

  RxList<BuildingViewData> buildinglist = <BuildingViewData>[].obs;
  var isLoading = false.obs;
  //search widget
  var filterwidgetloading = false.obs;
  var allmyfilter = 'My'.obs;
  var filterList = [].obs;
  //RxList<EntityStatus> siteTypeLov = <EntityStatus>[].obs;
  // var siteTypeFilter = ''.obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var status = 'OP'.obs;
  var statusname = ''.obs;
  RxList<EntityStatus> sortByLov = <EntityStatus>[].obs;
  var sortBy = 'STORE_NUMBER'.obs;

  @override
  void onInit() {
    super.onInit();
    //fetchProjects();
  }

  @override
  void onReady() {
    super.onReady();
    //fetchProjects();
  }

  @override
  void onClose() {
    debugPrint('------------onClose--------');
    super.onClose();
  }

  Future getBuildingSearchData({String? searchText, String? action}) async {
    debugPrint('------------getBuildingSearchData--------');
    var user = UserContext.info()!.userName;
    bool filterFlag = false;
    buildinglist.clear();
    isLoading.value = true;
    List<BuildingViewData> buildinsRec;
    var payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap;
    var filter;
    dynamicMap = {
      'a': ['p_username', 'EXACT', user!.toUpperCase()],
      'z': ['loc_sub_entity_type', 'ISNULL', 'dummyvalue'], // for isnull, value is not required
    };
    if (searchController.text != null && searchController.text.isNotEmpty && searchController.text != '') {
      dynamicMap['b'] = ['all_columns', 'CONTAINS', searchController.text];
      filterFlag = true;
    }

    dynamicMap['mpf'] = ['p_myall_flag', 'EXACT', (allmyfilter.value == 'My') ? 'Y' : 'N'];

    if (status.value.isNotEmpty) {
      dynamicMap['stst'] = ['status', 'EXACT', status.value];
      filterFlag = true;
    }

    if (sortBy.value != null && sortBy.value != '') {
      filter = CommonServices.getFilter(dynamicMap, size: 100, sortby: sortBy.value);
    } else {
      filter = CommonServices.getFilter(dynamicMap, size: 100);
    }
    debugPrint('filter---------' + filter);
    try {
      resMap = await CommonServices.fetchDynamicApi(buildingsearchviewurl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            buildinsRec = tagObjsJson.map((tagJson) => BuildingViewData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${buildinsRec.length}');
            buildinglist.addAll(buildinsRec);
          }
        } else {
          buildinglist.clear();
        }
      } else {
        buildinglist.clear();
      }
    } finally {
      isLoading.value = false;
      await loadFilterLovData(action);
      setFilterChips(filterFlag);
    }
  }

  Future loadFilterLovData(String? action) async {
    if (action == 'onload') {
      await getStatusLovs();
      sortByLov.value.clear();
      sortByLov.insert(0, EntityStatus(statusCode: '', status: 'Select'));

      //setting sort By lov list--------------------------------------------------------------------------
      if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENUMBER') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENUMBER')?.value ?? 'Store Number',
            statusCode: 'STORE_NUMBER'));
      }
      if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENAME') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENAME')?.value ?? 'Store Name', statusCode: 'STORE_NAME'));
      }

      if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_PROPERTYNAME') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_PROPERTYNAME')?.value ?? 'Property Name',
            statusCode: 'PROPERTY_NAME'));
      }
    }
  }

  Future getStatusLovs() async {
    try {
      statuslov.value = await CommonServices.getStatuses('BUILDING');
    } catch (e) {
      debugPrint('getStatusLovs>>>>>>>$e');
    }
  }

  setFilterChips(bool flag) {
    Future.delayed(const Duration(seconds: 0), () {
      filterList.clear();
      //filterList.add(allmyfilter.value);

      filterList.add(GestureDetector(
        child: Chip(
          label: Text(isLoading.value ? '' : allmyfilter.value),
        ),
        onTap: () async {
          if (isLoading.value) {
            debugPrint('------');
          } else {
            if (allmyfilter.value == 'My') {
              allmyfilter.value = 'All';
            } else if (allmyfilter.value == 'All') {
              allmyfilter.value = 'My';
            }

            await getBuildingSearchData();
          }
        },
      ));

      if (flag) {
        if (status.value != null && status.value != '') {
          var statusname =
              statuslov.firstWhere((EntityStatus s) => s.statusCode == status.value, orElse: () => EntityStatus(status: '')).status ?? '';

          filterList.add(Chip(
            avatar: const Icon(Icons.flag, size: 20),
            label: Text(statusname),
            onDeleted: () async {
              status.value = '';
              await getBuildingSearchData();
            },
          ));
        }
        if (searchController.text != null && searchController.text.isNotEmpty && searchController.text != '') {
          filterList.add(Chip(
            avatar: const Icon(Icons.search, size: 20),
            label: Text(searchController.text),
            onDeleted: () async {
              searchController.text = '';
              await getBuildingSearchData();
            },
          ));
        }
      }
    });
  }

  Future<String> getUserEntityEditAccess({required String entitytype, required String entityid, String? roFlag}) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    Map<dynamic, dynamic>? vmap;
    String res = 'view';
    tabroles.value = Map<dynamic, dynamic>();
    try {
      vmap = await CommonServices.userEntityEditAccessService(entityid: entityid, entitytype: entitytype);
      debugPrint('getUserEntityEditAccess >>>>>>>' + roFlag.toString());
      if (vmap != null) {
        tabroles.value = vmap;
        if (vmap['result'] != null && vmap['result'] == 'Y' && vmap['role_flag'] != null && vmap['role_flag'] == 'Y')
          res = 'Y';
        else if (vmap['result'] != null && vmap['result'] == 'CN') res = 'error';

        if (res != 'error') res = (res?.toUpperCase() == 'Y' && (roFlag?.toUpperCase() == 'N' || roFlag == null)) ? 'edit' : 'view';
      }
    } catch (e) {
      debugPrint('getUserEntityEditAccess Exception >>>>>>>>>>>$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    debugPrint('getUserEntityEditAccess >>>>>>>' + res);
    return res;
    //}
  }
}
