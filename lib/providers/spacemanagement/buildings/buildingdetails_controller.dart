import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/spacemgmt/buildings/building_data.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../../utils/user_secure_storage.dart';

class BuildingDetailsController extends GetxController {
  var isDetailsLoading = false.obs;
  var scrollCtrl = ScrollController();
  var lastStatus = true.obs;
  var buildingRec = BuildingData().obs;

  Future getBuildingRecord(String? storeId) async {
    debugPrint('getBuildingRecord>>');

    List<BuildingData> bRec;
    var payloadObj;
    Map<String, dynamic>? resMap;
    //var user = UserContext.info()!.userName;
    Map<String, dynamic> dynamicMap = {
      'a': ['store_id', 'EXACT', storeId],
    };

    payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(buildingentityurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            bRec = tagObjsJson.map((tagJson) => BuildingData.fromJson(tagJson)).toList();
            debugPrint(' list Count>>${bRec.length}');
            if (bRec.length > 0) buildingRec.value = bRec.first;
          }
        } else {}
      }
    } finally {
      isDetailsLoading.value = false;
    }
  }
}
