import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../common/component_utils.dart';
import '../../../models/lookup_values.dart';
import '../../../models/spacemgmt/reservationlist_data.dart';

class ReservationController extends GetxController {
  RxList<ReservationListData> rsrvlist = <ReservationListData>[].obs;
  var existStatus = ['SCHEDULED', 'CHECK-IN', 'COMPLETED'];
  var rsrvListStatus = 0.obs;
  var isLoading = false.obs;
  RxList<LookupValues> rsrvtypelov = <LookupValues>[].obs;
  var rsrvtype = ''.obs;

  Future getRsrvListData({String? action, int? rsrvlistindex}) async {
    debugPrint('------------getRsrvListData--------');
    if (rsrvlistindex != null) rsrvListStatus.value = rsrvlistindex;
    rsrvlist.clear();
    isLoading.value = true;
    List<ReservationListData> rsrvData;
    var payloadObj;
    Map<String, dynamic>? resMap;
    debugPrint('rsrvListStatus >>>>>>> ${rsrvListStatus.value}');

    Map<String, dynamic> dynamicMap = {
      'a': ['person_user_name', 'EXACT', UserContext.info()!.userName],
      'c': ['status', 'IN', rsrvListStatus.value == 0 ? existStatus.join(',') : 'CLOSED'],
    };
    if (rsrvtype.value != null && rsrvtype.value != '') dynamicMap['bb'] = ['reservation_type', 'EXACT', rsrvtype.value];
    payloadObj = CommonServices.getFilter(dynamicMap, size: 100, sortby: 'start_date');

    try {
      if (action == 'onLoad') {
        rsrvtypelov.value.clear();

        var jsonObj = await CommonServices.fetchMultiLovs('RESERVATION_TYPE');
        if (jsonObj != null) {
          var fh = jsonObj['RESERVATION_TYPE'] as List?;

          if (fh != null) rsrvtypelov.value = fh.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
          rsrvtypelov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'All'));
        }
      }
      resMap = await CommonServices.fetchDynamicApi(reservationlisturl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            rsrvData = tagObjsJson.map((tagJson) => ReservationListData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${rsrvData.length}');
            rsrvlist.addAll(rsrvData);
          }
        } else {
          rsrvlist.clear();
        }
      } else {
        rsrvlist.clear();
      }
    } finally {
      isLoading.value = false;
    }
  }

  void updateStatus(int? reservationid, String status) async {
    String? result = await updateReservationStatus(status, reservationid);
    if (result == 'error') {
      showDialog(
        context: Get.context!,
        builder: (BuildContext context) => getAlertDialog("Error Occurred", 'Unable to update Status', context),
      );
    } else {
      getRsrvListData();
      var msg = status == 'CANCELED' ? 'Cancelled' : 'Checked In';
      ComponentUtils.showsnackbar(text: "Resrvation $reservationid $msg successfully...");
    }
  }

  Future<String?> updateReservationStatus(String status, int? reservationid) async {
    String? responseStr = "error";
    try {
      var data = <String, dynamic>{};
      data['reservationid'] = reservationid.toString();
      data['status'] = status;
      String apival = reservationupdatestatusurl;
      var resMap = await ApiService.post(apival, payloadObj: data, type: 'form');
      var tagObjsJson = jsonDecode(resMap) as Map<String, dynamic>;
      if (tagObjsJson['status'] == 0) {
        responseStr = tagObjsJson['statusmessage'];
      }
    } catch (e) {
      debugPrint('updateReservationStatus>>> $e');
    }
    return responseStr;
  }
}
