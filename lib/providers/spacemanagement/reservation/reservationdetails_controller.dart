import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/screens/suppliermanagement/suppliertabs/supplier_general.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../../../models/lookup_values.dart';
import '../../../models/spacemgmt/buildings/activereservbuildings_data.dart';
import '../../../models/spacemgmt/buildings/activereservespaces_data.dart';
import '../../../models/spacemgmt/buildings/activereservfloors_data.dart';
import '../../../models/spacemgmt/property/activeproperties_data.dart';
import '../../../models/spacemgmt/reservationlist_data.dart';
import '../../../models/ta_admin/page_rules.dart';
import '../../../models/ta_admin/user_info.dart';
import '../../../services/common_services.dart';
import '../../../utils/common_utils.dart';
import '../../../utils/constvariables.dart';
import '../../../utils/preferences_utils.dart';
import '../../../utils/request_api_utils.dart';
import '../../ta_admin/label_controller.dart';
import 'reservation_controller.dart';

class ReservationDetailsController extends GetxController {
  ReservationController rsrvCtrl = Get.find<ReservationController>();
  LabelController talabel = Get.find<LabelController>();
  RxList<ReservationListData> rsrvlist = <ReservationListData>[].obs;
  var rsrvRow = ReservationListData().obs;
  var allDayDefaultVal = 'Y'.obs;
  var rsrvrules = ReservationRules().obs;
  var isdetloading = false.obs;
  RxList<LookupValues> rsrvtypelov = <LookupValues>[].obs;
  var selectedStartDate = DateTime.now().obs;
  var selectedEndDate = DateTime.now().obs;
  var user = UserInfo().obs;
  var propertyLOV = <ActiveProperties>[].obs;
  var buildingLOV = <ActiveReserveBuilding>[].obs;
  var floorsLOV = <ActiveReserveFloors>[].obs;
  var spaceNameCtrl = TextEditingController();

  // String allDayDefaultVal =
  //(String) ADFUtils.evaluateEL("#{appResourceBundle.GENERALUSERHOME_NAVIGATION_NEWRESERVATIONS_ALLDAY.defaultValue}");

  var startdatecntrl = TextEditingController();
  var enddatecntrl = TextEditingController();

  var startdtcntrl = TextEditingController();
  var enddtcntrl = TextEditingController();

  Future getlabels(LabelController talabel) async {
    try {
      talabel.getlabels('TMCMOBILE_RESERVATION', 'reservation', ruletype: 'reservation');
      rsrvrules.value = talabel.reservationrules.value;
      debugPrint('rsrvrules -------${talabel.reservationrules.value.toJson()}');
      // alldayStartHour = rsrvrules?.alldayStartHour;
      // alldayStartMin = rsrvrules?.alldayStartMin;
      // alldayEndHour = rsrvrules?.alldayEndHour;
      // alldayEndMin = rsrvrules?.alldayEndMin;
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future getPersonData() async {
    String userid = await SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
    String apival = personuidurl + userid;
    Map<String, dynamic> map = {};
    try {
      var resMap = await ApiService.get(apival);
      debugPrint('getPersonId id11>>>>>>>');
      resMap = jsonDecode(resMap);

      if (resMap.isNotEmpty) {
        if (resMap['status'] == 0) {
          map = resMap['params'];
          if (map.isNotEmpty) {
            if (map['person_id'] != null) {
              user.value.personId = int.parse(map['person_id']);
              user.value.fullname = map['full_name'];

              debugPrint('getPersonData>>>>>${user.value.personId}  ${user.value.fullname}');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('getPersonId serv>>>>>>>$e');
    }
  }

  Future setCurrentRow({String? action, String? resType}) async {
    rsrvtypelov.value = rsrvCtrl.rsrvtypelov.value;
    rsrvtypelov.value[0] = LookupValues(lookupCode: '', lookupValue: 'Select');
    await getPersonData();
    if (action == 'create') {
      rsrvRow.value = ReservationListData();
      rsrvRow.value.reservationTitle = '$resType Reservation';
      await getProperties();
      if ('Y'.equalsIgnoreCase(allDayDefaultVal.value)) {
        allDayVCL(true);
      } else {
        allDayVCL(false);
      }

      if (user.value.personId == null || user.value.personId == 0) {
        rsrvRow.value.selfReserve = 'N';
      }
    }
    if (action == 'edit') {
    } else {
      rsrvtypelov.value = rsrvCtrl.rsrvtypelov.value;
      rsrvtypelov.value[0] = LookupValues(lookupCode: '', lookupValue: 'Select');

      rsrvRow.value.allDay = 'Y';
      rsrvRow.value.reservationTitle = rsrvRow.value.reservationType;

      DateTime currentDate = DateTime.now();
      DateTime dt = DateTime(currentDate.year, currentDate.month, currentDate.day, 08, 00);
      String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
      startdatecntrl.text = d1;

      DateTime dt1 = DateTime(currentDate.year, currentDate.month, currentDate.day, 08, 30);
      String d2 = DateFormat('MM/dd/yyyy hh:mm a').format(dt1);
      enddatecntrl.text = d2;

      String startdateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
      startdtcntrl.text = startdateformatted;

      String enddateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt1);
      enddtcntrl.text = enddateformatted;

      //_reservationType.text = reservationType;
      //print('$reservationType');
      if ('persondata' != null) {
        rsrvRow.value.selfReserve = 'Y';
      } else {
        rsrvRow.value.selfReserve = 'N';
      }
    }
  }

  void allDayVCL(bool val) {
    if (val) {
      rsrvRow.value.allDay = 'Y';
      DateTime currentDate = DateTime.now();
      DateTime dt = DateTime(
          currentDate.year, currentDate.month, currentDate.day, rsrvrules.value.alldayStartHour ?? 0, rsrvrules.value.alldayStartMin ?? 0);
      String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
      startdatecntrl.text = d1;

      DateTime dt1 = DateTime(
          currentDate.year, currentDate.month, currentDate.day, rsrvrules.value.alldayEndHour ?? 23, rsrvrules.value.alldayEndMin ?? 59);
      String d2 = DateFormat('MM/dd/yyyy hh:mm a').format(dt1);
      enddatecntrl.text = d2;

      String startdateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
      startdtcntrl.text = startdateformatted;

      String enddateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt1);
      enddtcntrl.text = enddateformatted;
    } else {
      rsrvRow.value.allDay = 'N';

      DateTime currentDate = DateTime.now();
      DateTime dt = DateTime(currentDate.year, currentDate.month, currentDate.day, 08, 00);
      String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
      startdatecntrl.text = d1;

      DateTime dt1 = DateTime(currentDate.year, currentDate.month, currentDate.day, 08, 30);
      String d2 = DateFormat('MM/dd/yyyy hh:mm a').format(dt1);
      enddatecntrl.text = d2;

      String startdateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
      startdtcntrl.text = startdateformatted;

      String enddateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt1);
      enddtcntrl.text = enddateformatted;
    }
  }

  Future selectDate(BuildContext context, String source) async {
    debugPrint('selectDate -------${talabel.reservationrules.value.toJson()}');
    rsrvrules.value = talabel.reservationrules.value;

    DateTime eeDate;
    if ('end' == source) {
      if (rsrvrules.value != null && (rsrvrules.value.bwStartEndDate != null && rsrvrules.value.bwStartEndDate! > 0)) {
        eeDate = selectedStartDate.value.add(Duration(days: rsrvrules.value.bwStartEndDate!));
      } else {
        eeDate = DateTime(2101);
      }
    } else {
      if (rsrvrules.value != null && (rsrvrules.value.advanceAllowedDays != null && rsrvrules.value.advanceAllowedDays! > 0)) {
        eeDate = selectedStartDate.value.add(Duration(days: rsrvrules.value.advanceAllowedDays!));
      } else {
        eeDate = DateTime(2101);
      }
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: source == 'end' ? selectedStartDate.value : DateTime.now(),
      initialEntryMode: DatePickerEntryMode.calendar,
      firstDate: source == 'end' ? selectedStartDate.value : DateTime.now(),
      lastDate: eeDate,
    );
    if (picked != null) {
      if (source == 'end') {
        selectedEndDate.value = picked;
      } else {
        selectedStartDate.value = picked;
      }

      selectTime(context, source);
    }
  }

  Future selectTime(BuildContext context, String source) async {
    TimeOfDay? selectedTime = const TimeOfDay(hour: 00, minute: 00);

    TimeOfDay? picked;
    if (rsrvRow.value.allDay != 'Y') {
      picked = await showTimePicker(context: context, initialTime: selectedTime!, initialEntryMode: TimePickerEntryMode.dial);
    }
    if (picked != null && rsrvRow.value.allDay != 'Y') {
      selectedTime = picked;
      selectedTime!.replacing(hour: selectedTime!.hourOfPeriod);

      if (source == 'end') {
        DateTime dt = DateTime(
            selectedEndDate.value.year, selectedEndDate.value.month, selectedEndDate.value.day, selectedTime!.hour, selectedTime!.minute);
        String d = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
        enddatecntrl.text = d;
        String d1 = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
        enddtcntrl.text = d1;
      } else {
        DateTime dt = DateTime(selectedStartDate.value.year, selectedStartDate.value.month, selectedStartDate.value.day, selectedTime!.hour,
            selectedTime!.minute);
        String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
        startdatecntrl.text = d1;
        String d2 = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
        debugPrint(d2);
        startdtcntrl.text = d2;

        DateTime enddateformat = DateTime(selectedStartDate.value.year, selectedStartDate.value.month, selectedStartDate.value.day,
            selectedTime!.hour, selectedTime!.minute + 30);

        String d = DateFormat('MM/dd/yyyy hh:mm a').format(enddateformat);
        enddatecntrl.text = d;
        String d11 = DateFormat('yyyy-MM-dd hh:mm a').format(enddateformat);
        enddtcntrl.text = d11;
      }
    } else {
      if (source == 'end') {
        DateTime enddateformat = DateTime(
            selectedEndDate.value.year,
            selectedEndDate.value.month,
            selectedEndDate.value.day,
            rsrvRow.value.allDay == 'Y' ? (rsrvrules.value.alldayEndHour ?? 23) : 23,
            rsrvRow.value.allDay == 'Y' ? (rsrvrules.value.alldayEndMin ?? 59) : 59);
        String d = DateFormat('MM/dd/yyyy hh:mm a').format(enddateformat);
        enddatecntrl.text = d;
        String d1 = DateFormat('yyyy-MM-dd hh:mm a').format(enddateformat);
        enddtcntrl.text = d1;
      } else {
        DateTime startdateformat = DateTime(
            selectedStartDate.value.year,
            selectedStartDate.value.month,
            selectedStartDate.value.day,
            rsrvRow.value.allDay == 'Y' ? (rsrvrules.value.alldayStartHour ?? 00) : 00,
            rsrvRow.value.allDay == 'Y' ? (rsrvrules.value.alldayStartMin ?? 00) : 00);
        String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(startdateformat);
        startdatecntrl.text = d1;
        String d2 = DateFormat('yyyy-MM-dd hh:mm a').format(startdateformat);

        startdtcntrl.text = d2;

        DateTime enddateformat = DateTime(
            selectedStartDate.value.year,
            selectedStartDate.value.month,
            selectedStartDate.value.day,
            rsrvRow.value.allDay == 'Y' ? (rsrvrules.value.alldayEndHour ?? 23) : 23,
            rsrvRow.value.allDay == 'Y' ? (rsrvrules.value.alldayEndMin ?? 59) : 59);
        String d = DateFormat('MM/dd/yyyy hh:mm a').format(enddateformat);
        enddatecntrl.text = d;
        String d3 = DateFormat('yyyy-MM-dd hh:mm a').format(enddateformat);
        enddtcntrl.text = d3;
      }
    }
  }

  Future getProperties() async {
    debugPrint('getProperties>>>');
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap;

    try {
      resMap = await CommonServices.fetchDynamicApi(reservpropertylovurl);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            propertyLOV.value = tagObjsJson.map((tagJson) => ActiveProperties.fromJson(tagJson)).toList();
            propertyLOV.value.insert(0, ActiveProperties(propertyId: 0, propertyName: 'Select a Property'));
          }
        }
      }
    } catch (e) {
      e.printError();
      propertyLOV.value.clear();
    }
  }

  Future getBuildings(int propertyId) async {
    debugPrint('getBuildings>>>');
    ;
    bool filterFlag = false;

    var payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap;
    var filter;

    dynamicMap = {
      'a': ['property_id', 'EXACT', propertyId.toString()],
      'b': ['building_status_type', 'EXACT', 'AC']
    };

    payloadObj = CommonServices.getFilter(dynamicMap, sortby: 'store_name');
    try {
      resMap = await CommonServices.fetchDynamicApi(reservbuildinglovurl);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            buildingLOV.value = tagObjsJson.map((tagJson) => ActiveReserveBuilding.fromJson(tagJson)).toList();
            buildingLOV.value.insert(0, ActiveReserveBuilding(storeId: 0, storeName: 'Select a Building'));
          }
        }
      }
    } catch (e) {
      e.printError();
      buildingLOV.value.clear();
    }
  }

  Future getFloors(int buildingId) async {
    debugPrint('getFloors>>>');

    var payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap;

    dynamicMap = {
      'a': ['parent_loc_id', 'EXACT', buildingId.toString()],
      'b': ['building_status_type', 'EXACT', 'AC'],
      'c': ['dwg_file_id', 'NOTNULL', 'dummyvalue']
    };

    payloadObj = CommonServices.getFilter(dynamicMap, sortby: 'store_name');
    try {
      resMap = await CommonServices.fetchDynamicApi(reservbuildinglovurl);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            floorsLOV.value = tagObjsJson.map((tagJson) => ActiveReserveFloors.fromJson(tagJson)).toList();
            floorsLOV.value.insert(0, ActiveReserveFloors(storeId: 0, storeName: 'Select a Floor'));
          }
        }
      }
    } catch (e) {
      e.printError();
      buildingLOV.value.clear();
    }
  }

  // static Future<int?> getsrid() async {
  //   int? result = 0;
  //   try {
  //     var apival = getservicerequestidurl;

  //     var resMap = await ApiService.get(apival);
  //     if (resMap != null) {
  //       resMap = jsonDecode(resMap);

  //       var status = resMap['status'] as int?;
  //       if (status == 0) {
  //         result = resMap['id'] as int?;
  //       }
  //     }
  //   } catch (error) {
  //     debugPrint(error?.toString());
  //   } finally {}

  //   return result;
  // }

//spaces search data
  TextEditingController spaceSearchCtrl = TextEditingController();
  var spaceLovData = <ActiveReserveSpaces>[].obs;
  var spaceDataLoading = false.obs;

  Future getSpaceData() async {
    debugPrint('getSpaceData>>>>>');
    spaceLovData.clear();
    spaceDataLoading.value = true;
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['p_buildingid', 'EXACT', rsrvRow.value.buildingId],
      'b': ['p_floorid', 'EXACT', rsrvRow.value.floorId],
      // 'c': ['p_reservationid', 'EXACT', buildingid],
      //'d': ['p_resv_start_date', 'EXACT', buildingid],
      // 'e': ['p_resv_end_date', 'EXACT', buildingid],
    };
    // if (floorid != null && floorid != '0') {
    //   dynamicMap['b'] = ['floor_id', 'EXACT', floorid];
    // }

    var payloadObj = CommonServices.getFilter(dynamicMap, size: 500, sortby: 'SPACE_NUMBER');

    try {
      resMap = await CommonServices.fetchDynamicApi(reservspaceslovurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            spaceLovData.value = tagObjsJson.map((tagJson) => ActiveReserveSpaces.fromJson(tagJson)).toList();
          }
        }
      }
    } finally {
      spaceDataLoading.value = false;
    }
  }
}
