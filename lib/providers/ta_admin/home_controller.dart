import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/ta_admin/user_info.dart';

import 'label_controller.dart';

class HomeController extends GetxController {
  LabelController talabel = Get.isRegistered<LabelController>() ? Get.find<LabelController>() : LabelController();
  RxMap<String, dynamic>? bundle = <String, dynamic>{}.obs;
  var isLoading = false.obs;
  var taskCntLoading = false.obs;
  var userParams = ''.obs;
  var wfcnt = ''.obs;
  var wfcntflag = false.obs;
  var aqserverurl = ''.obs;
  var userinfo = UserInfo().obs;
  var env = ''.obs;
  var hostlist = [
    'DEV',
    'LOCALHOST',
    'TEST',
    'AZTEST',
    'AISTEST',
    'AMZNTEST',
    'HMTEST',
    'CUSHMANTEST',
    'HMTEST',
    'USPSTEST',
  ].obs;

  Future<void> getUserInfo() async {
    userinfo.value = await CommonUtils.getUserdata();
  }

  Future<void> getBundle() async {
    try {
      String apival = bundleurl;
      isLoading.value = true;

      var resMap = await ApiService.get(apival);
      var res = jsonDecode(resMap);
      if (res['status'] == 0) {
        bundle = (res['records'] as Map?) as RxMap<String, dynamic>?;
      }
    } catch (e) {
      debugPrint('$e');
    }
    isLoading.value = false;
  }

  String? getLabel(String key) {
    String? label = "";
    if (bundle != null) {
      Map<String, dynamic>? map = bundle![key];
      label = map != null ? map['labelValue'] : false as String;
    }
    return label;
  }

  bool? isVisible(String key) {
    bool? isVisible = false;
    if (bundle != null) {
      Map<String, dynamic>? map = bundle![key];
      isVisible = map != null ? map['displayValue'] : false;
    }
    return isVisible;
  }

  Future loadtaskcount() async {
    Future.delayed(const Duration(seconds: 2), () async {
      if (talabel.get('TMCMOBILE_HOME_APPROVALS') != null && talabel.get('TMCMOBILE_HOME_APPROVALCOUNT') != null) {
        try {
          debugPrint('---loadtaskcount------');
          taskCntLoading(true);
          wfcnt.value = '';
          userParams.value = await (CommonUtils.getUserParamsdata());
          debugPrint('filter---------' + userParams.value.toString());

          wfcnt.value =
              await (CommonServices.getTaskCount(payloadObj: userParams.value, url: muserpendingtaskcounturl, taskname: 'all approvals'));
          wfcntflag.value = true;
        } catch (e) {
          debugPrint('$e');
        } finally {
          taskCntLoading(false);
          update();
        }
      }
    });
  }
}
