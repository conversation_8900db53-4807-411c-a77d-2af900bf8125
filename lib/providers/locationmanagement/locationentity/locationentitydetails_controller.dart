import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/location/locationentity/locationentity.dart';
import 'package:tangoworkplace/models/sitemanagement/site/siteview.dart';

import '../../../models/sitemanagement/target/targetview.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class LocationEntityDetailsController extends GetxController {
  Rx<LocationEntity> locEntityrec = LocationEntity().obs;
  var isloading = false.obs;
  Future getlocEntityRecord(String? entityid) async {
    debugPrint('------------getlocEntityRecord--------');

    List<LocationEntity> locEntityr;
    var payloadObj;
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['store_id', 'EXACT', entityid],
    };

    payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(locationentityviewurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            locEntityr = tagObjsJson.map((tagJson) => LocationEntity.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${locEntityr.length}');
            if (locEntityr.length > 0) locEntityrec.value = locEntityr.first;
          }
        } else {}
      }
    } finally {
      //isloading.value = false;
    }
  }
}
