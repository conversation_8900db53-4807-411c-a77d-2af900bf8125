class Demographic {
  List<String>? rows;
  String? dbColumn;
  String? columnName;

  Demographic({
    this.rows,
    this.dbColumn,
    this.columnName,
  });
  Demographic.fromJson(Map<String, dynamic> json) {
    if (json['rows'] != null) {
      final v = json['rows'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      rows = arr0;
    }
    dbColumn = json['dbColumn']?.toString();
    columnName = json['columnName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (rows != null) {
      final v = rows!;
      final arr0 = [];
      v.forEach((v) {
        arr0.add(v);
      });
      data['rows'] = arr0;
    }
    data['dbColumn'] = dbColumn;
    data['columnName'] = columnName;
    return data;
  }
}
