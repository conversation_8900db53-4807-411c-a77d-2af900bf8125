
import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';

import '../types/dates/aq_recurrence_pattern_type.dart';

part 'aq_recurrence_rule.g.dart';

@JsonSerializable(includeIfNull: false)
class AqRecurrenceRule {
  int? sysidRecurrenceRule;
  int? sysidMonthlyDayType;
  int? sysidRecurrencePatternType;
  AqDate? aqStartDate;
  AqDate? aqEndDate;
  int? occurrences;
  int? sysidMonthlyDayOccurrenceType;
  int? interval;
  int? theDate;
  bool? onSunday;
  bool? onMonday;
  bool? onTuesday;
  bool? onWednesday;
  bool? onThursday;
  bool? onFriday;
  bool? onSaturday;

  AqRecurrenceRule({
    this.sysidRecurrenceRule,
    this.sysidMonthlyDayType,
    this.sysidRecurrencePatternType,
    this.aqStartDate,
    this.aqEndDate,
    this.occurrences,
    this.sysidMonthlyDayOccurrenceType,
    this.interval,
    this.theDate,
    this.onSunday, 
    this.onMonday, 
    this.onTuesday,
    this.onWednesday,
    this.onThursday,
    this.onFriday,
    this.onSaturday
  });

  factory AqRecurrenceRule.fromJson(Map<String, dynamic> json) => _$AqRecurrenceRuleFromJson(json);

  Map<String, dynamic> toJson() => _$AqRecurrenceRuleToJson(this);
}