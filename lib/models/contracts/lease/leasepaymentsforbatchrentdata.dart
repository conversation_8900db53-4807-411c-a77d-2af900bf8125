class LeasePaymentsforBatchRentData {
  String? accountingString;
  String? advanceArrears;
  String? appliesFromDate;
  String? appliesToDate;
  int? brandId;
  int? clientId;
  String? comments;
  String? createdBy;
  String? creationDate;
  String? currencyCode;
  String? dueDate;
  String? effectiveDate;
  int? grossAmount;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  int? leaseFinancialId;
  int? leaseId;
  int? leaseOptionId;
  int? leasePaymentId;
  int? netAmount;
  int? objectVersionNumber;
  String? paymentStatus;
  String? paymentType;
  String? remitMessage;
  int? taxAmount;
  String? taxRate;
  int? vendorId;
  String? vendorName;
  String? vendorStatus;
  String? leaseIncluded;
  int? leasePaymentBatchId;
  String? oneTime;
  String? taxApplies;
  String? leaseNumber;
  String? storeNumber;
  String? storeName;
  int? prevNet;
  String? invoiceId;
  String? country;
  int? changeAmount;
  int? changePercent;
  int? netAmountLocal;
  int? netAmountGlobal;
  int? taxAmountLocal;
  int? taxAmountGlobal;
  int? grossAmountLocal;
  int? grossAmountGlobal;
  int? leasePaymentInvoiceId;
  int? errorCount;
  String? errorDescription;
  String? paymentCategory;

  LeasePaymentsforBatchRentData({
    this.accountingString,
    this.advanceArrears,
    this.appliesFromDate,
    this.appliesToDate,
    this.brandId,
    this.clientId,
    this.comments,
    this.createdBy,
    this.creationDate,
    this.currencyCode,
    this.dueDate,
    this.effectiveDate,
    this.grossAmount,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.leaseFinancialId,
    this.leaseId,
    this.leaseOptionId,
    this.leasePaymentId,
    this.netAmount,
    this.objectVersionNumber,
    this.paymentStatus,
    this.paymentType,
    this.remitMessage,
    this.taxAmount,
    this.taxRate,
    this.vendorId,
    this.vendorName,
    this.vendorStatus,
    this.leaseIncluded,
    this.leasePaymentBatchId,
    this.oneTime,
    this.taxApplies,
    this.leaseNumber,
    this.storeNumber,
    this.storeName,
    this.prevNet,
    this.invoiceId,
    this.country,
    this.changeAmount,
    this.changePercent,
    this.netAmountLocal,
    this.netAmountGlobal,
    this.taxAmountLocal,
    this.taxAmountGlobal,
    this.grossAmountLocal,
    this.grossAmountGlobal,
    this.leasePaymentInvoiceId,
    this.errorCount,
    this.errorDescription,
    this.paymentCategory,
  });
  LeasePaymentsforBatchRentData.fromJson(Map<String, dynamic> json) {
    accountingString = json['accounting_string']?.toString();
    advanceArrears = json['advance_arrears']?.toString();
    appliesFromDate = json['applies_from_date']?.toString();
    appliesToDate = json['applies_to_date']?.toString();
    brandId = json['brand_id']?.toInt();
    clientId = json['client_id']?.toInt();
    comments = json['comments']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    currencyCode = json['currency_code']?.toString();
    dueDate = json['due_date']?.toString();
    effectiveDate = json['effective_date']?.toString();
    grossAmount = json['gross_amount']?.toInt();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    leaseFinancialId = json['lease_financial_id']?.toInt();
    leaseId = json['lease_id']?.toInt();
    leaseOptionId = json['lease_option_id']?.toInt();
    leasePaymentId = json['lease_payment_id']?.toInt();
    netAmount = json['net_amount']?.toInt();
    objectVersionNumber = json['object_version_number']?.toInt();
    paymentStatus = json['payment_status']?.toString();
    paymentType = json['payment_type']?.toString();
    remitMessage = json['remit_message']?.toString();
    taxAmount = json['tax_amount']?.toInt();
    taxRate = json['tax_rate']?.toString();
    vendorId = json['vendor_id']?.toInt();
    vendorName = json['vendor_name']?.toString();
    vendorStatus = json['vendor_status']?.toString();
    leaseIncluded = json['lease_included']?.toString();
    leasePaymentBatchId = json['lease_payment_batch_id']?.toInt();
    oneTime = json['one_time']?.toString();
    taxApplies = json['tax_applies']?.toString();
    leaseNumber = json['lease_number']?.toString();
    storeNumber = json['store_number']?.toString();
    storeName = json['store_name']?.toString();
    prevNet = json['prev_net']?.toInt();
    invoiceId = json['invoice_id']?.toString();
    country = json['country']?.toString();
    changeAmount = json['change_amount']?.toInt();
    changePercent = json['change_percent']?.toInt();
    netAmountLocal = json['net_amount_local']?.toInt();
    netAmountGlobal = json['net_amount_global']?.toInt();
    taxAmountLocal = json['tax_amount_local']?.toInt();
    taxAmountGlobal = json['tax_amount_global']?.toInt();
    grossAmountLocal = json['gross_amount_local']?.toInt();
    grossAmountGlobal = json['gross_amount_global']?.toInt();
    leasePaymentInvoiceId = json['lease_payment_invoice_id']?.toInt();
    errorCount = json['error_count']?.toInt();
    errorDescription = json['error_description']?.toString();
    paymentCategory = json['payment_category']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['accounting_string'] = accountingString;
    data['advance_arrears'] = advanceArrears;
    data['applies_from_date'] = appliesFromDate;
    data['applies_to_date'] = appliesToDate;
    data['brand_id'] = brandId;
    data['client_id'] = clientId;
    data['comments'] = comments;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['currency_code'] = currencyCode;
    data['due_date'] = dueDate;
    data['effective_date'] = effectiveDate;
    data['gross_amount'] = grossAmount;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['lease_financial_id'] = leaseFinancialId;
    data['lease_id'] = leaseId;
    data['lease_option_id'] = leaseOptionId;
    data['lease_payment_id'] = leasePaymentId;
    data['net_amount'] = netAmount;
    data['object_version_number'] = objectVersionNumber;
    data['payment_status'] = paymentStatus;
    data['payment_type'] = paymentType;
    data['remit_message'] = remitMessage;
    data['tax_amount'] = taxAmount;
    data['tax_rate'] = taxRate;
    data['vendor_id'] = vendorId;
    data['vendor_name'] = vendorName;
    data['vendor_status'] = vendorStatus;
    data['lease_included'] = leaseIncluded;
    data['lease_payment_batch_id'] = leasePaymentBatchId;
    data['one_time'] = oneTime;
    data['tax_applies'] = taxApplies;
    data['lease_number'] = leaseNumber;
    data['store_number'] = storeNumber;
    data['store_name'] = storeName;
    data['prev_net'] = prevNet;
    data['invoice_id'] = invoiceId;
    data['country'] = country;
    data['change_amount'] = changeAmount;
    data['change_percent'] = changePercent;
    data['net_amount_local'] = netAmountLocal;
    data['net_amount_global'] = netAmountGlobal;
    data['tax_amount_local'] = taxAmountLocal;
    data['tax_amount_global'] = taxAmountGlobal;
    data['gross_amount_local'] = grossAmountLocal;
    data['gross_amount_global'] = grossAmountGlobal;
    data['lease_payment_invoice_id'] = leasePaymentInvoiceId;
    data['error_count'] = errorCount;
    data['error_description'] = errorDescription;
    data['payment_category'] = paymentCategory;
    return data;
  }
}

class LeasePaymentsforBatchRentDataRec {
  List<LeasePaymentsforBatchRentData?>? records;

  LeasePaymentsforBatchRentDataRec({
    this.records,
  });
  LeasePaymentsforBatchRentDataRec.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      final v = json['records'];
      final arr0 = <LeasePaymentsforBatchRentData>[];
      v.forEach((v) {
        arr0.add(LeasePaymentsforBatchRentData.fromJson(v));
      });
      records = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (records != null) {
      final v = records;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['records'] = arr0;
    }
    return data;
  }
}
