class BudgetTotal {
  String? division;
  int? entityId;
  String? entityType;
  double? initialBudget;
  double? approvedBudget;
  double? commitment;
  int? changeOrder;
  double? actualCost;
  double? estimatedCost;
  String? category;

  BudgetTotal({
    this.division,
    this.entityId,
    this.entityType,
    this.initialBudget,
    this.approvedBudget,
    this.commitment,
    this.changeOrder,
    this.actualCost,
    this.estimatedCost,
    this.category,
  });

  BudgetTotal.fromJson(Map<String, dynamic> json) {
    division = json['division']?.toString();
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    initialBudget = double.tryParse(json["initial_budget"].toString());
    approvedBudget = double.tryParse(json["approved_budget"].toString());
    commitment = double.tryParse(json["commitment"].toString());;
    changeOrder = json['change_order']?.toInt();
    actualCost = double.tryParse(json["actual_cost"].toString()); 
    estimatedCost = double.tryParse(json["estimated_cost"].toString()); 
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['division'] = division;
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['initial_budget'] = initialBudget;
    data['approved_budget'] = approvedBudget;
    data['commitment'] = commitment;
    data['change_order'] = changeOrder;
    data['actual_cost'] = actualCost;
    data['estimated_cost'] = estimatedCost;
    return data;
  }
}
