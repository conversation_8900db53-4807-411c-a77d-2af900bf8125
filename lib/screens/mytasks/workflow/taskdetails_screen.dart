//import 'dart:html';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/models/mytasks/workflow/budgetpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/copendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/documentpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/invoicependingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/leasebatchrentpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/leaseotppendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/leasependingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/popendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/projectpendingtask.dart';
import 'package:tangoworkplace/providers/mytasks/workflow/userpendingtasks_controller.dart';
import 'package:tangoworkplace/screens/contracts/lease/common/lease_batch_rentbill_summary.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../models/mytasks/workflow/budgetchgreqtask.dart';
import '../../../models/mytasks/workflow/leaserecurringcosttask.dart';
import '../../../models/mytasks/workflow/sitependingtask.dart';
import '../../../providers/common/utils/dmsfiles_controller.dart';
import '../../../providers/projectmanagement/project/budget/budget_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';
import '../../../utils/device_util.dart';
import '../../common/budget/projectbaselineversions_pg.dart';
import '../../common/utils/dms/dmsfiles.dart';
import '../../common/workflow/workflowhistory_widget.dart';
import '../../contracts/lease/common/lease_batch_rentpay_summary.dart';
import '../../contracts/lease/common/lease_payments_forbatch_rent_details.dart';
import '../../contracts/lease/common/lease_receivables_forbatch_rent_details.dart';
import '../../projectmanagement/project/budget/budget_approval_details.dart';
import '../../projectmanagement/project/budget/budget_data.dart';
import '../../sitemanagement/site/site_details.dart';

class TaskDetails extends StatelessWidget {
  final String? entitytype;
  final String? entitylabel;
  bool? docview;
  bool? leasebatchview;
  ProjectPendingTask? projecttask;
  BudgetPendingTask? budgettask;
  DocumentPendingTask? doctask;
  PoPendingTask? potask;
  CoPendingTask? cotask;
  InvoicePendingTask? invtask;
  LeasePendingTask? leasetask;
  LeaseOtpPendingTask? leaseotptask;
  LeaseBatchRentPendingTask? leasebatchrenttask;
  LeaseRecurringCostTask? leaserecurringcosttask;
  BudgetChgReqTaskData? budgetchgreqtask;
  SitePendingTask? sitetask;

  TaskDetails({
    this.entitytype,
    this.entitylabel,
    this.docview,
    this.leasebatchview,
    this.budgettask,
    this.projecttask,
    this.cotask,
    this.doctask,
    this.potask,
    this.invtask,
    this.leasetask,
    this.leaseotptask,
    this.leasebatchrenttask,
    this.leaserecurringcosttask,
    this.budgetchgreqtask,
    this.sitetask,
  });
  LabelController talabel = Get.find<LabelController>();

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  TabController? _tabController;
  MyApprovalsController myApprovalsCntrlr = Get.find<MyApprovalsController>();
  int? entityid;
  String? status;
  String? assignee;
  String? taskid;
  int? subentityid;
  String? subentitytype;
  int? projectid;
  final primary = ComponentUtils.primary;

  setwfdata() async {
    if (entitytype == 'PROJECT') {
      entityid = projecttask!.projectId;
      // status = projecttask.status ?? '';
      assignee = projecttask!.assignee_ ?? 'X';
      taskid = projecttask!.id_?.toString();
    } else if (entitytype == 'BUDGET') {
      entityid = budgettask!.baseid != null ? int.parse(budgettask!.baseid!) : null;
      // status = budgettask.status ?? '';
      assignee = budgettask!.assignee_ ?? 'X';
      taskid = budgettask!.id_?.toString();
      projectid = budgettask!.projectId;
    } else if (entitytype == 'BUDGETCO') {
      entityid = budgetchgreqtask?.budgetChgId ?? 0;
      // status = budgettask.status ?? '';
      assignee = budgetchgreqtask?.assignee_ ?? 'X';
      taskid = budgetchgreqtask?.taskid?.toString();
      projectid = budgetchgreqtask?.projectId;
    } else if (entitytype == 'PO') {
      entityid = potask!.poId;
      // status = potask.status ?? '';
      assignee = potask!.assignee_ ?? 'X';
      taskid = potask!.id_;
    } else if (entitytype == 'CO') {
      entityid = cotask!.chgOrderId;
      // status = cotask.status ?? '';
      assignee = cotask!.assignee_ ?? 'X';
      taskid = cotask!.id_?.toString();
    } else if (entitytype == 'INVOICE') {
      entityid = invtask!.invoiceId;
      // status = cotask.status ?? '';
      assignee = invtask!.assignee_ ?? 'X';
      taskid = invtask!.id_?.toString();
    } else if (entitytype == 'DOCUMENT') {
      entityid = doctask!.fileId;
      // status = doctask.status ?? '';
      assignee = doctask!.assignee_ ?? 'X';
      taskid = doctask!.id_?.toString();
    } else if (entitytype == 'LEASE') {
      entityid = leasetask!.leaseId;
      // status = doctask.status ?? '';
      assignee = leasetask!.assignee ?? 'X';
      taskid = leasetask!.id_?.toString();
    } else if (entitytype == 'LEASEOTP') {
      entityid = leaseotptask!.entityId;
      // status = doctask.status ?? '';
      assignee = leaseotptask!.assignee_ ?? 'X';
      taskid = leaseotptask!.id_?.toString();
    } else if (entitytype == 'LEASEBATCH') {
      entityid = leasebatchrenttask!.leasePaymentBatchId;
      // status = doctask.status ?? '';
      assignee = leasebatchrenttask!.assignee ?? 'X';
      taskid = leasebatchrenttask!.taskid?.toString();
    } else if (entitytype == 'LEASEREC') {
      entityid = int.tryParse(leaserecurringcosttask!.entityid?.toString() ?? '0');
      // status = doctask.status ?? '';
      assignee = leaserecurringcosttask!.assignee_ ?? 'X';
      taskid = leaserecurringcosttask!.id_?.toString();
    } else if (entitytype == 'SITE') {
      entityid = int.tryParse(sitetask!.entityid?.toString() ?? '0');
      // status = doctask.status ?? '';
      assignee = sitetask!.assignee_ ?? 'X';
      taskid = sitetask!.id_?.toString();
    }
    debugPrint('entitytype ----$entitytype    entityid-----$entityid');
    var user = await CommonUtils.getUserdata();
    var username = user?.userName;
    debugPrint('assignee ---$assignee      username---$username');
    myApprovalsCntrlr.bottombtnsflag.value = username == assignee;
  }

  @override
  Widget build(BuildContext context) {
    docview = docview ?? false;
    leasebatchview = leasebatchview ?? false;

    setwfdata();
    return DefaultTabController(
      initialIndex: 0,
      length: docview!
          ? 3
          : leasebatchview!
              ? 4
              : 2,
      child: Scaffold(
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text(
              // leasebatchview! ? 'Rent Batch' :
              'Task Details',
              style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
          backgroundColor: Colors.white,
          elevation: 5,
          leading: IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
            onPressed: () async {
              debugPrint('------back-------');
              ProgressUtil.showLoaderDialog(Get.context!);
              // myApprovalsCntrlr.bottombtnsflag.value = true;
              await myApprovalsCntrlr.fetchEntityTaskList(entitytype);

              myApprovalsCntrlr.taskListSearchController.text = '';

              ProgressUtil.closeLoaderDialog(Get.context!);
              Get.back();
            },
          ),
          bottom: TabBar(
            isScrollable: leasebatchview!,
            labelColor: ComponentUtils.tablabelcolor,
            unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
            indicatorColor: ComponentUtils.tabindicatorColor,
            indicatorSize: TabBarIndicatorSize.tab,
            labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontStyle: FontStyle.normal,
                fontSize: 14,
                color: CommonUtils.createMaterialColor(Color(0XFF3953A4))), // HexColor('#3953A4')),
            tabs: [
              const Tab(
                text: 'Details',
              ),
              if (docview!)
                const Tab(
                  text: 'Documents',
                ),
              if (leasebatchview!)
                const Tab(
                  text: 'Batch Summary',
                ),
              if (leasebatchview!)
                const Tab(
                  text: 'Manage Batch',
                ),
              const Tab(
                text: 'History',
              ),
            ],
          ),
        ),
        //],),

        key: _scaffoldKey,
        body: GetX<LabelController>(
          initState: (state) {
            if (entitytype == 'SITE') myApprovalsCntrlr.sitetaskdata.value = sitetask ?? SitePendingTask();
            myApprovalsCntrlr.detLoading.value = true;
            Future.delayed(const Duration(seconds: 1), () async {
              // await _getlabels(talabel);
              myApprovalsCntrlr.detLoading.value = false;
            });
          },
          builder: (_) {
            return TabBarView(
              children: <Widget>[
                taskdetContainer(entitytype),
                if (docview!) documents(context),
                if (leasebatchview!) leaseBatchSummery(), if (leasebatchview!) leaseBatchDetails(),
                WorkflowHistoryWidget(entityid: entityid.toString(), entitytype: entitytype), //workflowHistory(context),
              ],
            );
          },
        ),
        bottomNavigationBar: bottombuttonbar(),
      ),
    );
  }

  Widget bottombuttonbar() {
    return BottomAppBar(
      color: Colors.white,
      child: Obx(
        () => myApprovalsCntrlr.bottombtnsflag.isTrue
            ? Container(
                margin: const EdgeInsets.only(left: 12.0, right: 12.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    TaButton(
                      type: 'elevate',
                      buttonText: 'Reject',
                      onPressed: () {
                        wfactionpopup('rejected');
                      },
                    ),
                    (myApprovalsCntrlr.approvarrole.value == 'na' || myApprovalsCntrlr.approvarrole.value == 'approver')
                        ? TaButton(
                            type: 'elevate',
                            buttonText: 'Approve',
                            color: Colors.green,
                            onPressed: () {
                              wfactionpopup('approved');
                            },
                          )
                        : SizedBox(
                            height: 2,
                            width: 2,
                          ),
                  ],
                ),
              )
            : Container(
                width: 1,
                height: 1,
              ),
      ),
    );
  }

  wfactionpopup(String type) {
    return Get.defaultDialog(
        title: 'Add Comment',
        titleStyle: TextStyle(fontSize: 14),
        content: Obx(() => TaInputTextField(
              title: 'Comment',
              controller: myApprovalsCntrlr.addCommentCtrl,
              minLines: 3,
              maxLines: 15,
              keyboard: TextInputType.multiline,
              textInputAct: TextInputAction.newline,
              errortext: myApprovalsCntrlr.commentValidate.value ? null : 'Please enter comment.',
              //onChanged: (val) {},
            )),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: 'Cancel',
                onPressed: () {
                  myApprovalsCntrlr.resetCommentfield();
                  Get.back();
                },
              ),
              SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Ok',
                onPressed: () async {
                  ProgressUtil.showLoaderDialog(Get.context!);
                  await myApprovalsCntrlr.submitwftask(type, assignee, int.parse(taskid!), entitytype: entitytype, projectid: projectid);
                  myApprovalsCntrlr.fetchApprovalHistory(entitytype, entityid?.toString());
                  if (entitytype == 'SITE') {
                    await myApprovalsCntrlr.getSiteDatainfo();
                  }
                  //Get.back();
                },
              ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  Widget leaseBatchSummery() {
    return leasebatchrenttask?.batchType == 'AP'
        ? LeaseBatchRentPaySummery(
            batchid: entityid,
          )
        : (leasebatchrenttask?.batchType == 'AR')
            ? LeaseBatchRentBillSummery(
                batchid: entityid,
              )
            : (leasebatchrenttask?.batchType == 'INVOICEAP')
                ? LeaseBatchRentBillSummery(
                    batchid: entityid,
                  )
                : Center(
                    child: Text(
                      'No Data',
                      style: TextStyle(
                        fontSize: DeviceUtils.taFontSize(1.5, Get.context!),
                      ),
                    ),
                  );
  }

  Widget leaseBatchDetails() {
    return leasebatchrenttask?.batchType == 'AP'
        ? LeasePaymentsforBatchRentDetails(
            batchid: entityid,
          )
        : (leasebatchrenttask?.batchType == 'AR')
            ? LeaseReceivablesforBatchRentDetails(
                batchid: entityid,
              )
            : Center(
                child: Text(
                  'No Data',
                  style: TextStyle(
                    fontSize: DeviceUtils.taFontSize(1.5, Get.context!),
                  ),
                ),
              );
  }

  Widget documents(BuildContext context) {
    DmsFilesController dmsfileCtrl = Get.put(DmsFilesController());
    return DmsFiles(
      subentityid: entityid,
      subentitytype: entitytype,
      file_mode: 'view',
    );
  }

  Widget taskdetContainer(String? entitype) {
    return GestureDetector(
      onTap: () {
        if (entitytype == 'BUDGET') {
          BudgetController budgetController = Get.put(BudgetController());
          LabelController talabel = Get.find<LabelController>();

          debugPrint('projectid-----${budgettask!.projectId}');
          Get.to(() => BudgetApprovalDetails(
                entityId: budgettask!.projectId,
                entityType: 'PROJECT',
                //source: 'PROJ-BUDGET-DET',
              ));
        }
      },
      child: SingleChildScrollView(
        child: myApprovalsCntrlr.detLoading.value
            ? Container(
                child: const ProgressIndicatorCust(),
                padding: const EdgeInsets.only(top: 200),
              )
            : Column(
                children: [
                  Container(
                    margin: EdgeInsets.all(10.0),
                    constraints: BoxConstraints(maxWidth: 500),
                    padding: EdgeInsets.all(10.0),
                    // height: 200,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.0),
                        boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                    child: Obx(
                      () => taskdetails(
                        entitytype,
                        project: projecttask,
                        budget: budgettask,
                        budgetchgreq: budgetchgreqtask,
                        doc: doctask,
                        po: potask,
                        co: cotask,
                        inv: invtask,
                        lease: leasetask,
                        leaseotp: leaseotptask,
                        leasebatch: leasebatchrenttask,
                        leasereccost: leaserecurringcosttask,
                        site: myApprovalsCntrlr.sitetaskdata.value,
                      ),
                    ),
                  ),
                  // if (entitype?.toUpperCase() == 'BUDGET') baselineVersions('Baseline Versions'),
                ],
              ),
      ),
    );
  }

  Widget baselineVersions(String str) {
    //var txt = talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET')?.value ?? 'Details';
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
      margin: EdgeInsets.all(10.0),
      constraints: BoxConstraints(maxWidth: 500),
      padding: EdgeInsets.only(bottom: 5.0, left: 10.0, right: 10.0, top: 5.0),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(str),
        IconButton(
            onPressed: () {
              Get.to(() => ProjBaselineVerionsPg(
                    hdrtitle: str,
                    projectid: budgettask!.projectId,
                  ));
            },
            icon: Icon(
              Icons.arrow_forward_ios,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  taskdetails(
    String? entitytype, {
    ProjectPendingTask? project,
    BudgetPendingTask? budget,
    BudgetChgReqTaskData? budgetchgreq,
    DocumentPendingTask? doc,
    PoPendingTask? po,
    CoPendingTask? co,
    InvoicePendingTask? inv,
    LeasePendingTask? lease,
    LeaseOtpPendingTask? leaseotp,
    LeaseBatchRentPendingTask? leasebatch,
    LeaseRecurringCostTask? leasereccost,
    SitePendingTask? site,
  }) {
    if (entitytype == 'PROJECT')
      return projColumn(project!);
    else if (entitytype == 'BUDGET')
      return budgetColumn(budget!);
    else if (entitytype == 'BUDGETCO')
      return budgetChgReqColumn(budgetchgreq!);
    else if (entitytype == 'DOCUMENT')
      return docColumn(doc!);
    else if (entitytype == 'PO')
      return poColumn(po!);
    else if (entitytype == 'CO')
      return coColumn(co!);
    else if (entitytype == 'INVOICE')
      return invColumn(inv!);
    else if (entitytype == 'LEASE')
      return leaseColumn(lease!);
    else if (entitytype == 'LEASEBATCH')
      return leaseBatchColumn(leasebatch!);
    else if (entitytype == 'LEASEOTP')
      return leaseOtpColumn(leaseotp!);
    else if (entitytype == 'LEASEREC')
      return leaseRecCostColumn(leasereccost!);
    else if (entitytype == 'SITE')
      return siteColumn(sitetask!);
    else
      Container();
  }

  Widget projColumn(ProjectPendingTask p) {
    return Column(
      children: [
        taskAttribute('Entity', p.entityType ?? ''),
        taskAttribute('Project Name', p.projectName!),
        taskAttribute('Project Id', p.projectId?.toString() ?? ''),
        taskAttribute('Start Date', p.startTime_?.toString() ?? ''),
        // taskAttribute('Projected Open Date', p.projectedOpenDate ?? ''),
        // taskAttribute('Real Estate Manager', p.realEstateManager ?? ''),
        // taskAttribute('Construction Manager', p.constructionManager ?? ''),
        taskAttribute('Forecast', ComponentUtils.convertNumberFormat(p.forecast?.toString() ?? '0.00')),
        taskAttribute('Approved Budget', ComponentUtils.convertNumberFormat(p.approvedbudget?.toString() ?? '0.00')),
        //taskAttribute('Created By', p.createdBy),
        //taskAttribute('Creation Date', p.creationDate),
      ],
    );
  }

  Widget siteColumn(SitePendingTask s) {
    return Column(
      children: [
        GestureDetector(
          onTap: () => Get.to(SiteDetails(
            nav_from: 'approval',
            siteid: int.tryParse(s.entityid!.toString()),
            sitename: s.siteName ?? '',
          )),
          child: taskAttribute('Site Name', s.siteName ?? '',
              valStyle: TextStyle(
                  color: primary, fontSize: 12, letterSpacing: .1, fontWeight: FontWeight.bold, decoration: TextDecoration.underline)),
        ),
        taskAttribute('Entity Id', s.entityid?.toString() ?? ''),
        taskAttribute('Latitude', s.latitude?.toString() ?? ''),
        taskAttribute('Longitude', s.longitude?.toString() ?? ''),
        taskAttribute('Address', s.address ?? ''),
        taskAttribute('Status', s.dealStatusDesc ?? ''),
        taskAttribute('Forecast', ComponentUtils.convertNumberFormat(s.forecast?.toString() ?? '0.00')),
      ],
    );
  }

  Widget budgetColumn(BudgetPendingTask b) {
    return Column(
      children: [
        taskAttribute('Project Name', b.projectName!),
        //taskAttribute('Entity', b.entityType ?? '' + '-' + b.entityId?.toString() ?? ''),
        taskAttribute('Initial Budget', ComponentUtils.convertNumberFormat(b.forecast?.toString() ?? '0.0')),
        taskAttribute('Approved Budget', ComponentUtils.convertNumberFormat(b.approvedbudget?.toString() ?? '0.0')),
        taskAttribute('Project Manager', b.realEstateManager ?? ''),
        taskAttribute('Construction Manager', b.constructionManager ?? ''),
        taskAttribute('Initiator', b.initiator!),
        // taskAttribute('Creation Date', b.creationDate),
      ],
    );
  }

  Widget budgetChgReqColumn(BudgetChgReqTaskData b) {
    return Column(
      children: [
        taskAttribute('Project Name', b?.projectName ?? ''),
        taskAttribute('Budget Change Number', b?.budgetChgNumber ?? ''),
        taskAttribute('Change Reason', b?.chgReasonDesc ?? ''),
        taskAttribute('Change Order Date', b?.chgOrderDate ?? ''),
        taskAttribute('Release Amount', ComponentUtils.convertNumberFormat(b.releaseAmount?.toString() ?? '0.0')),
        // taskAttribute('Approved Budget', ComponentUtils.convertNumberFormat(b.approvedbudget?.toString() ?? '0.0')),
        taskAttribute('Start Time', b.creationDate ?? ''),
      ],
    );
  }

  Widget docColumn(DocumentPendingTask d) {
    return Column(
      children: [
        taskAttribute('File Name', d.fileName ?? ''),
        taskAttribute('Entity', (d.entityType ?? '')),
        taskAttribute('Entity Id', d.entityId?.toString() ?? ''),
        if (d.entityType?.toUpperCase() == 'PROJECT') taskAttribute('Entity Name', d.project_name ?? ''),
        if (d.entityType?.toUpperCase() == 'SITE') taskAttribute('Entity Name', d.site_name ?? ''),
        if (d.entityType?.toUpperCase() == 'STORE' || d.entityType?.toUpperCase() == 'BUILDING')
          taskAttribute('Entity Name', d.store_name ?? ''),
        if (d.entityType?.toUpperCase() == 'LEASE') taskAttribute('Entity Name', d.lease_name ?? ''),
        taskAttribute('Document Type', d.docTypeDesc ?? ''),
        taskAttribute('Folder Name', d.folderName ?? ''),
        taskAttribute('Description', d.description ?? ''),
        taskAttribute('Created By', d.createdBy!),
        taskAttribute('Creation Date', d.creationDate!),
      ],
    );
  }

  Widget poColumn(PoPendingTask p) {
    return Column(
      children: [
        taskAttribute('PO Number', p.poNumber ?? ''),
        taskAttribute('Project Name', p.projectName ?? ''),
        taskAttribute('Amount', ComponentUtils.convertNumberFormat(p.amount?.toString() ?? '0.0')),
        taskAttribute('Currency', p.currency ?? ''),
        taskAttribute('Vendor', p.supplierName ?? ''),
        taskAttribute('Initiator', p.initiator ?? ''),
        taskAttribute('Start Time', p.startTime_ ?? ''),
      ],
    );
  }

  Widget coColumn(CoPendingTask p) {
    return Column(
      children: [
        taskAttribute('CO Number', p.chgOrderNumber ?? ''),
        taskAttribute('PO Number', p.poNumber ?? ''),
        taskAttribute('Project Name', p.projectName!),
        taskAttribute('Amount', ComponentUtils.convertNumberFormat(p.amount?.toString() ?? '0.00')),
        taskAttribute('Currency', p.currency ?? ''),
        taskAttribute('Vendor', p.supplierName ?? ''),
        taskAttribute('Change order date', p.chgOrderDate ?? ''),
        // taskAttribute('Created By', p.createdBy),
        // taskAttribute('Creation Date', p.creationDate),
      ],
    );
  }

  Widget invColumn(InvoicePendingTask i) {
    return Column(
      children: [
        taskAttribute('Invoice Number', i.invoiceNumber ?? ''),
        taskAttribute('PO Number', i.poNumber ?? ''),
        taskAttribute('Project Name', i.projectName ?? ''),
        taskAttribute('Amount', ComponentUtils.convertNumberFormat(i.amount?.toString() ?? '0.00')),
        taskAttribute('Currency', i.currency ?? ''),
        taskAttribute('Vendor', i.supplierName ?? ''),
        taskAttribute('Start Date', i.startTime_ ?? ''),
      ],
    );
  }

  Widget leaseOtpColumn(LeaseOtpPendingTask l) {
    return Column(
      children: [
        taskAttribute('Lease Name', l.leaseName ?? ''),
        //taskAttribute('Lease Id', l.leaseId?.toString() ?? ''),
        taskAttribute('Lease Number', l.leaseNum ?? ''),
        //taskAttribute('Vendor', l.leaseId?.toString() ?? ''),
        taskAttribute('City State', l.citystate ?? ''),
        taskAttribute('Effective Date', l.effectiveDate ?? ''),
        taskAttribute('Payment Type', l.paymentTypeDesc ?? ''),
        taskAttribute('Amount', ComponentUtils.convertNumberFormat(l.grossAmount?.toString() ?? '0.0')),
        taskAttribute('Creation Date', l.creationDate ?? ''),
        taskAttribute('Created By', l.createdBy ?? ''),
        taskAttribute('Vendor', l.vendor?.toString() ?? ''),
      ],
    );
  }

  Widget leaseRecCostColumn(LeaseRecurringCostTask l) {
    return Column(
      children: [
        taskAttribute('Lease Name', l.name ?? ''),
        taskAttribute('Lease Id', l.leaseId?.toString() ?? ''),
        taskAttribute('Lease Number', l.leaseNum ?? ''),
        taskAttribute('City State', l.citystate ?? ''),
        taskAttribute('Effective From', l.startDate ?? ''),
        taskAttribute('Effective To', l.endDate ?? ''),
        taskAttribute('Payment Type', l.paymentType ?? ''),
        taskAttribute('Frequency', l.frequency ?? ''),
        taskAttribute('Period Amount', ComponentUtils.convertNumberFormat(l.amout?.toString() ?? '0.0')),
        taskAttribute('Total Obligation', ComponentUtils.convertNumberFormat(l.totalObligation?.toString() ?? '0.0')),
        taskAttribute('In Queue Since', l.startTime_ ?? ''),
      ],
    );
  }

  Widget leaseColumn(LeasePendingTask l) {
    return Column(
      children: [
        taskAttribute('Lease Number', l.leaseNum ?? ''),
        taskAttribute('Lease Name', l.name ?? ''),
        taskAttribute('Lease Commencement Date', l.leaseCommencementDate ?? ''),
        taskAttribute('Termination Date', l.leaseTerminationDate ?? ''),
        taskAttribute('City State', l.citystate ?? ''),
        taskAttribute('Portfolio Type', l.leasePortfolioType ?? ''),
        taskAttribute('Start Date', l.startTime_ ?? ''),
      ],
    );
  }

  Widget leaseBatchColumn(LeaseBatchRentPendingTask l) {
    return Column(
      children: [
        taskAttribute('Lease Batch Number', l.batchNumber ?? ''),
        taskAttribute('Lease Financials Group', l.leaseFinancialsGroup ?? ''),
        taskAttribute('Lease Payment Batch Id', '${l.leasePaymentBatchId ?? ''}'),
        taskAttribute(
          'Batch Total',
          ComponentUtils.convertNumberFormat(l.batchTotal?.toString() ?? '0.0'),
        ),
        taskAttribute('Status', l.statusDesc ?? ''),
        taskAttribute('Tenancy Type', l.paymentTypeDesc ?? ''),
        taskAttribute('From', l.appliesFromDate ?? ''),
        taskAttribute('To', l.appliesToDate ?? ''),
        taskAttribute('Creation Date', l.creationDate ?? ''),
        taskAttribute('Created By', l.createdBy ?? ''),
      ],
    );
  }

  Widget taskAttribute(String label, String val, {TextStyle? valStyle}) {
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(right: 20),
              child: Text(
                label,
                style: const TextStyle(fontSize: 12),
              ),
            ),

            Flexible(
              child: Text(val ?? '',
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                  style: valStyle ??
                      TextStyle(
                        color: primary,
                        fontSize: 12,
                        letterSpacing: .1,
                      )),
            ),
            // Text(
            //   val,
            //   style: const TextStyle(
            //     fontSize: 12,
            //     //fontWeight: FontWeight.bold
            //   ),
            // ),
          ],
        ));
  }
}
