import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/utils.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/mytasks/workflow/budgetpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/copendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/documentpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/invoicependingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/popendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/projectpendingtask.dart';
import 'package:tangoworkplace/providers/contracts/lease/lease_receivables_forbatch_rent_controller.dart';
import 'package:tangoworkplace/providers/mytasks/workflow/userpendingtasks_controller.dart';
import 'package:tangoworkplace/screens/mytasks/workflow/taskdetails_screen.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../models/mytasks/workflow/budgetchgreqtask.dart';
import '../../../models/mytasks/workflow/leasebatchrentpendingtask.dart';
import '../../../models/mytasks/workflow/leaseotppendingtask.dart';
import '../../../models/mytasks/workflow/leasependingtask.dart';
import '../../../models/mytasks/workflow/leaserecurringcosttask.dart';
import '../../../models/mytasks/workflow/sitependingtask.dart';
import '../../../providers/contracts/lease/lease_batch_rentbill_summery_controller.dart';
import '../../../providers/contracts/lease/lease_batch_rentpay_summery_controller.dart';
import '../../../providers/contracts/lease/lease_payments_forbatch_rent_controller.dart';

class UsertaskList extends StatelessWidget {
  final String? entitylabel;
  final String? entitytype;
  UsertaskList({Key? key, this.entitylabel, this.entitytype}) : super(key: key);
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  MyApprovalsController myApprovalsCntrlr = Get.find<MyApprovalsController>();

  Future _refreshUsertaskslist() async {
    myApprovalsCntrlr.fetchProjectUsertaskList(entitytype);
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('------------' + entitytype!);
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(entitylabel ?? entitytype! + ' Approvals',
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,

            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            myApprovalsCntrlr.approvarrole.value = 'na';
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: [
          TaSearchInputText(
              makeSearch: (searchtext) async {
                if (entitytype == 'LEASE')
                  await myApprovalsCntrlr.fetchLeaseUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'LEASEOTP')
                  await myApprovalsCntrlr.fetchLeaseOtpUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'LEASEBATCH')
                  await myApprovalsCntrlr.fetchLeaseBatchRentUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'LEASEREC')
                  await myApprovalsCntrlr.fetchLeaseRecurCosttaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'PROJECT')
                  await myApprovalsCntrlr.fetchProjectUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'PO')
                  await myApprovalsCntrlr.fetchPoUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'CO')
                  await myApprovalsCntrlr.fetchCoUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'INVOICE')
                  await myApprovalsCntrlr.fetchInvoiceUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'BUDGETCO')
                  await myApprovalsCntrlr.fetchBudgetcoUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'DOCUMENT')
                  await myApprovalsCntrlr.fetchDocsUsertaskList(entitytype, searchtext: searchtext?.toString());
                else if (entitytype == 'SITE')
                  await myApprovalsCntrlr.fetchSiteUsertaskList(entitytype, searchtext: searchtext?.toString());
              },
              searchController: myApprovalsCntrlr.taskListSearchController,
              hintSearch: 'Search '),
          if (entitytype == 'LEASE') leasetasks(context),
          if (entitytype == 'LEASEBATCH') leasebatchrenttasks(context),
          if (entitytype == 'LEASEOTP') leaseotptasks(context),
          if (entitytype == 'LEASEREC') leasrecurcosttasks(context),
          if (entitytype == 'PROJECT') projecttasks(context),
          if (entitytype == 'BUDGET') budgettasks(context),
          if (entitytype == 'PO') potasks(context),
          if (entitytype == 'CO') cotasks(context),
          if (entitytype == 'INVOICE') invoicetasks(context),
          if (entitytype == 'DOCUMENT') documenttasks(context),
          if (entitytype == 'BUDGETCO') budgetcotasks(context),
          if (entitytype == 'SITE') sitetasks(context),
        ],
      ),
    );
  }

  Widget projecttasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchProjectUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.projecttasklist != null && ctrlr.projecttasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.projecttasklist.length,
                        itemBuilder: (context, index) {
                          return projtaskitem(context, ctrlr.projecttasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget budgettasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchBudgetUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.budgettasklist != null && ctrlr.budgettasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.budgettasklist.length,
                        itemBuilder: (context, index) {
                          return budgettaskitem(context, ctrlr.budgettasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget budgetcotasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchBudgetcoUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.budgetchgreqlist != null && ctrlr.budgetchgreqlist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.budgetchgreqlist.length,
                        itemBuilder: (context, index) {
                          return budgetchgreqtaskitem(context, ctrlr.budgetchgreqlist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget documenttasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchDocsUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? const ProgressIndicatorCust()
                : ctrlr.documenttasklist != null && ctrlr.documenttasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.documenttasklist.length,
                        itemBuilder: (context, index) {
                          return doctaskitem(context, ctrlr.documenttasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget sitetasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchSiteUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? const ProgressIndicatorCust()
                : ctrlr.sitetasklist != null && ctrlr.sitetasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.sitetasklist.length,
                        itemBuilder: (context, index) {
                          return sitetaskitem(context, ctrlr.sitetasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget potasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchPoUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? const ProgressIndicatorCust()
                : ctrlr.potasklist != null && ctrlr.potasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.potasklist.length,
                        itemBuilder: (context, index) {
                          return potaskitem(context, ctrlr.potasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget cotasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchCoUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.cotasklist != null && ctrlr.cotasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.cotasklist.length,
                        itemBuilder: (context, index) {
                          return cotaskitem(context, ctrlr.cotasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget invoicetasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            Get.find<MyApprovalsController>().fetchInvoiceUsertaskList(entitytype);
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.invoicetasklist != null && ctrlr.invoicetasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.invoicetasklist.length,
                        itemBuilder: (context, index) {
                          return invoicetaskitem(context, ctrlr.invoicetasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget leasetasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            myApprovalsCntrlr.taskListSearchController.text = '';
            Get.find<MyApprovalsController>().fetchLeaseUsertaskList(entitytype, searchtext: '');
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.leasetasklist != null && ctrlr.leasetasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.leasetasklist.length,
                        itemBuilder: (context, index) {
                          return leasetaskitem(context, ctrlr.leasetasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget leasebatchrenttasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            myApprovalsCntrlr.taskListSearchController.text = '';
            Get.find<MyApprovalsController>().fetchLeaseBatchRentUsertaskList(entitytype, searchtext: '');
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.leasebatchrenttasklist != null && ctrlr.leasebatchrenttasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.leasebatchrenttasklist.length,
                        itemBuilder: (context, index) {
                          return leasebatchrenttaskitem(context, ctrlr.leasebatchrenttasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget leaseotptasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            myApprovalsCntrlr.taskListSearchController.text = '';
            Get.find<MyApprovalsController>().fetchLeaseOtpUsertaskList(entitytype, searchtext: '');
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.leaseotptasklist != null && ctrlr.leaseotptasklist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.leaseotptasklist.length,
                        itemBuilder: (context, index) {
                          return leaseotptaskitem(context, ctrlr.leaseotptasklist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget leasrecurcosttasks(BuildContext context) {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshUsertaskslist,
        child: GetX<MyApprovalsController>(
          initState: (state) {
            myApprovalsCntrlr.taskListSearchController.text = '';
            Get.find<MyApprovalsController>().fetchLeaseRecurCosttaskList(entitytype, searchtext: '');
          },
          builder: (ctrlr) {
            return ctrlr.isloading.value
                ? ProgressIndicatorCust()
                : ctrlr.leaserecurringcostlist != null && ctrlr.leaserecurringcostlist.isNotEmpty
                    ? ListView.builder(
                        itemCount: ctrlr.leaserecurringcostlist.length,
                        itemBuilder: (context, index) {
                          return leaserecurrcosttaskitem(context, ctrlr.leaserecurringcostlist[index]);
                        })
                    : Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                      );
          },
        ),
      ),
    );
  }

  Widget taskAttribute(String label, String val) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    return Container(
        margin: EdgeInsets.symmetric(vertical: 4, horizontal: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              label,
              style: TextStyle(color: primary, fontSize: 12, letterSpacing: .1),
            ),
            Text(
              val,
              style: TextStyle(color: primary, fontSize: 12, letterSpacing: .1),
            ),
          ],
        ));
  }

  Widget whoCol(String pers, String date) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    return Column(
      children: [
        Row(children: [
          Icon(
            Icons.person,
            color: secondary,
            size: 15,
          ),
          SizedBox(
            width: 5,
          ),
          Text(pers ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
        ]),
        SizedBox(
          height: 2,
        ),
        Row(children: [
          Icon(
            Icons.date_range,
            color: secondary,
            size: 15,
          ),
          SizedBox(
            width: 5,
          ),
          Text(date ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
        ]),
      ],
    );
  }

  Widget projtaskitem(BuildContext context, ProjectPendingTask p) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'PROJECT',
                entitylabel: entitylabel,
                projecttask: p,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${p.projectName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    ComponentUtils.listVertRow('Entity', p.entityType ?? ''),
                    ComponentUtils.listVertRow('Project Id', p.projectId?.toString() ?? ''),
                    ComponentUtils.listVertRow('Start date', p.startTime_?.toString() ?? ''),
                    ComponentUtils.listVertRow('Forecast', p.forecast?.toString() ?? ''),
                    ComponentUtils.listVertRow('Approved Budget', p.approvedbudget?.toString() ?? '0.00', dtype: 'num'),
                    // taskAttribute('Creation Date', p.creationDate ?? '')
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget budgettaskitem(BuildContext context, BudgetPendingTask b) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'BUDGET',
                entitylabel: entitylabel,
                budgettask: b,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${b.projectName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    ComponentUtils.listVertRow('Entity', b.entityType ?? ''),
                    ComponentUtils.listVertRow('Project Name', b.projectName ?? ''),
                    ComponentUtils.listVertRow('Initial Budget', b.forecast?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('Approved Budget', b.approvedbudget?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('Project Manager', b.realEstateManager ?? ''),
                    ComponentUtils.listVertRow('Start Time', b.startTime_ ?? ''),
                    //taskAttribute('Creation Date', b.creationDate ?? '')
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget budgetchgreqtaskitem(BuildContext context, BudgetChgReqTaskData b) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'BUDGETCO',
                entitylabel: entitylabel,
                budgetchgreqtask: b,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${b.projectName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    ComponentUtils.listVertRow('Budget Change Number', b.budgetChgNumber ?? ''),
                    ComponentUtils.listVertRow('Change Reason', b.chgReasonDesc ?? ''),
                    ComponentUtils.listVertRow('Change Order Date', b.chgOrderDate ?? ''),
                    ComponentUtils.listVertRow('Release Amount', b.releaseAmount?.toString() ?? '0.00', dtype: 'num'),
                    //taskAttribute('Approved Budget', b.approvedbudget?.toString() ?? '0.00'),
                    //taskAttribute('Project Manager', b.realEstateManager ?? ''),
                    ComponentUtils.listVertRow('Start Time', b.creationDate ?? ''),
                    //taskAttribute('Creation Date', b.creationDate ?? '')
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget potaskitem(BuildContext context, PoPendingTask p) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'PO',
                entitylabel: entitylabel,
                potask: p,
                docview: true,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${p.projectName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    ComponentUtils.listVertRow('PO Number', p.poNumber ?? ''),
                    ComponentUtils.listVertRow('Amount', p.amount?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('Currency', p.currency ?? ''),
                    ComponentUtils.listVertRow('Vendor', p.supplierName ?? ''),
                    ComponentUtils.listVertRow('Start Time', p.startTime_ ?? ''),
                    //whoCol(p.createdBy ?? '', p.creationDate ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget cotaskitem(BuildContext context, CoPendingTask c) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'CO',
                entitylabel: entitylabel,
                cotask: c,
                docview: true,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${c.projectName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    ComponentUtils.listVertRow('CO Number', c.chgOrderNumber ?? ''),
                    ComponentUtils.listVertRow('Amount', c.amount?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('PO Number', c.poNumber ?? ''),
                    ComponentUtils.listVertRow('Project Name', c.projectName ?? ''),
                    ComponentUtils.listVertRow('Vendor', c.supplierName ?? ''),
                    ComponentUtils.listVertRow('Start Date', c.startTime_ ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget invoicetaskitem(BuildContext context, InvoicePendingTask i) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'INVOICE',
                entitylabel: entitylabel,
                invtask: i,
                docview: true,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${i.projectName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    ComponentUtils.listVertRow('Invoice Number', i.invoiceNumber ?? ''),
                    ComponentUtils.listVertRow('Amount', i.amount?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('PO Number', i.poNumber ?? ''),
                    ComponentUtils.listVertRow('Vendor', i.supplierName ?? ''),
                    ComponentUtils.listVertRow('Created By', i.projectName ?? ''),
                    ComponentUtils.listVertRow('Start Date', i.startTime_ ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget leaseotptaskitem(BuildContext context, LeaseOtpPendingTask l) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'LEASEOTP',
                entitylabel: entitylabel,
                leaseotptask: l,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${l.leaseName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    taskAttribute('Lease Number', l.leaseNum ?? ''),

                    taskAttribute('City State', l.citystate ?? ''),
                    taskAttribute('Payment Type', l.paymentTypeDesc ?? ''),
                    taskAttribute('Gross Amount', ComponentUtils.convertNumberFormat(l.grossAmount?.toString() ?? '0.0')),

                    //taskAttribute('Start Date', l.startTime_ ?? ''),
                    taskAttribute('Effective Date', l.effectiveDate ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget leasebatchrenttaskitem(BuildContext context, LeaseBatchRentPendingTask l) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.put(LeaseBatchPayRentSummeryController());
          Get.put(LeaseBatchRentBillSummeryController());
          Get.put(LeasePaymentsforBatchRentController());
          Get.put(LeaseReceivablesforBatchRentController());
          Get.to(() => TaskDetails(
                entitytype: 'LEASEBATCH',
                entitylabel: entitylabel,
                leasebatchrenttask: l,
                leasebatchview: true,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${l.batchNumber}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    taskAttribute('Lease Financials Group', l.leaseFinancialsGroup ?? ''),
                    taskAttribute('Lease Payment Batch Id', '${l.leasePaymentBatchId ?? ''}'),
                    taskAttribute('Batch Total', ComponentUtils.convertNumberFormat(l.batchTotal?.toString() ?? '0.0')),
                    taskAttribute('Status', '${l.statusDesc ?? ''}'),
                    //taskAttribute('Termination Date', l.leaseTerminationDate ?? ''),
                    //taskAttribute('Start Date', l.startTime_ ?? ''),
                    // taskAttribute('Creation Date', l.creationDate ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget leasetaskitem(BuildContext context, LeasePendingTask l) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'LEASE',
                entitylabel: entitylabel,
                leasetask: l,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${l.name}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    taskAttribute('Lease Number', l.leaseNum ?? ''),
                    taskAttribute('Lease Name', l.name ?? ''),
                    taskAttribute('City State', l.citystate ?? ''),
                    taskAttribute('Termination Date', l.leaseTerminationDate ?? ''),
                    taskAttribute('Start Date', l.startTime_ ?? ''),
                    // taskAttribute('Creation Date', l.creationDate ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget leaserecurrcosttaskitem(BuildContext context, LeaseRecurringCostTask l) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'LEASEREC',
                entitylabel: entitylabel,
                leaserecurringcosttask: l,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${l.name}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    taskAttribute('Lease Number', l.leaseNum ?? ''),
                    taskAttribute('Lease Name', l.name ?? ''),
                    taskAttribute('City State', l.citystate ?? ''),
                    taskAttribute('Period Amount', ComponentUtils.convertNumberFormat(l.amout?.toString() ?? '0.0')),
                    taskAttribute('Start Date', l.startTime_ ?? ''),
                    // taskAttribute('Creation Date', l.creationDate ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget doctaskitem(BuildContext context, DocumentPendingTask d) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'DOCUMENT',
                entitylabel: entitylabel,
                doctask: d,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      d.fileName ?? '',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    taskAttribute('Folder Name', d.folderName ?? ''),
                    taskAttribute('Description', d.description ?? ''),
                    taskAttribute('Entity', d.entityType ?? ''),
                    taskAttribute('Entity Id', d.entityId?.toString() ?? ''),
                    if (d.entityType?.toUpperCase() == 'PROJECT') taskAttribute('Entity Name', d.project_name ?? ''),
                    if (d.entityType?.toUpperCase() == 'SITE') taskAttribute('Entity Name', d.site_name ?? ''),
                    if (d.entityType?.toUpperCase() == 'STORE' || d.entityType?.toUpperCase() == 'BUILDING')
                      taskAttribute('Entity Name', d.store_name ?? ''),
                    if (d.entityType?.toUpperCase() == 'LEASE') taskAttribute('Entity Name', d.lease_name ?? ''),
                    taskAttribute('Document Type', d.docTypeDesc ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget sitetaskitem(BuildContext context, SitePendingTask s) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => TaskDetails(
                entitytype: 'SITE',
                entitylabel: entitylabel,
                sitetask: s,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      s.siteName ?? '',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 5.0,
                    ),
                    taskAttribute('Entity Id', s.entityid?.toString() ?? ''),
                    taskAttribute('Address', s.address?.toString() ?? ''),
                    taskAttribute('Status', s.dealStatusDesc?.toString() ?? ''),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
