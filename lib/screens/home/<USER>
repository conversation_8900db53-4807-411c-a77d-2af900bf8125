import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/drawer_widget.dart';
import 'package:tangoworkplace/screens/ta_admin/change_context_screen.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

class SectionTile extends StatelessWidget {
  final Widget? child;
  String? containerhdr;
  SectionTile({this.child, this.containerhdr});
  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    bool isLargeScreen = CommonUtils.isTablet(context);
    return Container(
        padding: EdgeInsets.all(5),
        width: size.width,
        margin: EdgeInsets.fromLTRB(10, 5, 10, 5),
        // height: 200,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.0),
            boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 5, offset: Offset(0, 0))]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (containerhdr != null)
              Text(
                containerhdr!,
                style: TextStyle(
                  // color: Colors.grey,
                  fontSize: isLargeScreen ? 25 : 15,
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (containerhdr != null)
              const SizedBox(
                height: 1,
              ),
            child!
          ],
        ));

    // return Container(
    //   padding: EdgeInsets.all(15),
    //   margin: EdgeInsets.fromLTRB(10, 5, 10, 5),
    //   width: size.width,
    //   decoration: BoxDecoration(
    //     border: Border.all(
    //       color: HexColor('#4C4B5D').withOpacity(0.2),
    //       width: 1,
    //     ),
    //     borderRadius: BorderRadius.circular(15),
    //   ),
    //   child: child,
    // );
  }
}

class HomeTile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return SingleChildScrollView(
      padding: EdgeInsets.fromLTRB(10, 22, 10, 5),
      child: Column(
        children: <Widget>[
          Container(
            padding: EdgeInsets.all(15),
            margin: EdgeInsets.fromLTRB(10, 5, 10, 5),
            width: size.width,
            decoration: BoxDecoration(
                border: Border.all(color: HexColor('#4C4B5D').withOpacity(0.2), width: 1), borderRadius: BorderRadius.circular(15)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text('Maps',
                    //style: TextStyle(color: Color(0xff003366),
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    )),
                SizedBox(
                  height: 10,
                ),
                Container(
                    //padding: EdgeInsets.all(10),
                    child: new GridView.count(
                  shrinkWrap: true,
                  crossAxisCount: 2,
                  physics: NeverScrollableScrollPhysics(),
                  children: <Widget>[
                    MenuItemCard(
                        name: 'LOCATE',
                        imagePath: "assets/icons/globe.png",
                        onTap: () {
                          //Navigator.of(context).push(MaterialPageRoute(builder: (context) => LocatePage()));
                        }),
                    MenuItemCard(
                        name: 'MAPPING',
                        imagePath: "assets/icons/mappings.png",
                        onTap: () {
                          //Navigator.of(context).push(MaterialPageRoute(builder: (context) => MappingPage()));
                        }),
                  ],
                )),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(15),
            margin: EdgeInsets.fromLTRB(10, 5, 10, 5),
            width: size.width,
            decoration: BoxDecoration(
                border: Border.all(color: HexColor('#4C4B5D').withOpacity(0.2), width: 1), borderRadius: BorderRadius.circular(15)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Project Management',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                    //padding: EdgeInsets.all(10),
                    child: new GridView.count(
                  shrinkWrap: true,
                  crossAxisCount: 2,
                  physics: NeverScrollableScrollPhysics(),
                  children: <Widget>[
                    MenuItemCard(name: 'PROGRAMS', imagePath: "assets/icons/programs.png", onTap: () {}),
                    MenuItemCard(name: 'PROJECTS', imagePath: "assets/icons/projects.png", onTap: () {}),
                    MenuItemCard(name: 'DASHBOARD', imagePath: "assets/icons/charts.png", onTap: () {}),
                    MenuItemCard(name: 'SUPPLIERS', imagePath: "assets/icons/vendors.png", onTap: () {}),
                  ],
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class MenuItemCard extends StatelessWidget {
  final String? name;
  final String? imagePath;
  final VoidCallback? onTap;

  MenuItemCard({Key? key, this.name, this.imagePath, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(7.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: InkWell(
        onTap: onTap,
        splashColor: Colors.red,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              new Container(
                padding: EdgeInsets.fromLTRB(1, 1, 1, 6),
                child: Image(
                  image: new AssetImage(imagePath!),
                  width: 70.0,
                  height: 70.0,
                  color: null,
                  fit: BoxFit.fill,
                  alignment: Alignment.center,
                ),
              ),
              Text(name!, style: new TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold, color: HexColor('#65679a')))
            ],
          ),
        ),
      ),
    );
  }
}

// class Homedrawer extends StatelessWidget {
 
//   @override
//   Widget build(BuildContext context) {
//      bool isLargeScreen = CommonUtils.isTablet(context);
//     return Drawer(
//           child: ListView(
//             // Important: Remove any padding from the ListView.
//             padding: EdgeInsets.zero,
//             children: <Widget>[
//               SizedBox(
//                 height: 80.0,
//                 child: DrawerHeader(
//                     child: new Text('', style: TextStyle(color: Colors.white)),
//                     decoration: new BoxDecoration(color: Colors.grey),
//                     margin: EdgeInsets.zero,
//                     padding: EdgeInsets.zero),
//               ),
//               createDrawerBodyItem(
//                 icon: Icons.settings_backup_restore_rounded,
//                 text: 'Country',
//                 onTap: () {
//                   Navigator.pop(context);
//                   isLargeScreen ? showChangeContextDialog(context) : Navigator.pushNamed(context, TaChange.routName);
//                 },
//               ),
//               Divider(),
//               createDrawerBodyItem(
//                   icon: Icons.settings, text: 'Settings', onTap: () => Navigator.pushNamed(context, SettingsForm.routName)),
//               Divider(),
//               ListTile(
//                   title: Row(
//                     children: <Widget>[
//                       Icon(Icons.logout),
//                       Padding(
//                         padding: EdgeInsets.only(left: 8.0),
//                         child: Text('Log out'),
//                       )
//                     ],
//                   ),
//                   onTap: () {
//                     // AppResourceBundleImpl().clear();
//                     // _pageController.dispose();
//                     CommonUtils.logout(context);
//                   }),
//               Divider()
//             ],
//           ), // Populate the Drawer in the next step.
//         );
//   }
// }
