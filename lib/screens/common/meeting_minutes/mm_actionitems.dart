import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/adhoctemplate.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../models/common/adhoctask.dart';
import '../../../providers/common/meetingminute/mmActionItems_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';
import '../../../utils/device_util.dart';
import 'mm_actionItem_details.dart';

class MeetingMinuteActionItems extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  final String? parentEntityType;
  final int? parentEntityId;
  final hdrText;
  String? mode;
  MeetingMinuteActionItems({Key? key, this.entityType, this.entityId, this.mode, this.hdrText, this.parentEntityId, this.parentEntityType})
      : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  MmActionItemsController mmActItemsCtrl = Get.put(MmActionItemsController());
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  Future _refreshAdhocTasklist() async {
    mmActItemsCtrl.loadAdhocTasks(entityType: entityType, entityId: entityId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // bottomNavigationBar: bottombuttonbar(),
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrText ?? 'Follow up', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: dataArea(context),
    );
  }

  Widget dataArea(BuildContext context) {
    return listStack(context);
    // (mmActItemsCtrl.isContractor.value == 'N')
    //     ? Obx(() => mmActItemsCtrl.listflag.isTrue ? listStack(context) : templateStack(context))
    //     : listStack(context);
  }

  Widget addTasks() {
    return PopupMenuButton(
      icon: Icon(Icons.more_vert),
      //color: ComponentUtils.primecolor,
      itemBuilder: (context) => [
        PopupMenuItem(
          child: Text("Add Item", style: TextStyle(fontSize: 12, color: ComponentUtils.primecolor)),
          value: 1,
          onTap: () {
            Future.delayed(Duration(seconds: 0), () async {
              mmActItemsCtrl.at_mode.value = 'create';
              mmActItemsCtrl.adhoctask.value = AdhocTask();
              Get.to(() => MeetingMinuteActionItemDetails(
                    entityId: entityId,
                    entityType: entityType, mode: mode,

                    //task: new AdhocTask(),
                  ));
            });
          },
        ),
        PopupMenuItem(
          child: Text("Add from Template", style: TextStyle(fontSize: 12, color: ComponentUtils.primecolor)),
          value: 2,
          onTap: () async {
            await mmActItemsCtrl.gettemplateslov(parentEntityType);
            await _showtemplates();
          },
        ),
      ],
    );
  }

  _showtemplates() async {
    return Get.defaultDialog(
      title: 'Add from Template',
      titleStyle: TextStyle(fontSize: 16),
      content: Column(
        children: [
          await templateform(Get.context),
        ],
      ),
    );
  }

  Widget templateStack(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: <Widget>[
        templateform(context),
      ],
    );
  }

  Widget listStack(BuildContext context) {
    return Column(children: <Widget>[
      if (mmActItemsCtrl.isContractor.value == 'N' && mode == 'edit')
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              children: [
                SizedBox(
                  width: 15,
                ),
                addTasks(),
              ],
            ),
          ],
        ),
      Expanded(
        child: RefreshIndicator(
          onRefresh: _refreshAdhocTasklist,
          child: GetX<MmActionItemsController>(
            initState: (state) {
              debugPrint('------------initstate-----------');
              mmActItemsCtrl.loadAdhocTasks(entityType: entityType, entityId: entityId, isContractor: mmActItemsCtrl.isContractor.value);
            },
            builder: (_) {
              return _.isloading.isTrue
                  ? ProgressIndicatorCust()
                  : _.adhoctaskdata.length < 1
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.adhoctaskdata[index]);
                          },
                          itemCount: _.adhoctaskdata.length,
                        );
            },
          ),
        ),
      ),
      // Positioned(
      //   bottom: 30,
      //   right: 30,
      //   child: FloatingActionButton(
      //     elevation: 5.0,
      //     child: const Icon(
      //       Icons.add,
      //       size: 30,
      //     ),
      //     onPressed: () {
      //       debugPrint('add button----------------------');
      //       Get.to(() => AdhocTaskDetails(
      //             entityId: entityId,
      //             entityType: entityType,
      //             at_mode: 'create',
      //           ));
      //     },
      //   ),
      // ),
    ]);
  }

  Widget filterlov() {
    return Container(
      margin: EdgeInsets.only(top: 5, right: 7),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Obx(
            () => TaDropSown(
              label: 'Select',
              initvalue: mmActItemsCtrl.taskfilter.value,
              items: <DropdownMenuItem<String>>[
                DropdownMenuItem(child: Text("All Tasks", style: TextStyle(fontSize: 12, color: Colors.black)), value: "All"),
                DropdownMenuItem(child: Text("My Tasks", style: TextStyle(fontSize: 12, color: Colors.black)), value: "My"),
              ].map<DropdownMenuItem<String>>((DropdownMenuItem<String> value) {
                return DropdownMenuItem<String>(
                  value: value.value,
                  child: value.child,
                );
              }).toList(),
              onChanged: (val) async {
                mmActItemsCtrl.taskfilter.value = val;
                mmActItemsCtrl.loadAdhocTasks(entityType: entityType, entityId: entityId, filter: val);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, AdhocTask task) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {
        //adhocTaskCtrl.labelsloading.value = true;
        mmActItemsCtrl.adhoctask.value = task;
        mmActItemsCtrl.at_mode.value = 'edit';
        Get.to(() => MeetingMinuteActionItemDetails(
              entityId: entityId,
              entityType: entityType,
              mode: mode,
            ));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Flexible(
                  child: Text(
                    task.taskName ?? '',
                    style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  task.taskSeq?.toString() ?? '',
                  style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ]),
              SizedBox(
                height: 5,
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                // Text(
                //   'task Type:',
                //   style: TextStyle(color: primary, fontSize: 10),
                // ),
                // SizedBox(
                //   width: 10.0,
                // ),
                Text(
                  task.taskTypeDesc?.toString() ?? '',
                  style: TextStyle(color: primary, fontSize: 10),
                ),
                Text(
                  task.status?.toString() ?? '',
                  style: TextStyle(color: primary, fontSize: 10),
                  overflow: TextOverflow.ellipsis,
                ),
              ]),
              SizedBox(
                height: 5,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Icon(
                    Icons.mail,
                    color: primary,
                    size: 15,
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Text(
                    task.assignedTo?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              if (task.actualFinsh != null)
                SizedBox(
                  height: 5.0,
                ),
              if (task.actualFinsh != null)
                Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  Text(
                    task.actualFinsh?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                ]),
            ],
          ),
        ),
      ),
    );
  }

  templateform(BuildContext? context) async {
    return Obx(
      () => mmActItemsCtrl.templateloading.isTrue
          ? ProgressIndicatorCust()
          : Column(
              children: [
                Container(
                  padding: EdgeInsets.only(top: 5),
                  margin: EdgeInsets.fromLTRB(10, 20, 10, 5),
                  width: Get.width,
                  height: 190,
                  // decoration: BoxDecoration(
                  //     color: Colors.white,
                  //     borderRadius: BorderRadius.circular(10.0),
                  //     boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                  // decoration: BoxDecoration(
                  //     border: Border.all(color: HexColor('#4C4B5D').withOpacity(0.2), width: 1), borderRadius: BorderRadius.circular(15)),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      // adhocTaskCtrl.templateloading.value ? ProgressIndicatorCust() :
                      templatesLov(context),
                      SizedBox(
                        height: 40,
                      ),
                      TaButton(
                        type: 'elevate',
                        buttonText: 'Generate AdhocTasks',
                        onPressed: () async {
                          if (mmActItemsCtrl.generatebtnbinding.value) {
                            await mmActItemsCtrl.generateItemsFromTemplate(
                              entityType: entityType,
                              entityId: entityId,
                            );
                            Get.back();
                          } else {
                            ComponentUtils.showsnackbar(text: "Please Select Template...");
                            return null;
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget templatesLov(BuildContext? context) {
    return (mmActItemsCtrl.templatelov.isNotEmpty && mmActItemsCtrl.templatelov != null)
        ? DropdownButton(
            hint: Text(
              'Select Template',
              style: TextStyle(fontSize: 14, color: Colors.black),
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
              mmActItemsCtrl.selectedtemplate.value = newValue;
              mmActItemsCtrl.generatebtnbinding.value = (newValue != null && newValue != '') ? true : false;
            },
            value: mmActItemsCtrl.selectedtemplate.value == '' ? null : mmActItemsCtrl.selectedtemplate.value,
            items: mmActItemsCtrl.templatelov.map((AdhocTemplate t) {
              return DropdownMenuItem(
                child: new Text(
                  t.templateName!,
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
                value: t?.adhocTemplateId?.toString(),
              );
            }).toList(),
          )
        : DropdownButton(
            hint: Text(
              'Select Template',
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
            },
            value: null,
            items: [
              DropdownMenuItem(
                  child: Text(
                    "Select",
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: null),
            ],
          );
  }
}
