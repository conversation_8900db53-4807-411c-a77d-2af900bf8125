import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tacheckbox.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_attendee.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_followup.dart';
import 'package:tangoworkplace/models/common/utils/adhoctasknotifuser.dart';
import 'package:tangoworkplace/models/common/utils/contact.dart';
import 'package:tangoworkplace/models/common/utils/systemuser.dart';
import 'package:tangoworkplace/providers/common/utils/usernotifications_controller.dart';
import 'package:tangoworkplace/providers/common/utils/docattachments_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/utils/usersdata/systemuserdata.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../common/widgets/component_widgets/taforminputdate.dart';
import '../../../models/lookup_values.dart';
import '../../../providers/common/meetingminute/meetingminutes_controller.dart';

class MeetingMinuteFollowups extends StatelessWidget {
  final meetingid;
  final entityid;
  final entitytype;
  final hdrText;
  String? mode;
  MeetingMinuteFollowups({Key? key, this.meetingid, this.mode, this.entityid, this.entitytype, this.hdrText}) : super(key: key);

  MeetingMinutesController mmCtrl = Get.find<MeetingMinutesController>();
  LabelController talabel = Get.find<LabelController>();
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // bottomNavigationBar: bottombuttonbar(),
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrText ?? 'Action Items', style: ComponentUtils.appbartitlestyle),
        //actions: [
        // TextButton(
        //   onPressed: () async {
        //     await mmCtrl.saveAttendees(meetingid);
        //     Get.back();
        //   },
        //   child: Row(
        //     mainAxisSize: MainAxisSize.min,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       Text(
        //         talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
        //         style: ComponentUtils.appbartitlestyle,
        //       ),
        //     ],
        //   ),
        // ),
        //],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Stack(
        children: <Widget>[
          Positioned.fill(
            //child: RefreshIndicator(
            // onRefresh: _refreshComments,
            child: GetX<MeetingMinutesController>(
              initState: (state) {
                mmCtrl.fetchMeetingFollowups(meetingid);
              },
              builder: (_) {
                return _.isFollowupsLoading.isTrue
                    ? ProgressIndicatorCust()
                    : _.followupsdata.length < 1
                        ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return listitem(context, _.followupsdata[index]);
                            },
                            itemCount: _.followupsdata.length,
                          );
              },
            ),
          ),
          if (mode == 'edit')
            Positioned(
              bottom: 30,
              right: 30,
              child: FloatingActionButton(
                elevation: 5.0,
                child: const Icon(
                  Icons.add,
                  size: 30,
                ),
                onPressed: () {
                  mmCtrl.meetingFollowup.value = MeetingFollowup();
                  mmCtrl.fuFollowupDate.text = '';
                  _openDialog();
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget bottombuttonbar() {
    return BottomAppBar(
      child: Container(
        margin: EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            // TaButton(
            //   type: 'elevate',
            //   //buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
            //   buttonText: sysUserText ?? 'System Users',
            //   onPressed: () {
            //     _openDialog('systemusers');
            //   },
            // ),
            SizedBox(
              height: 5.0,
              width: 5.0,
            ),
            TaButton(
              type: 'elevate',
              onPressed: () {
                //  _openDialog('');
              },
            ),
          ],
        ),
      ),
      color: Colors.white,
    );
  }

  Widget listitem(BuildContext context, MeetingFollowup mfu) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          debugPrint('');
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child:
                    // Row(
                    //   crossAxisAlignment: CrossAxisAlignment.center,
                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   mainAxisSize: MainAxisSize.max,
                    //  children: [
                    Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          mfu.itemId?.toString() ?? ' ',
                          style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        // SizedBox(
                        //   width: 2,
                        // ),
                        Text(
                          mfu.title?.toString() ?? '',
                          style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, mainAxisSize: MainAxisSize.max, children: [
                      Row(
                        children: [
                          Text('Issue Type: ',
                              style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1, fontWeight: FontWeight.bold)),
                          Text((mfu.issueTypeDesc?.toString() ?? ''), style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ],
                      ),
                      Row(
                        children: [
                          Text('Risk Level: ',
                              style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1, fontWeight: FontWeight.bold)),
                          Text((mfu.riskLevelDesc?.toString() ?? '    '),
                              style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ],
                      ),
                    ]),
                    SizedBox(
                      height: 5,
                    ),
                    Row(
                      children: [
                        Text('Follow up Needed: ',
                            style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1, fontWeight: FontWeight.bold)),
                        Text((mfu.followupNeededDesc?.toString() ?? ''), style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                      ],
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, mainAxisSize: MainAxisSize.max, children: [
                      Row(
                        children: [
                          Text('Follow up Date: ',
                              style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1, fontWeight: FontWeight.bold)),
                          Text((mfu.followupDate?.toString() ?? ''), style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ],
                      ),
                      Row(
                        children: [
                          Text('Resolved: ',
                              style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1, fontWeight: FontWeight.bold)),
                          Text((mfu.resolvedDesc?.toString() ?? '    '), style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ],
                      ),
                    ]),
                  ],
                ),
                // Column(
                //   crossAxisAlignment: CrossAxisAlignment.end,
                //   children: <Widget>[
                //     IconButton(
                //         onPressed: () {
                //           debugPrint('-----------------');
                //           mmCtrl.meetingattendeesdata.removeWhere((element) => element.emailAddress == ma.emailAddress);
                //         },
                //         icon: Icon(
                //           Icons.delete,
                //           color: secondary,
                //         )),
                //   ],
                // ),
                // ],
                // ),
              )
            ],
          ),
        ));
  }

  void _openDialog() async {
    Get.defaultDialog(
        title: 'Add Follow up',
        titleStyle: TextStyle(fontSize: 16),
        content: Container(
          height: 300,
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: followupForm(),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  mmCtrl.meetingFollowup.value = MeetingFollowup();
                  mmCtrl.fuFollowupDate.text = '';
                  Get.back();
                },
              ),
              SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                onPressed: () {
                  final isValid = _formKey.currentState!.validate();
                  if (!isValid) {
                    return;
                  }

                  _formKey.currentState!.save();
                  mmCtrl.onSaveFollowup(meetingid);
                },
              ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  Widget followupForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Obx(
        () => Column(
          children: <Widget>[
            TaFormDropdown(
              label: 'Issue Type',
              emptttext: 'Select',

              onChanged: (newValue) {
                debugPrint('newValue          ' + newValue.lookupCode);
                mmCtrl.meetingFollowup.value.issueType = newValue.lookupCode;
                mmCtrl.meetingFollowup.value.issueTypeDesc =
                    (newValue.lookupValue != null && newValue.lookupValue != '') ? newValue.lookupValue : '';
              },
              // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH').ro
              //     ? null
              //     : (newValue) {
              //         debugPrint('newValue          ' + newValue);
              //         statusrepoCtrl.statusreport.value.financialHealth = newValue;
              //       },
              value: null,
              // (statusrepoCtrl.statusreport.value.financialHealth == '' || statusrepoCtrl.statusreport.value.financialHealth == 'null')
              //     ? null
              //     : statusrepoCtrl.statusreport.value.financialHealth,
              listflag: (mmCtrl.fuissueTypelov.value.isNotEmpty && mmCtrl.fuissueTypelov.value != null),
              items: mmCtrl.fuissueTypelov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l,
                );
              }).toList(),
            ),
            TaFormInputText(
              keyboard: TextInputType.number,
              //label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJECTINWEEK').value,
              //readOnly: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJECTINWEEK').ro,
              label: 'Item Id',
              value: mmCtrl.meetingFollowup.value.itemId?.toString() ?? '',
              onChanged: (val) {
                mmCtrl.meetingFollowup.value.itemId = (val != null && val != '') ? int.parse(val) : null;
              },
              onSaved: (val) {
                mmCtrl.meetingFollowup.value.itemId = (val != null && val != '') ? int.parse(val) : null;
              },
            ),
            TaFormInputText(
              //keyboard: TextInputType.number,
              //label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJECTINWEEK').value,
              //readOnly: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJECTINWEEK').ro,
              label: 'Title',
              value: mmCtrl.meetingFollowup.value.title?.toString() ?? '',
              onChanged: (val) {
                mmCtrl.meetingFollowup.value.title = val;
              },
              onSaved: (val) {
                mmCtrl.meetingFollowup.value.title = val;
              },
            ),
            TaFormDropdown(
              label: 'Risk Level',
              emptttext: 'Select',
              onChanged: (newValue) {
                debugPrint('newValue          ' + newValue.lookupCode);
                mmCtrl.meetingFollowup.value.riskLevel = newValue.lookupCode;
                mmCtrl.meetingFollowup.value.riskLevelDesc =
                    (newValue.lookupValue != null && newValue.lookupValue != '') ? newValue.lookupValue : '';
              },
              // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH').ro
              //     ? null
              //     : (newValue) {
              //         debugPrint('newValue          ' + newValue);
              //         statusrepoCtrl.statusreport.value.financialHealth = newValue;
              //       },
              value: null,
              // (statusrepoCtrl.statusreport.value.financialHealth == '' || statusrepoCtrl.statusreport.value.financialHealth == 'null')
              //     ? null
              //     : statusrepoCtrl.statusreport.value.financialHealth,
              listflag: (mmCtrl.fuRiskLevellov.value.isNotEmpty && mmCtrl.fuRiskLevellov.value != null),
              items: mmCtrl.fuRiskLevellov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l,
                );
              }).toList(),
            ),
            TaFormDropdown(
              label: 'Followup Needed',
              emptttext: 'Select',

              onChanged: (newValue) {
                debugPrint('newValue          ' + newValue.lookupCode);
                mmCtrl.meetingFollowup.value.followupNeeded = newValue.lookupCode;
                mmCtrl.meetingFollowup.value.followupNeededDesc =
                    (newValue.lookupValue != null && newValue.lookupValue != '') ? newValue.lookupValue : '';
              },
              // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH').ro
              //     ? null
              //     : (newValue) {
              //         debugPrint('newValue          ' + newValue);
              //         statusrepoCtrl.statusreport.value.financialHealth = newValue;
              //       },
              value: null,
              // (statusrepoCtrl.statusreport.value.financialHealth == '' || statusrepoCtrl.statusreport.value.financialHealth == 'null')
              //     ? null
              //     : statusrepoCtrl.statusreport.value.financialHealth,
              listflag: (mmCtrl.yesnolov.value.isNotEmpty && mmCtrl.yesnolov.value != null),
              items: mmCtrl.yesnolov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l,
                );
              }).toList(),
            ),
            TaFormInputDate(
              //label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEEKENDING').value,
              label: 'Followup Date',
              controller: mmCtrl.fuFollowupDate,
              onChanged: (dateval) {
                debugPrint('Needed By--------- $dateval');
                mmCtrl.fuFollowupDate.text = dateval.toString();
                mmCtrl.meetingFollowup.value.followupDate = dateval.toString();
              },
              //readOnly: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEEKENDING').ro,
              onSaved: (val) {
                mmCtrl.fuFollowupDate.text = val.toString();
                mmCtrl.meetingFollowup.value.followupDate = val.toString();
              },
            ),
            TaFormDropdown(
              label: 'Resolved',
              emptttext: 'Select',

              onChanged: (newValue) {
                debugPrint('newValue          ' + newValue.lookupCode);
                mmCtrl.meetingFollowup.value.resolved = newValue.lookupCode;
                mmCtrl.meetingFollowup.value.resolvedDesc =
                    (newValue.lookupValue != null && newValue.lookupValue != '') ? newValue.lookupValue : '';
              },
              // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH').ro
              //     ? null
              //     : (newValue) {
              //         debugPrint('newValue          ' + newValue);
              //         statusrepoCtrl.statusreport.value.financialHealth = newValue;
              //       },
              value: null,
              // (statusrepoCtrl.statusreport.value.financialHealth == '' || statusrepoCtrl.statusreport.value.financialHealth == 'null')
              //     ? null
              //     : statusrepoCtrl.statusreport.value.financialHealth,
              listflag: (mmCtrl.yesnolov.value.isNotEmpty && mmCtrl.yesnolov.value != null),
              items: mmCtrl.yesnolov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
