import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tacheckbox.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputdate.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/adhoctask.dart';
import 'package:tangoworkplace/models/common/utils/contact.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/ta_admin/entitystatus.dart';
import 'package:tangoworkplace/providers/common/adhoctasks_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/utils/dms/docattachments_screen.dart';
import 'package:tangoworkplace/screens/common/utils/usersdata/systemuserdata.dart';
import 'package:tangoworkplace/screens/common/utils/usersdata/systemusers.dart';
import 'package:tangoworkplace/screens/common/utils/usersdata/usernotifications_screen.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';
import '../../../providers/common/meetingminute/mmActionItems_controller.dart';

class MeetingMinuteActionItemDetails extends StatelessWidget {
  //final AdhocTask task;
  final entityId;
  final entityType;
  String? mode;

  MeetingMinuteActionItemDetails({Key? key, this.entityId, this.entityType, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  MmActionItemsController adhoctaskCntrlr = Get.find<MmActionItemsController>();
  AdhocTask? task;
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GetX<LabelController>(
      initState: (state) {
        adhoctaskCntrlr.labelsloading.value = true;
        adhoctaskCntrlr.isdetailsloading.value = true;

        Future.delayed(Duration(seconds: 1), () async {
          //adhoctaskCntrlr.labelsloading.value = true;
          // await adhoctaskCntrlr.getAdhocTaskId(at_mode);
          task = adhoctaskCntrlr.adhoctask.value;
          //await adhoctaskCntrlr.getlabels(talabel);
          await adhoctaskCntrlr.setCurrentRow();
          adhoctaskCntrlr.labelsloading.value = false;
          adhoctaskCntrlr.isdetailsloading.value = false;
        });
      },
      builder: (_) {
        return (adhoctaskCntrlr.labelsloading.value || adhoctaskCntrlr.isdetailsloading.value)
            ? ComponentUtils.labelLoadScaffold()
            : Scaffold(
                appBar: AppBar(
                  iconTheme: Theme.of(context).appBarTheme.iconTheme,
                  title: Text('Details', //Text(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS')?.value ?? '',
                      style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
                      ),
                  backgroundColor: Colors.white,
                  elevation: 5,
                  leading: new IconButton(
                    icon: ComponentUtils.backpageIcon,
                    color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                    onPressed: () {
                      debugPrint('------back-------');
                      Get.back();
                      adhoctaskCntrlr.loadAdhocTasks(entityType: entityType, entityId: entityId);
                    },
                  ),
                ),
                body: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: adhoctaskDetForm(),
                ),
                bottomNavigationBar: bottombuttonbar(_formKey),
              );
      },
    );
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      child: Obx(
        () => Container(
          margin: EdgeInsets.only(left: 12.0, right: 12.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  Get.back();
                  adhoctaskCntrlr.loadAdhocTasks(entityType: entityType, entityId: entityId);
                },
              ),
              if (mode == 'edit')
                const SizedBox(
                  height: 5.0,
                  width: 5.0,
                ),
              if (mode == 'edit')
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                  onPressed: () {
                    final isValid = fkey.currentState!.validate();
                    if (!isValid) {
                      return;
                    }

                    _formKey.currentState!.save();
                    debugPrint('------------------saved--------------------');
                    adhoctaskCntrlr.onDetFormsave(task!, entityType, entityId, 'Save');
                  },
                ),
              if (mode == 'edit')
                SizedBox(
                  height: 5.0,
                  width: 5.0,
                ),
              if (mode == 'edit')
                if (adhoctaskCntrlr.at_mode.value == 'aaaaaaaaaa')
                  TaButton(
                    type: 'elevate',
                    buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SUBMIT')?.value ?? 'Submit',
                    onPressed: () {
                      final isValid = fkey.currentState!.validate();
                      if (!isValid) {
                        return;
                      }

                      _formKey.currentState!.save();
                      if (adhoctaskCntrlr.status.value == 'Draft') {
                        ComponentUtils.showpopup(msg: 'Item cannot be saved and assigned to user in Draft status', type: 'Error');
                      } else {
                        debugPrint('------------------saved--------------------');
                        adhoctaskCntrlr.emailbodyCtrl.text = '';
                        adhoctaskCntrlr.includemecheck.value = false;

                        if (adhoctaskCntrlr.sendNotifications.value) {
                          Get.defaultDialog(
                              title: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFDATA')?.value ?? 'Notification Data',
                              titleStyle: TextStyle(fontSize: 16),
                              content: Column(
                                children: [
                                  TaInputTextField(
                                    title: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFMESSAGE')?.value ?? 'body',
                                    controller: adhoctaskCntrlr.emailbodyCtrl,
                                    minLines: 3,
                                    keyboard: TextInputType.multiline,
                                    textInputAct: TextInputAction.newline,
                                    maxLines: 15,
                                  ),
                                  TaFormCheckBoxicon(
                                    label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_INCLUDEME')?.value ?? 'include me',
                                    enabled: true,
                                    value: adhoctaskCntrlr.includemecheck.value,
                                    onChanged: (val) {
                                      adhoctaskCntrlr.includemecheck.value = val!;
                                    },
                                  ),
                                ],
                              ),
                              actions: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TaButton(
                                      type: 'elevate',
                                      buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                                      onPressed: () {
                                        Get.back();
                                      },
                                    ),
                                    SizedBox(
                                      width: 4.0,
                                    ),
                                    TaButton(
                                      type: 'elevate',
                                      buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_ADD')?.value ?? 'Add',
                                      onPressed: () {
                                        debugPrint('-----------');
                                        adhoctaskCntrlr.onDetFormsave(task!, entityType, entityId, 'Submit');
                                      },
                                    ),
                                    SizedBox(
                                      width: 7.0,
                                    ),
                                  ],
                                ),
                              ]);
                        } else {
                          adhoctaskCntrlr.onDetFormsave(task!, entityType, entityId, 'Submit');
                        }
                      }
                    },
                  ),
            ],
          ),
        ),
      ),
      color: Colors.white,
    );
  }

  Widget adhoctaskDetForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Obx(
        () => Column(
          children: <Widget>[
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSEQ') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSEQ')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSEQ')!.ro : true,
                value: task?.taskSeq?.toString() ?? '',
                keyboard: TextInputType.number,
                // onChanged: (val) {
                //   if (val != null) adhoctaskCntrlr.taskseq.value = int.parse(val);
                // },
                onSaved: (val) {
                  //adhoctaskCntrlr.taskseq.value = val!;
                  adhoctaskCntrlr.taskseq.value = ((val != null && val != '') ? int.parse(val) : 0);
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKTYPE') != null) tasktypeLov(Get.context),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKNAME') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKNAME')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKNAME')!.ro : true,
                value: task?.taskName ?? '',
                onSaved: (val) {
                  adhoctaskCntrlr.taskname.value = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKDESC') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKDESC')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKDESC')!.ro : true,
                value: adhoctaskCntrlr.taskdesc.value ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  adhoctaskCntrlr.taskdesc.value = val;
                },
                onSaved: (val) {
                  adhoctaskCntrlr.taskdesc.value = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_STATUS') != null) statusLov(Get.context),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_PRIORITY') != null) priorityLov(Get.context),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART') != null)
              TaFormInputDate(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.ro : true,
                controller: adhoctaskCntrlr.startdate,
                onChanged: (dateval) {
                  debugPrint('startdate--------- $dateval');
                  adhoctaskCntrlr.startdate.text = dateval.toString();
                },
                validate: (value) {
                  if ((talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.req) == 'TRUE' &&
                      (value == null || value == '')) {
                    return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.value! + ' is Required';
                  } else if ((adhoctaskCntrlr.finishdate.text != null &&
                              adhoctaskCntrlr.finishdate.text != '' &&
                              adhoctaskCntrlr.finishdate.text.isNotEmpty ||
                          adhoctaskCntrlr.actualfinish.text != null &&
                              adhoctaskCntrlr.actualfinish.text != '' &&
                              adhoctaskCntrlr.actualfinish.text.isNotEmpty) &&
                      value != null &&
                      value != '' &&
                      value.isNotEmpty) {
                    DateFormat dateFormat = DateFormat("MM/dd/yyyy");
                    DateTime taskstartdt = dateFormat.parse(value);
                    DateTime? finishdt;
                    DateTime? actualfinishdt;

                    debugPrint('taskstartdt  $taskstartdt   finishdt   $finishdt    actualfinishdt    $actualfinishdt');
                    if (adhoctaskCntrlr.finishdate.text != null &&
                        adhoctaskCntrlr.finishdate.text != '' &&
                        adhoctaskCntrlr.finishdate.text.isNotEmpty) finishdt = dateFormat.parse(adhoctaskCntrlr.finishdate.text);
                    if (adhoctaskCntrlr.actualfinish.text != null &&
                        adhoctaskCntrlr.actualfinish.text != '' &&
                        adhoctaskCntrlr.actualfinish.text.isNotEmpty) actualfinishdt = dateFormat.parse(adhoctaskCntrlr.actualfinish.text);

                    if (finishdt != null && actualfinishdt != null) {
                      //if (finishdt.isBefore(actualfinishdt) || finishdt.isAtSameMomentAs(actualfinishdt)) {
                      if ((actualfinishdt.isAfter(finishdt) || actualfinishdt.isAtSameMomentAs(finishdt)) && taskstartdt.isAfter(finishdt))
                        return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.value! +
                            ' must be on or before ' +
                            adhoctaskCntrlr.finishdate.text;
                      // } else {
                      if ((actualfinishdt.isAfter(actualfinishdt) || actualfinishdt.isAtSameMomentAs(finishdt)) &&
                          taskstartdt.isAfter(actualfinishdt))
                        return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.value! +
                            ' must be on or before ' +
                            adhoctaskCntrlr.actualfinish.text;
                      // }
                    } else {
                      if (finishdt != null && taskstartdt.isAfter(finishdt)) {
                        return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.value! +
                            ' must be on or before ' +
                            adhoctaskCntrlr.finishdate.text;
                      }
                      if (actualfinishdt != null && taskstartdt.isAfter(actualfinishdt)) {
                        return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKSTART')!.value! +
                            ' must be on or before ' +
                            adhoctaskCntrlr.actualfinish.text;
                      }
                    }
                  }

                  return null;
                },
                onSaved: (val) {
                  adhoctaskCntrlr.startdate.text = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKFINISH') != null)
              TaFormInputDate(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKFINISH')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKFINISH')!.ro : true,
                controller: adhoctaskCntrlr.finishdate,
                onChanged: (dateval) {
                  debugPrint('finishdate--------- $dateval');
                  adhoctaskCntrlr.finishdate.text = dateval.toString();
                },
                onSaved: (val) {
                  adhoctaskCntrlr.finishdate.text = val;
                },
                validate: (value) {
                  if ((talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKFINISH')!.req) == 'TRUE' &&
                      (value == null || value == '')) {
                    return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKFINISH')!.value! + ' is Required';
                  } else if (adhoctaskCntrlr.startdate.text != null &&
                      adhoctaskCntrlr.startdate.text != '' &&
                      adhoctaskCntrlr.startdate.text.isNotEmpty &&
                      value != null &&
                      value != '' &&
                      value.isNotEmpty) {
                    DateFormat dateFormat = DateFormat("MM/dd/yyyy");
                    DateTime taskstartdt = dateFormat.parse(adhoctaskCntrlr.startdate.text);
                    DateTime finishdt = dateFormat.parse(value);
                    if (finishdt.isBefore(taskstartdt))
                      return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKFINISH')!.value! +
                          ' must be on or after ' +
                          adhoctaskCntrlr.startdate.text;
                  }
                  return null;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ACTUALFINISH') != null)
              TaFormInputDate(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ACTUALFINISH')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ACTUALFINISH')!.ro : true,
                controller: adhoctaskCntrlr.actualfinish,
                onChanged: (dateval) {
                  debugPrint('actualfinish--------- $dateval');
                  adhoctaskCntrlr.actualfinish.text = dateval.toString();
                },
                onSaved: (val) {
                  adhoctaskCntrlr.actualfinish.text = val;
                },
                validate: (value) {
                  if ((talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ACTUALFINISH')!.req) == 'TRUE' &&
                      (value == null || value == '')) {
                    return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ACTUALFINISH')!.value! + ' is Required';
                  } else if (adhoctaskCntrlr.startdate.text != null &&
                      adhoctaskCntrlr.startdate.text != '' &&
                      adhoctaskCntrlr.startdate.text.isNotEmpty &&
                      value != null &&
                      value != '' &&
                      value.isNotEmpty) {
                    DateFormat dateFormat = DateFormat("MM/dd/yyyy");
                    DateTime taskstartdt = dateFormat.parse(adhoctaskCntrlr.startdate.text);
                    DateTime finishdt = dateFormat.parse(value);
                    if (finishdt.isBefore(taskstartdt))
                      return talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ACTUALFINISH')!.value! +
                          ' must be on or after ' +
                          adhoctaskCntrlr.startdate.text;
                  }
                  return null;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ASSIGNEDTO') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ASSIGNEDTO')!.value,
                readOnly: true,
                //readOnly: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_ASSIGNEDTO').ro,
                //value: adhoctaskCntrlr.assignedto.value ?? '',
                controller: adhoctaskCntrlr.assignToCtrl,
                // keyboard: TextInputType.emailAddress,
                onSaved: (val) {
                  adhoctaskCntrlr.assignToCtrl.text = val;
                },
                suffixbutton: IconButton(
                  onPressed: () {
                    Get.to(() => SystemUsers(
                          source: 'MM_ADHOC_ASSIGNTO',
                          mmadhoctaskCntrlr: adhoctaskCntrlr,
                        ));
                  },
                  icon: Icon(Icons.arrow_forward_ios),
                ),
                // validate: (val) {
                //   if (!GetUtils.isEmail(val)) {
                //     return 'Provide valid Email';
                //   }
                //   return null;
                // },
              ),
            // if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_DOCUMENTS') != null && adhoctaskCntrlr.at_mode.value != 'create')
            //    documents(),
            // if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_SENDEMAIL') != null && adhoctaskCntrlr.at_mode.value != 'create')
            //   TaFormCheckBoxicon(
            //     label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_SENDEMAIL').value,
            //     enabled: true,
            //     value: adhoctaskCntrlr.sendemail.value,
            //     onSaved: (val) {
            //       adhoctaskCntrlr.sendemail.value = val;
            //     },
            //   ),
            // if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_SENDNOTIFICATION') != null &&
            //     adhoctaskCntrlr.at_mode.value != 'create' &&
            //     talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATIONUSERS') != null)
            //   TaFormCheckBoxicon(
            //     label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_SENDNOTIFICATION').value,
            //     enabled: true,
            //     value: adhoctaskCntrlr.sendNotifications.value,
            //     onChanged: (val) {
            //       debugPrint('val---------${val}');
            //       adhoctaskCntrlr.sendNotifications.value = val;

            //       // adhoctaskCntrlr.taskdesc.value = 'testing now';

            //       // debugPrint('val---------${adhoctaskCntrlr.taskdesc.value}');
            //     },
            //     onSaved: (val) {
            //       debugPrint('val---------$val');
            //       adhoctaskCntrlr.sendNotifications.value = val;
            //     },
            //   ),
            //if (adhoctaskCntrlr.sendNotifications.value) sendNotification(),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION1') != null &&
                adhoctaskCntrlr.at_mode.value != 'create')
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION1')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION1')!.ro : true,
                value: task?.notification1?.toString() ?? '',
                keyboard: TextInputType.number,
                onSaved: (val) {
                  if (val != null && val != '') adhoctaskCntrlr.notif1.value = int.parse(val);
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION2') != null &&
                adhoctaskCntrlr.at_mode.value != 'create')
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION2')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION2')!.ro : true,
                value: task?.notification2?.toString() ?? '',
                keyboard: TextInputType.number,
                onSaved: (val) {
                  if (val != null && val != '') adhoctaskCntrlr.notif2.value = int.parse(val);
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION3') != null &&
                adhoctaskCntrlr.at_mode.value != 'create')
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION3')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATION3')!.ro : true,
                value: task?.notification3?.toString() ?? '',
                keyboard: TextInputType.number,
                onSaved: (val) {
                  if (val != null && val != '') adhoctaskCntrlr.notif3.value = int.parse(val);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget documents() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_DOCUMENTS')?.value ?? 'Documents'),
        IconButton(
            onPressed: () {
              debugPrint('assocfiles--- ${adhoctaskCntrlr.associatedfileids.value}');
              Get.to(() => DocAttachments(
                    entityid: entityId,
                    mode: mode,
                    entitytype: entityType,
                    subentityid: task!.adhocTaskId,
                    subentitytype: 'ADHOC_TASK',
                    fileidlist: adhoctaskCntrlr.associatedfileids.value,
                    hdrText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_DOCUMENTS')?.value,
                    attachmentTabText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_DOCATTACHEMNTS')?.value,
                    uploadedTabText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_DOCUPLOADED')?.value,
                  ));
            },
            icon: Icon(
              Icons.file_present,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget sendNotification() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATIONUSERS')!.value!),
        IconButton(
            onPressed: () {
              Get.to(() => UserNotifications(
                    sid: task!.adhocTaskId,
                    entityid: task!.entityId,
                    entitytype: task!.entityType,
                    contactsText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_CONTACTS')?.value ?? 'Contacts',
                    hdrText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_NOTIFICATIONUSERS')?.value ?? 'Notifications Users',
                    sysUserText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_SYSTEMUSERS')?.value ?? 'System Users',
                  ));
            },
            icon: Icon(
              Icons.people,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget tasktypeLov(BuildContext? context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        child: (adhoctaskCntrlr.tasktypelov.isNotEmpty && adhoctaskCntrlr.tasktypelov != null)
            ? DropdownButtonFormField(
                hint: Text(
                  'Select ',
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
                onChanged: mode == 'edit'
                    ? (dynamic newValue) {
                        debugPrint('newValue          ' + newValue);
                        adhoctaskCntrlr.tasktype.value = newValue;
                      }
                    : null,
                value: adhoctaskCntrlr.tasktype.value == '' ? null : adhoctaskCntrlr.tasktype.value,
                items: adhoctaskCntrlr.tasktypelov.map((LookupValues l) {
                  return DropdownMenuItem(
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                    value: l.lookupCode,
                  );
                }).toList(),
                decoration: InputDecoration(
                  labelStyle: TextStyle(fontSize: 12),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKTYPE')!.value,
                  //icon: Icon(Icons.build),
                  filled: true,
                  fillColor: Colors.white70,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                  ),
                ),
              )
            : DropdownButtonFormField(
                hint: Text(
                  'Select ',
                ),
                onChanged: mode == 'edit'
                    ? (dynamic newValue) {
                        debugPrint('newValue          ' + newValue);
                      }
                    : null,
                value: null,
                items: [
                  DropdownMenuItem(
                      child: Text(
                        "Select",
                        style: TextStyle(fontSize: 14, color: Colors.black),
                      ),
                      value: null),
                ],
                decoration: InputDecoration(
                    labelStyle: TextStyle(fontSize: 12),
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    labelText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_TASKTYPE')!.value,
                    //icon: Icon(Icons.build),
                    filled: true,
                    fillColor: Colors.white70,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(12.0)),
                      borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10.0)),
                      borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                    )),
              ),
      ),
    );
  }

  Widget statusLov(BuildContext? context) {
    debugPrint('status ----------${adhoctaskCntrlr.status.value}');

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        child: (adhoctaskCntrlr.statuslov.value.isNotEmpty && adhoctaskCntrlr.statuslov.value != null)
            ? DropdownButtonFormField(
                hint: Text(
                  'Select ',
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
                onChanged: mode == 'edit'
                    ? (dynamic newValue) {
                        debugPrint('newValue          ' + newValue);
                        adhoctaskCntrlr.status.value = newValue;
                      }
                    : null,
                value: (adhoctaskCntrlr.status.value == '' || adhoctaskCntrlr.status.value == 'null') ? null : adhoctaskCntrlr.status.value,
                items: adhoctaskCntrlr.statuslov.value.map((EntityStatus e) {
                  return DropdownMenuItem(
                    child: new Text(
                      e.status!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                    value: e.status,
                  );
                }).toList(),
                decoration: InputDecoration(
                  labelStyle: TextStyle(fontSize: 12),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_STATUS')!.value,
                  //icon: Icon(Icons.build),
                  filled: true,
                  fillColor: Colors.white70,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                  ),
                ),
              )
            : DropdownButtonFormField(
                hint: Text(
                  'Select ',
                ),
                onChanged: mode == 'edit'
                    ? (dynamic newValue) {
                        debugPrint('newValue          ' + newValue);
                      }
                    : null,
                value: null,
                items: [
                  DropdownMenuItem(
                      child: Text(
                        "Select",
                        style: TextStyle(fontSize: 14, color: Colors.black),
                      ),
                      value: null),
                ],
                decoration: InputDecoration(
                    labelStyle: TextStyle(fontSize: 12),
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    labelText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_STATUS')!.value,
                    //icon: Icon(Icons.build),
                    filled: true,
                    fillColor: Colors.white70,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(12.0)),
                      borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10.0)),
                      borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                    )),
              ),
      ),
    );
  }

  Widget priorityLov(BuildContext? context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        child: (adhoctaskCntrlr.prioritylov.isNotEmpty && adhoctaskCntrlr.prioritylov != null)
            ? DropdownButtonFormField(
                hint: Text(
                  'Select ',
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
                onChanged: mode == 'edit'
                    ? (dynamic newValue) {
                        debugPrint('newValue          ' + newValue);
                        adhoctaskCntrlr.priority.value = newValue;
                      }
                    : null,
                value: adhoctaskCntrlr.priority.value == '' ? null : adhoctaskCntrlr.priority.value,
                items: adhoctaskCntrlr.prioritylov.map((LookupValues l) {
                  return DropdownMenuItem(
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                    value: l.lookupCode,
                  );
                }).toList(),
                decoration: InputDecoration(
                  labelStyle: TextStyle(fontSize: 12),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_PRIORITY')!.value,
                  //icon: Icon(Icons.build),
                  filled: true,
                  fillColor: Colors.white70,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                  ),
                ),
              )
            : DropdownButtonFormField(
                hint: Text(
                  'Select ',
                ),
                onChanged: mode == 'edit'
                    ? (dynamic newValue) {
                        debugPrint('newValue          ' + newValue);
                      }
                    : null,
                value: null,
                items: [
                  DropdownMenuItem(
                      child: Text(
                        "Select",
                        style: TextStyle(fontSize: 14, color: Colors.black),
                      ),
                      value: null),
                ],
                decoration: InputDecoration(
                    labelStyle: TextStyle(fontSize: 12),
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    labelText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS_PRIORITY')!.value,
                    //icon: Icon(Icons.build),
                    filled: true,
                    fillColor: Colors.white70,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(12.0)),
                      borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10.0)),
                      borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                    )),
              ),
      ),
    );
  }
}
