import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_minute.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../models/ta_admin/user_info.dart';
import '../../../providers/common/meetingminute/meetingminutes_controller.dart';
import '../../../utils/common_utils.dart';
import 'meeting_minutes_details_screen.dart';

class MeetingMinutes extends StatelessWidget {
  final entityid;
  final entitytype;
  String? mode;
  final ProjectsView? projectview;
  MeetingMinutes({Key? key, this.entityid, this.entitytype, this.projectview, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  MeetingMinutesController mmCtrl = Get.find<MeetingMinutesController>();
  late UserInfo user;

  @override
  Widget build(BuildContext context) {
    return listStack(context);
  }

  Future _refreshStatusReports() async {
    await mmCtrl.loadMeetingMinutesData(entityid: entityid, entitytype: entitytype);
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      // Row(
      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //   children: [
      //     Row(
      //       children: [
      //         SizedBox(
      //           width: 15,
      //         ),
      //         TaButton(
      //           type: 'elevate',
      //           buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_ADD')?.value ?? 'Add',
      //           onPressed: () {
      //             debugPrint('Add----------');
      //             //addPunchlistItem(context);
      //           },
      //         ),
      //       ],
      //     ),
      //   ],
      // ),
      Positioned.fill(
        //top: 45,
        child: RefreshIndicator(
          onRefresh: _refreshStatusReports,
          child: GetX<MeetingMinutesController>(
            initState: (state) async {
              mmCtrl.loadMeetingMinutesData(entityid: entityid, entitytype: entitytype);
              user = await CommonUtils.getUserdata();
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? ProgressIndicatorCust()
                  : _.meetinminutesdata.length < 1
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.meetinminutesdata[index]);
                          },
                          itemCount: _.meetinminutesdata.length,
                        );
            },
          ),
        ),
      ),
      if (mode == 'edit')
        Positioned(
          bottom: 30,
          right: 30,
          child: FloatingActionButton(
            elevation: 5.0,
            child: const Icon(
              Icons.add,
              size: 30,
            ),
            onPressed: () {
              debugPrint('add button----------------------');
              mmCtrl.meetingminute.value = new MeetingMinute();
              mmCtrl.meetingminute.value.entityType = entitytype;
              mmCtrl.meetingminute.value.entityId = entityid;
              mmCtrl.meetingminute.value.organizer = user.userName;
              mmCtrl.mm_mode.value = 'create';
              Get.to(() => MeetingMinuteDetails(
                    entityid: entityid,
                    entitytype: entitytype,
                    projectview: projectview,
                    mode: mode,
                  ));
            },
          ),
        ),
    ]);
  }

  Widget listitem(BuildContext context, MeetingMinute mm) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {
        mmCtrl.meetingminute.value = mm;
        mmCtrl.mm_mode.value = 'edit';
        Get.to(() => MeetingMinuteDetails(
              entityid: entityid,
              entitytype: entitytype,
              projectview: projectview,
              mode: mode,
            ));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,

                  //crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      mm.meetingId.toString(),
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Flexible(
                      child: Text(
                        mm.meetingTitle ?? '',
                        style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ]),
              SizedBox(
                height: 5,
              ),
              Row(
                children: [
                  Icon(
                    Icons.calendar_month,
                    color: secondary,
                    size: 15,
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Text(
                    mm.meetingDate != null ? 'Date ${mm.meetingDate}' '' : '',
                    style: TextStyle(color: primary, fontSize: 10),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              SizedBox(
                height: 5.0,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(children: [
                    Icon(
                      Icons.schedule,
                      color: secondary,
                      size: 15,
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(
                      mm.startTime != null ? 'Start Time ${mm.startTime}' '' : '',
                      style: TextStyle(color: primary, fontSize: 10),
                    ),
                  ]),
                  Text(
                    mm.endTime != null ? 'End Time ${mm.endTime}' '' : '',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                ],
              ),
              SizedBox(
                height: 5,
              ),
              Row(
                children: [
                  Icon(
                    Icons.person,
                    color: secondary,
                    size: 15,
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Text(
                    mm.organizer ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
