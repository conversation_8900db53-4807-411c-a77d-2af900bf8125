import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../models/mytasks/workflow/wfhistory.dart';
import '../../../providers/mytasks/workflow/userpendingtasks_controller.dart';
import '../../../utils/device_util.dart';

class WorkflowHistoryWidget extends StatelessWidget {
  final entitytype;
  final entityid;
  WorkflowHistoryWidget({Key? key, this.entityid, this.entitytype}) : super(key: key);
  MyApprovalsController myApprovalsCntrlr = Get.find<MyApprovalsController>();
  @override
  Widget build(BuildContext context) {
    Future _refreshWFHistory() async {
      myApprovalsCntrlr.fetchApprovalHistory(entitytype, entityid);
    }

    if (entityid != null) {
      return Column(
        children: [
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshWFHistory,
              child: GetX<MyApprovalsController>(
                initState: (state) {
                  myApprovalsCntrlr.fetchApprovalHistory(entitytype, entityid);
                },
                builder: (ctrlr) {
                  return ctrlr.ishistloading.value
                      ? ProgressIndicatorCust()
                      : ctrlr.wfhistorylist != null && ctrlr.wfhistorylist.isNotEmpty
                          ? ListView.builder(
                              itemCount: ctrlr.wfhistorylist.length,
                              itemBuilder: (context, index) {
                                return wfHistoryitem(context, ctrlr.wfhistorylist[index]);
                              })
                          : Center(
                              child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                            );
                },
              ),
            ),
          ),
        ],
      );
    } else {
      return Center(
        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
      );
    }
  }

  Widget wfHistoryitem(BuildContext context, Wfhistory w) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return //Expanded(
        //child:
        Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  w.action ?? '',
                  style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                ),
                SizedBox(
                  height: 6,
                ),
                Row(
                  //mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Icon(
                      Icons.badge,
                      color: secondary,
                      size: 15,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Row(children: [
                      Text(w.role ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .1)),
                    ]),
                    // Text(w.approvalLimit?.toString() ?? 0, style: TextStyle(color: primary, fontSize: 12, letterSpacing: .1)),
                  ],
                ),
                SizedBox(
                  height: 6,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Icon(
                      Icons.person,
                      color: secondary,
                      size: 15,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Row(children: [
                      Text(w.name ?? 0 as String, style: TextStyle(color: primary, fontSize: 12, letterSpacing: .1)),
                    ]),
                    // Text(w.notificationDate ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                  ],
                ),
                SizedBox(
                  height: 6,
                ),
                Row(
                  children: <Widget>[
                    Icon(
                      Icons.calendar_month,
                      color: secondary,
                      size: 15,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      child: Text(w.notificationDate ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: primary,
                            fontSize: 12,
                            letterSpacing: .1,
                          )),
                    ),
                  ],
                ),
                if (w.comments != '' && w.comments != null)
                  const SizedBox(
                    height: 6,
                  ),
                if (w.comments != '' && w.comments != null)
                  GestureDetector(
                    onTap: () {
                      Get.defaultDialog(
                          title: 'Comments',
                          titleStyle: TextStyle(fontSize: 16),
                          content: TaInputText(
                            maxLines: 15,
                            value: w.comments,
                            readOnly: true,
                          ),
                          middleText: w.comments!,
                          actions: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                TaButton(
                                  type: 'elevate',
                                  buttonText: 'Close',
                                  onPressed: () {
                                    Get.back();
                                  },
                                ),
                                SizedBox(
                                  width: 7.0,
                                ),
                              ],
                            ),
                          ]);
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Icon(
                          Icons.forum,
                          color: secondary,
                          size: 15,
                        ),
                        const SizedBox(
                          width: 4,
                        ),
                        Expanded(
                          child: Text(w.comments ?? '',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              style: TextStyle(
                                color: primary,
                                fontSize: 12,
                                letterSpacing: .1,
                              )),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          )
        ],
      ),
      //),
    );
  }
}
