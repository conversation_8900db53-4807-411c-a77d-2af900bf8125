import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/sitemanagement/site/siteview.dart';
import '../../../../providers/sitemanagement/site/site_general_controller.dart';

class EntityQuestionnaire extends GetView<SiteGeneralController> {
  final SiteView? site;
  EntityQuestionnaire({Key? key, this.site}) : super(key: key);

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  SiteGeneralController genCtrl = Get.find<SiteGeneralController>();

  @override
  Widget build(BuildContext context) {
    genCtrl.labelsloading.value = true;

    return Stack(children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await genCtrl.getlabels(talabel);
            });
          },
          builder: (_) {
            return genCtrl.labelsloading.value ? ProgressIndicatorCust() : generalTab(site);
          },
        ),
      ),
    ]);
  }

  Widget generalTab(SiteView? s) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_SITE_GENERAL_SITENUM') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_SITENUM')!.value,
              value: s!.siteNumber.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_SITENAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_SITENAME')!.value,
              value: s!.siteName.toString(),
              readOnly: true,
            ),

          if (talabel.get('TMCMOBILE_SITE_GENERAL_STATUS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_STATUS')!.value,
              value: s!.statusDesc?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_LATITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_LATITUDE')!.value,
              value: s!.latitude?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_LONGITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_LONGITUDE')!.value,
              value: s!.longitude.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_ADDRESS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_ADDRESS')!.value,
              value: s!.address?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_CITY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_CITY')!.value,
              value: s!.city?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_STATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_STATE')!.value,
              value: s!.state?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_ZIPCODE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_ZIPCODE')!.value,
              value: s!.zipCode?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_DMANAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_DMANAME')!.value,
              value: s!.dmaName?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_COUNTY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_COUNTY')!.value,
              value: s!.county?.toString(),
              readOnly: true,
            ),
          // if (talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM') != null)
          // TaInputText(
          //   title: talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM').value,
          //   value:s.zipCode?.toString(),
          //   readOnly: true,
          // ),
          //  if (talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM') != null)
          // TaInputText(
          //   title: talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM').value,
          //   value:s.zipCode?.toString(),
          //   readOnly: true,
          // ),
        ],
      ),
    );
  }
}
