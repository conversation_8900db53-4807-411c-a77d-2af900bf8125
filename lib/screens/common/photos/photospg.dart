import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/screens/common/photos/photos.dart';
import 'package:tangoworkplace/screens/common/photos/photosgrid.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';
import '../../../providers/common/photosgrid_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';

class PhotosPG extends GetView<PhotosController> {
  final String? entityType;
  final int? entityId;
  String? mode;

  PhotosPG({Key? key, this.entityType, this.entityId, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  late PhotosController photoCntrl;

  @override
  Widget build(BuildContext context) {
    bool ctrlflag = Get.isRegistered<PhotosController>();
    if (!ctrlflag) Get.put(PhotosController());
    bool gridflag = Get.isRegistered<PhotosGridController>();
    if (!gridflag) Get.put(PhotosGridController());
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Photos', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
        // actions: [
        //   if (mode != 'view')
        //     TextButton(
        //       onPressed: () async {
        //         await photoCntrl.deletePhotos();
        //       },
        //       child: Row(
        //         mainAxisSize: MainAxisSize.min,
        //         crossAxisAlignment: CrossAxisAlignment.center,
        //         children: [
        //           Text(
        //             talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
        //             style: ComponentUtils.appbartitlestyle,
        //           ),
        //         ],
        //       ),
        //     ),
        // ],
      ),
      body: PhotosGrid(
        entityId: entityId,
        entityType: entityType,
        mode: mode,
      ),
    );
  }
}
