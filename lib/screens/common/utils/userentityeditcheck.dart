import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../common/component_utils.dart';

class UserEntityEditCheckPg extends StatelessWidget {
  final entitytype;
  final entityname;
  final entityid;

  UserEntityEditCheckPg({this.entitytype, this.entityname, this.entityid});

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(this.entityname, style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Container(
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'You dont have access to this $entitytype.\n\n Please contact the administrator...',
                style: TextStyle(
                  color: ComponentUtils.primecolor,
                  fontSize: DeviceUtils.taFontSize(1.55, context),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: 30,
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Go Back',
                onPressed: () async {
                  debugPrint('------back-------');
                  Get.back();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
