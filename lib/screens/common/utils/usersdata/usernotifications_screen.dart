import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tacheckbox.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/utils/adhoctasknotifuser.dart';
import 'package:tangoworkplace/models/common/utils/contact.dart';
import 'package:tangoworkplace/models/common/utils/systemuser.dart';
import 'package:tangoworkplace/providers/common/utils/usernotifications_controller.dart';
import 'package:tangoworkplace/providers/common/utils/docattachments_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/utils/usersdata/systemuserdata.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class UserNotifications extends StatelessWidget {
  final sid;
  final entityid;
  final entitytype;
  final hdrText;
  final sysUserText;
  final contactsText;
  UserNotifications({Key? key, this.sid, this.entityid, this.entitytype, this.hdrText, this.sysUserText, this.contactsText})
      : super(key: key);

  UserNotificationsController usernotiCtrl = Get.put(UserNotificationsController());
  LabelController talabel = Get.find<LabelController>();
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: bottombuttonbar(),
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrText ?? 'Notifications Users', style: ComponentUtils.appbartitlestyle),
        actions: [
          TextButton(
            onPressed: () {
              usernotiCtrl.saveNotifyUsers(entityid, entitytype, sid);
              Get.back();
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                  style: ComponentUtils.appbartitlestyle,
                ),
              ],
            ),
          ),
        ],
        backgroundColor: Colors.white,
        elevation: 0,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Stack(
        children: <Widget>[
          Positioned.fill(
            //child: RefreshIndicator(
            // onRefresh: _refreshComments,
            child: GetX<UserNotificationsController>(
              initState: (state) {
                usernotiCtrl.fetchAdhocNotifUsers(sid);
              },
              builder: (_) {
                return _.isLoading.isTrue
                    ? ProgressIndicatorCust()
                    : _.adhocnotifyusers.length < 1
                        ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return listitem(context, _.adhocnotifyusers[index]);
                            },
                            itemCount: _.adhocnotifyusers.length,
                          );
              },
            ),
          ),
          // Positioned(
          //   bottom: 30,
          //   right: 30,
          //   child: FloatingActionButton(
          //     elevation: 5.0,
          //     child: const Icon(
          //       Icons.add,
          //       size: 30,
          //     ),
          //     onPressed: () {
          //       _openDialog();
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget bottombuttonbar() {
    return BottomAppBar(
      child: Container(
        margin: EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            TaButton(
              type: 'elevate',
              //buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
              buttonText: sysUserText ?? 'System Users',
              onPressed: () {
                _openDialog('systemusers');
              },
            ),
            SizedBox(
              height: 5.0,
              width: 5.0,
            ),
            TaButton(
              type: 'elevate',
              //buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
              buttonText: contactsText ?? 'Contacts',
              onPressed: () {
                _openDialog('contacts');
              },
            ),
          ],
        ),
      ),
      color: Colors.white,
    );
  }

  Widget listitem(BuildContext context, AdhocTaskNotifUser atnu) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          debugPrint('');
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          atnu.userName ?? '',
                          style: TextStyle(
                              color: primary,
                              //fontWeight: FontWeight.bold,
                              fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Row(children: [
                          Text('${atnu.firstName} ${atnu.lastName}', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ]),
                        SizedBox(
                          height: 2,
                        ),
                        Row(children: [
                          Icon(
                            Icons.email,
                            color: secondary,
                            size: 15,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Text(atnu.emailAddress ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ]),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        IconButton(
                            onPressed: () {
                              debugPrint('-----------------');
                              usernotiCtrl.adhocnotifyusers.removeWhere((element) => element.emailAddress == atnu.emailAddress);
                            },
                            icon: Icon(
                              Icons.delete,
                              color: secondary,
                            )),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  void _openDialog(String usertype) async {
    usertype == 'contacts' ? usernotiCtrl.fetchContacts(entityid, entitytype) : usernotiCtrl.fetchSytemUsers();
    await Navigator.of(Get.context!).push(new MaterialPageRoute<String>(
        builder: (BuildContext context) {
          return new Scaffold(
            appBar: new AppBar(
              leading: new IconButton(
                icon: const Icon(Icons.clear),
                color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                onPressed: () {
                  debugPrint('------back-------');
                  Get.back();
                },
              ),
              backgroundColor: Colors.white,
              elevation: 5,
              title: Text(
                'Users',
                style: ComponentUtils.appbartitlestyle,
              ),
              // actions: [
              //   TextButton(
              //     onPressed: () {
              //       Get.back();
              //     },
              //     child: Row(
              //       mainAxisSize: MainAxisSize.min,
              //       crossAxisAlignment: CrossAxisAlignment.center,
              //       children: [
              //         Text(
              //           'Add',
              //           style: ComponentUtils.appbartitlestyle,
              //         ),
              //       ],
              //     ),
              //   ),
              // ],
            ),
            body: usertype == 'contacts'
                ? ContactsData(
                    entityid: entityid,
                    entitytype: entitytype,
                    adhoctaskid: sid,
                  )
                : SystemUsersData(
                    adhoctaskid: sid,
                  ),
          );
        },
        fullscreenDialog: true));
  }
}

class ContactsData extends StatelessWidget {
  final entitytype;
  final entityid;
  final adhoctaskid;
  ContactsData({Key? key, this.entitytype, this.entityid, this.adhoctaskid}) : super(key: key);
  UserNotificationsController usernotiCtrl = Get.find<UserNotificationsController>();

  @override
  Widget build(BuildContext context) {
    debugPrint('adhoctaskid ---------------------$adhoctaskid');
    return GetX<UserNotificationsController>(
      initState: (state) async {},
      builder: (_) {
        return _.isContactsLoading != null && _.isContactsLoading.isTrue
            ? ProgressIndicatorCust()
            : _.contacts.length < 1
                ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                // : Obx(
                //     () =>
                : ListView.builder(
                    itemBuilder: (context, index) {
                      return listitem(context, _.contacts[index], index);
                    },
                    itemCount: _.contacts.length,
                    //  ),
                  );
      },
    );
  }

  Widget listitem(BuildContext context, Contact c, int index) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return
        // GestureDetector(
        //   onTap: () {
        //     debugPrint('');
        //   },
        // child:
        Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      c.contactUserId ?? '',
                      style: TextStyle(
                          color: primary,
                          //fontWeight: FontWeight.bold,
                          fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Row(children: [
                      Text('${c.name} ${c.lastName}', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                    SizedBox(
                      height: 2,
                    ),
                    Row(children: [
                      Icon(
                        Icons.email,
                        color: secondary,
                        size: 15,
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Text(c.email ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    GestureDetector(
                        child: Obx(
                          () => usernotiCtrl.contacts.value[index].selectrow!
                              ? Icon(
                                  Icons.check,
                                  color: Theme.of(context).primaryColor,
                                )
                              : Icon(
                                  Icons.check_box_outline_blank,
                                  color: Colors.grey,
                                ),
                        ),
                        onTap: () {
                          c = usernotiCtrl.contacts.value[index];
                          usernotiCtrl.contacts[index].selectrow = !c.selectrow!;
                          debugPrint('-----------${usernotiCtrl.contacts.value[index].lastName}');

                          usernotiCtrl.contacts[index] = c;
                          usernotiCtrl.adhocnotifyusers
                              .removeWhere((element) => element.emailAddress == usernotiCtrl.contacts.value[index].email);

                          if (usernotiCtrl.contacts[index].selectrow!)
                            usernotiCtrl.adhocnotifyusers.value.add(AdhocTaskNotifUser(
                                adhocTaskId: adhoctaskid,
                                emailAddress: c.email,
                                firstName: c.name,
                                lastName: c.lastName,
                                userName: c.contactUserId));
                        }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// class SystemUsersData extends StatelessWidget {
//   final adhoctaskid;
//   SystemUsersData({Key key, this.adhoctaskid}) : super(key: key);
//   UserNotificationsController adhoctaskusernotiCtrl = Get.find<UserNotificationsController>();

//   @override
//   Widget build(BuildContext context) {
//     return GetX<UserNotificationsController>(
//       initState: (state) {
//         //adhoctaskusernotiCtrl.fetchSytemUsers();
//       },
//       builder: (_) {
//         return _.isSystemUserLoading.isTrue
//             ? ProgressIndicatorCust()
//             : _.systemusers.length < 1
//                 ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
//                 : ListView.builder(
//                     itemBuilder: (context, index) {
//                       return listitem(context, _.systemusers[index], index);
//                     },
//                     itemCount: _.systemusers.length,
//                     //  ),
//                   );
//       },
//     );
//   }

//   Widget listitem(BuildContext context, SystemUser c, int index) {
//     final primary = ComponentUtils.primary;
//     final secondary = ComponentUtils.secondary;

//     return Container(
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(12),
//         color: Colors.white,
//       ),
//       width: double.infinity,
//       //height: 110,
//       margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
//       padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: <Widget>[
//           Expanded(
//             child: Row(
//               crossAxisAlignment: CrossAxisAlignment.center,
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               mainAxisSize: MainAxisSize.max,
//               children: [
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: <Widget>[
//                     Text(
//                       c.userName ?? '',
//                       style: TextStyle(
//                           color: primary,
//                           //fontWeight: FontWeight.bold,
//                           fontSize: 14),
//                       maxLines: 2,
//                       overflow: TextOverflow.ellipsis,
//                     ),
//                     SizedBox(
//                       height: 5,
//                     ),
//                     Row(children: [
//                       Text('${c.firstName} ${c.lastName}', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
//                     ]),
//                     SizedBox(
//                       height: 2,
//                     ),
//                     Row(children: [
//                       Icon(
//                         Icons.email,
//                         color: secondary,
//                         size: 15,
//                       ),
//                       SizedBox(
//                         width: 5,
//                       ),
//                       Text(c.emailAddress ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
//                     ]),
//                   ],
//                 ),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.end,
//                   children: <Widget>[
//                     GestureDetector(
//                         child: Obx(
//                           () => adhoctaskusernotiCtrl.systemusers.value[index].selectrow
//                               ? Icon(
//                                   Icons.check,
//                                   color: Theme.of(context).primaryColor,
//                                 )
//                               : Icon(
//                                   Icons.check_box_outline_blank,
//                                   color: Colors.grey,
//                                 ),
//                         ),
//                         onTap: () {
//                           c = adhoctaskusernotiCtrl.systemusers.value[index];
//                           adhoctaskusernotiCtrl.systemusers[index].selectrow = !c.selectrow;
//                           debugPrint('-----------${adhoctaskusernotiCtrl.systemusers.value[index].lastName}');

//                           adhoctaskusernotiCtrl.systemusers[index] = c;
//                           adhoctaskusernotiCtrl.adhocnotifyusers.removeWhere(
//                               (element) => element.emailAddress == adhoctaskusernotiCtrl.systemusers.value[index].emailAddress);
//                           if (adhoctaskusernotiCtrl.systemusers[index].selectrow)
//                             adhoctaskusernotiCtrl.adhocnotifyusers.add(AdhocTaskNotifUser(
//                                 adhocTaskId: adhoctaskid,
//                                 emailAddress: c.emailAddress,
//                                 firstName: c.firstName,
//                                 lastName: c.lastName,
//                                 userName: c.userName));
//                         }),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//}
