import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/screens/common/utils/usersdata/systemuserdata.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../../common/component_utils.dart';
import '../../../../providers/common/meetingminute/mmActionItems_controller.dart';

class SystemUsers extends StatelessWidget {
  final source;
  MmActionItemsController? mmadhoctaskCntrlr;
  SystemUsers({Key? key, this.source, this.mmadhoctaskCntrlr}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Users', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: SystemUsersData(
        source: source,
        mmadhoctaskCntrlr: mmadhoctaskCntrlr,
      ),
    );
  }
}
