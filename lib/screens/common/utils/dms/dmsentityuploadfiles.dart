import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/utils/dmsfile.dart';
import 'package:tangoworkplace/providers/common/utils/docattachments_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class DmsEntityUploadFiles extends StatelessWidget {
  final entitytype;
  final entityid;
  final subentityid;
  final subentitytype;
  String? mode;
  DmsEntityUploadFiles({Key? key, this.entitytype, this.entityid, this.subentityid, this.subentitytype, this.mode}) : super(key: key);
  DocAttachmentController docAttachCtrl = Get.find<DocAttachmentController>();

  void _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );
    if (result != null) {
      List<File> files;
      files = result.paths.map((path) => File(path!)).toList();

      files.forEach((element) {
        File f = element;
        debugPrint('file name    ${f.path}');
      });
      ProgressUtil.showLoaderDialog(Get.context!);
      docAttachCtrl.uploadFiles(files, entityType: entitytype, entityId: entityid, subentityid: subentityid, subentitytype: subentitytype);
    } else {
      // User canceled the picker
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Positioned.fill(
          top: 15,
          child: GetX<DocAttachmentController>(
            initState: (state) {
              docAttachCtrl.tempuploadedlist.value.clear();
              docAttachCtrl.fetchDmsFiles(subentityid: subentityid, subentitytype: subentitytype);
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? ProgressIndicatorCust()
                  : _.uploadedDocs.length < 1
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.uploadedDocs[index], index);
                          },
                          itemCount: _.uploadedDocs.length,
                          //  ),
                        );
            },
          ),
        ),
        if (mode != 'view')
          Positioned(
            bottom: 30,
            right: 30,
            child: FloatingActionButton(
              elevation: 5.0,
              child: const Icon(
                Icons.add,
                size: 30,
              ),
              onPressed: () {
                _pickFiles();
              },
            ),
          ),
      ],
    );
  }

  Widget listitem(BuildContext context, DmsFile d, int index) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Icon(
                      Icons.description,
                      color: secondary,
                      size: 20.0,
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Text(
                      d.fileName ?? '',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    IconButton(
                        onPressed: () {
                          docAttachCtrl.uploadedDocs.removeWhere((element) => element.fileId == d.fileId);
                          docAttachCtrl.tempuploadedlist.value.add(d.fileId);
                        },
                        icon: Icon(
                          Icons.delete,
                          color: secondary,
                        )),
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
