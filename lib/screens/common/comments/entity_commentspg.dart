import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/screens/common/comments/entity_comments.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';

class EntityCommentsPG extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  final int? subentityid;
  String? mode;

  EntityCommentsPG({Key? key, this.entityType, this.entityId, this.subentityid, this.mode}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Comments', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: EntityComments(
        entityId: entityId,
        entityType: entityType,
        subentityid: subentityid,
        mode: mode,
      ),
    );
  }
}
