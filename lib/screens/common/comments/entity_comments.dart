import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/entity_comment.dart';
import 'package:tangoworkplace/providers/common/entitycomments_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../models/lookup_values.dart';

class EntityComments extends GetView<EntityCommentsController> {
  final String? entityType;
  final int? entityId;
  final int? subentityid;
  String? mode;
  EntityComments({Key? key, this.entityType, this.entityId, this.subentityid, this.mode}) : super(key: key);
  final _addCommentCtrl = TextEditingController();
  LabelController talabel = Get.find<LabelController>();
  EntityCommentsController commCntrl = Get.find<EntityCommentsController>();
  final DateFormat formatter = DateFormat('MM-dd-yyyy');
  Future _refreshComments() async {
    commCntrl.loadEntityComments(entityType: entityType, entityId: entityId, subentityid: subentityid);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: RefreshIndicator(
          onRefresh: _refreshComments,
          child: GetX<EntityCommentsController>(
            initState: (state) {
              commCntrl.loadEntityComments(entityType: entityType, entityId: entityId, subentityid: subentityid);
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? ProgressIndicatorCust()
                  : _.entitycomments.length < 1
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.entitycomments[index]);
                          },
                          itemCount: _.entitycomments.length,
                        );
            },
          ),
        ),
      ),
      if (mode != 'view')
        Positioned(
          bottom: 30,
          right: 30,
          child: FloatingActionButton(
            elevation: 5.0,
            child: const Icon(
              Icons.add,
              size: 30,
            ),
            onPressed: () {
              commCntrl.getLovItems();
              commCntrl.commentType.value = '';
              Get.defaultDialog(
                  title: 'Add Comment',
                  titleStyle: TextStyle(fontSize: 16),
                  content: Container(
                    // margin: EdgeInsets.only(left: 1),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TaInputTextField(
                          title: 'Comment',
                          controller: commCntrl.addCommentCtrl,
                          minLines: 3,
                          maxLines: 15,
                          keyboard: TextInputType.multiline,
                          textInputAct: TextInputAction.newline,
                        ),
                        categorylov(),
                      ],
                    ),
                  ),
                  actions: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TaButton(
                          type: 'elevate',
                          buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                          onPressed: () {
                            Get.back();
                          },
                        ),
                        SizedBox(
                          width: 4.0,
                        ),
                        TaButton(
                          type: 'elevate',
                          buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_ADD')?.value ?? 'Add',
                          onPressed: () {
                            debugPrint('-----------');
                            commCntrl.addComments(entityid: entityId, entitytype: entityType, subentityid: subentityid);
                          },
                        ),
                        SizedBox(
                          width: 7.0,
                        ),
                      ],
                    ),
                  ]);
            },
          ),
        ),
    ]);
  }

  Widget categorylov() {
    return Container(
      margin: EdgeInsets.only(top: 1, right: 7),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Obx(
            () => commCntrl.lovloading.value
                ? ProgressIndicatorCust()
                : TaDropDown(
                    label: 'Comment Type',
                    initvalue: commCntrl.commentType.value,
                    value: commCntrl.commentType.value,
                    items: commCntrl.commentTypelov.value.map((LookupValues l) {
                      return DropdownMenuItem(
                        child: new Text(
                          l.lookupValue!,
                          style: TextStyle(fontSize: 14, color: Colors.black),
                        ),
                        value: l.lookupCode,
                      );
                    }).toList(),
                    onChanged: (val) async {
                      debugPrint('val -----' + val);
                      commCntrl.commentType.value = val;
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, EntityComment c) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.defaultDialog(
              title: 'Comment',
              titleStyle: TextStyle(fontSize: 16),
              content: TaInputText(
                maxLines: 15,
                value: c.comments,
                readOnly: true,
              ),
              middleText: c.comments!,
              actions: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TaButton(
                      type: 'elevate',
                      buttonText: 'Cancel',
                      onPressed: () {
                        Get.back();
                      },
                    ),
                    SizedBox(
                      width: 7.0,
                    ),
                  ],
                ),
              ]);
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${c.comments}',
                      style: TextStyle(
                          color: primary,
                          //fontWeight: FontWeight.bold,
                          fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Row(children: [
                      Icon(
                        Icons.person,
                        color: secondary,
                        size: 15,
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Text(c.reviewedBy ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                    SizedBox(
                      height: 2,
                    ),
                    Row(children: [
                      Icon(
                        Icons.date_range,
                        color: secondary,
                        size: 15,
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Text(c.reviewDate ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
