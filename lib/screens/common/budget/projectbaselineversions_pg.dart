import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/common/budget/projbaseline_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';
import 'projectbaselineversions_list.dart';

class ProjBaselineVerionsPg extends StatelessWidget {
  final hdrtitle;
  final projectid;
  ProjBaselineVerionsPg({Key? key, this.hdrtitle, this.projectid}) : super(key: key);

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  ProjectBaselineVersionsController bvState = Get.put(ProjectBaselineVersionsController());

  ProjectBaselineVersionsController pbvCtrlr = Get.find<ProjectBaselineVersionsController>();
  LabelController talabel = Get.find<LabelController>();

  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrtitle, style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: ProjBaselineVerions(entityid: projectid),
    );
  }
}
