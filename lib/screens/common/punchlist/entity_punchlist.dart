import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/punchlist.dart';
import 'package:tangoworkplace/models/common/template.dart';
import 'package:tangoworkplace/providers/common/punchlist_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/common/punchlist/punchlist_details.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';

class EntityPunchlist extends GetView<EntityPunchlistController> {
  final String? entityType;
  final int? entityId;
  String? mode;
  EntityPunchlist({Key? key, this.entityType, this.entityId, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  EntityPunchlistController punchlistCntrl = Get.find<EntityPunchlistController>();

  Future _refreshPunchlist() async {
    punchlistCntrl.loadEntityPunchlist(entityType, entityId);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => punchlistCntrl.listflag.isTrue ? listStack(context) : templateSatck(context));
  }

  Widget templateSatck(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: <Widget>[
        if (mode == 'view') new Container() else templateform(context),
      ],
    );
  }

  Widget listStack(BuildContext context) {
    final colWidth = Get.width / 3;
    final double car = Get.context!.isLandscape ? (1 / .2) : (1 / .3);
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          mode == 'view'
              ? new SizedBox(
                  width: 5,
                )
              : Row(
                  children: [
                    SizedBox(
                      width: 15,
                    ),
                    TaButton(
                      type: 'elevate',
                      buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_ADD')?.value ?? 'Add',
                      onPressed: () {
                        debugPrint('Add----------');
                        addPunchlistItem(context);
                      },
                    ),
                  ],
                ),
          // TaButton(
          //   type: 'elevate',
          //   buttonText: 'Actions',
          //   onPressed: () {
          //     showactions();
          //   },
          // ),
          // SizedBox(
          //   width: 5,
          // ),
          Row(
            children: [
              IconButton(
                icon: new Icon(Icons.filter_alt_rounded),
                color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                onPressed: () {
                  filterDialog();

                  // adhoctaskCntrlr.loadAdhocTasks(entityType: entityType, entityId: entityId);
                },
              ),
              if (punchlistCntrl.searchflag.isTrue)
                IconButton(
                  icon: new Icon(Icons.clear),
                  color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                  onPressed: () async {
                    punchlistCntrl.searchflag.value = false;
                    punchlistCntrl.searchTextCtrl.text = '';
                    punchlistCntrl.completedFilter.value = 'ALL';
                    await punchlistCntrl.loadEntityPunchlist(entityType, entityId);
                  },
                ),
              //filterlov(),
            ],
          ),
        ],
      ),

      Positioned.fill(
        top: 45,
        child: RefreshIndicator(
          onRefresh: _refreshPunchlist,
          child: GetX<EntityPunchlistController>(
            initState: (state) async {
              await punchlistCntrl.getlabels(talabel);
              punchlistCntrl.loadEntityPunchlist(entityType, entityId);
            },
            builder: (_) {
              return _.isloading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.punchlistdata.length < 1
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : CommonUtils.isTablet(context)
                          ? GridView.builder(
                              // controller: _.punchlistScrollCtrl,
                              itemCount: _.punchlistdata.length,
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                childAspectRatio: car,
                              ),
                              itemBuilder: (BuildContext context, int index) {
                                return listitem(context, _.punchlistdata[index]);
                              })
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.punchlistdata[index]);
                              },
                              itemCount: _.punchlistdata.length,
                              controller: _.punchlistScrollCtrl,
                            );
            },
          ),
        ),
      ),
      // Positioned(
      //   bottom: 30,
      //   right: 30,
      //   child: FloatingActionButton(
      //     elevation: 5.0,
      //     child: const Icon(
      //       Icons.add,
      //       size: 30,
      //     ),
      //     onPressed: () {
      //       debugPrint('add button----------------------');
      // Get.defaultDialog(
      //     title: 'Add Comment',
      //     titleStyle: TextStyle(fontSize: 16),
      //     content: TaInputTextField(
      //       title: 'Comment',
      //       controller: commCntrl.addCommentCtrl,
      //       minLines: 3,
      //       maxLines: 15,
      //       keyboard: TextInputType.multiline,
      //     ),
      //     actions: [
      //       Row(
      //         mainAxisAlignment: MainAxisAlignment.end,
      //         children: [
      //           TaButton(
      //             type: 'elevate',
      //             buttonText: 'Cancel',
      //             onPressed: () {
      //               Get.back();
      //             },
      //           ),
      //           SizedBox(
      //             width: 4.0,
      //           ),
      //           TaButton(
      //             type: 'elevate',
      //             buttonText: 'Add',
      //             onPressed: () {
      //               debugPrint('-----------');
      //               commCntrl.addComments(entityid: entityId, entitytype: entityType);
      //             },
      //           ),
      //           SizedBox(
      //             width: 7.0,
      //           ),
      //         ],
      //       ),
      //     ]);
      //     },
      //   ),
      // ),
    ]);
  }

  Widget listitem(BuildContext context, Punchlist punch) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {
        Get.to(() => PunchlistDetails(
              punch: punch,
              entityId: entityId,
              entityType: entityType,
              mode: mode,
            ));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          decoration: BoxDecoration(
            border: Border(
                left: BorderSide(
              color: (punch.notApplicableFlag == 'true' || punch.notApplicableFlag == 'TRUE')
                  ? Colors.red
                  : ((punch.cExtAttr1 == 'true' || punch.cExtAttr1 == 'TRUE') ? Colors.green : Colors.blue),
              width: 3,
            )),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ITEMSEQ') != null)
                listVertRow(
                    talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ITEMSEQ')!.value ?? 'Item Num', punch.lineItemSeq?.toString() ?? ''),
              if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_SCOPEPUNCHNAME') != null)
                listVertRow(talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_SCOPEPUNCHNAME')!.value ?? 'Name',
                    punch.scopePunchName?.toString() ?? ''),
              if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DIVISION') != null)
                listVertRow(
                    talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DIVISION')!.value ?? 'Division', punch.division?.toString() ?? ''),
              if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DESC') != null)
                listVertRow(
                    talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DESC')!.value ?? 'Description', punch.description?.toString() ?? ''),
              if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_COMMENTS') != null)
                listVertRow(
                    talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_COMMENTS')!.value ?? 'Comments', punch.comments?.toString() ?? ''),

              // Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,

              //     //crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       Text(
              //         punch.lineItemSeq?.toString() ?? '',
              //         style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
              //         overflow: TextOverflow.ellipsis,
              //       ),
              //       Flexible(
              //         child: Text(
              //           punch.scopePunchName ?? '',
              //           style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
              //           maxLines: 1,
              //           overflow: TextOverflow.ellipsis,
              //         ),
              //       ),
              //     ]),
              // Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              //   Row(mainAxisAlignment: MainAxisAlignment.start, children: [
              //     Text(
              //       punch.division?.toString() ?? '',
              //       style: TextStyle(color: primary, fontSize: 10),
              //     ),
              //   ]),
              // ]),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: [
              //     Flexible(
              //       child: Text(
              //         punch.description?.toString() ?? '',
              //         style: TextStyle(color: primary, fontSize: 10),
              //         overflow: TextOverflow.ellipsis,
              //       ),
              //     ),
              //   ],
              // ),
              Container(
                margin: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    (punch.notApplicableFlag == 'true' || punch.notApplicableFlag == 'TRUE')
                        ? Text(
                            'N/A',
                            style: TextStyle(color: Colors.red, fontSize: 10),
                          )
                        : ((punch.cExtAttr1 == 'true' || punch.cExtAttr1 == 'TRUE')
                            ? Text(
                                'Completed',
                                style: TextStyle(color: Colors.green, fontSize: 10),
                              )
                            : Text(
                                'Not Completed',
                                style: TextStyle(color: Colors.blue, fontSize: 10),
                              )),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget listVertRow(String label, String val, {String? dtype}) {
    final primary = ComponentUtils.primary;
    final nf = NumberFormat.currency(
      customPattern: '#,###.##',
      //locale: 'en_US',
    );
    return Container(
        margin: EdgeInsets.symmetric(vertical: 3, horizontal: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.only(right: 10),
              child: Text(
                label,
                style: TextStyle(
                    color: primary, //fontWeight: FontWeight.bold,
                    fontSize: 10),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Flexible(
              child: Text(
                overflow: TextOverflow.ellipsis,
                //maxLines: 2,
                dtype == 'num' ? nf.format(double.tryParse(val)) : val,
                style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 10),
              ),
            ),
          ],
        ));
  }

  Widget templateform(BuildContext context) {
    return Obx(
      () => punchlistCntrl.isloading.isTrue
          ? ProgressIndicatorCust()
          : Column(
              children: [
                Container(
                  padding: EdgeInsets.only(top: 35),
                  margin: EdgeInsets.fromLTRB(10, 20, 10, 5),
                  width: Get.width,
                  height: 230,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                      boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                  // decoration: BoxDecoration(
                  //     border: Border.all(color: HexColor('#4C4B5D').withOpacity(0.2), width: 1), borderRadius: BorderRadius.circular(15)),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      templatesLov(context),
                      SizedBox(
                        height: 40,
                      ),
                      TaButton(
                        type: 'elevate',
                        buttonText: 'Generate Punchlist',
                        onPressed: () {
                          if (punchlistCntrl.generatebtnbinding.value) {
                            punchlistCntrl.generateItemsFromTemplate(
                              entityType: entityType,
                              entityId: entityId,
                            );
                          } else {
                            ComponentUtils.showsnackbar(text: "Please Select Template...");
                            return null;
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  filterDialog() {
    Get.defaultDialog(
        title: 'Search',
        titleStyle: TextStyle(fontSize: 16),
        content: Container(
          // margin: EdgeInsets.only(left: 1),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //TaSearchInputText(searchController: punchlistCntrl.searchTextCtrl, hintSearch: 'Search '),
              TaInputText(
                title: 'Punchlist Name',
                controller: punchlistCntrl.searchTextCtrl,
              ),
              SizedBox(
                height: 10,
              ),
              filterlov(),
              SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: 'Reset', //talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () async {
                  Get.back();
                  punchlistCntrl.searchflag.value = false;
                  punchlistCntrl.searchTextCtrl.text = '';
                  punchlistCntrl.completedFilter.value = 'ALL';
                  await punchlistCntrl.loadEntityPunchlist(entityType, entityId);
                },
              ),
              SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Search', //talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                onPressed: () async {
                  Get.back();
                  if ((punchlistCntrl.searchTextCtrl.text != '' && punchlistCntrl.searchTextCtrl.text != null) ||
                      punchlistCntrl.completedFilter.value != 'ALL') {
                    punchlistCntrl.searchflag.value = true;
                    await punchlistCntrl.loadEntityPunchlist(entityType, entityId,
                        searchText: punchlistCntrl.searchTextCtrl.text, statusfilter: punchlistCntrl.completedFilter.value);
                  }
                },
              ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  Widget filterlov() {
    return Container(
      margin: EdgeInsets.only(top: 5, right: 7),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Obx(
            () => TaDropSown(
              label: 'Select',
              initvalue: punchlistCntrl.completedFilter.value,
              items: <String>['ALL', 'Not Completed', 'N/A'].map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value,
                    style: TextStyle(fontSize: 12, color: Colors.black),
                  ),
                );
              }).toList(),
              onChanged: (val) async {
                punchlistCntrl.completedFilter.value = val;
                // await punchlistCntrl.loadEntityPunchlist(entityType, entityId);
                //punchlistCntrl.listflag.value = true;
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget templatesLov(BuildContext context) {
    return (punchlistCntrl.templatelov.isNotEmpty && punchlistCntrl.templatelov != null)
        ? DropdownButton(
            hint: Text(
              'Select Template',
              style: TextStyle(fontSize: 14, color: Colors.black),
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
              punchlistCntrl.selectedtemplate.value = newValue;
              punchlistCntrl.generatebtnbinding.value = (newValue != null && newValue != '') ? true : false;
            },
            value: punchlistCntrl.selectedtemplate.value == '' ? null : punchlistCntrl.selectedtemplate.value,
            items: punchlistCntrl.templatelov.map((Template t) {
              return DropdownMenuItem(
                child: new Text(
                  t.templateName!,
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
                value: t.templateId.toString(),
              );
            }).toList(),
          )
        : DropdownButton(
            hint: Text(
              'Select Template',
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
            },
            value: null,
            items: [
              DropdownMenuItem(
                  child: Text(
                    "Select",
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: null),
            ],
          );
  }

  addPunchlistItem(BuildContext ctx) {
    return Get.defaultDialog(
        title: 'Add Punchlist',
        titleStyle: TextStyle(fontSize: 16),
        content: Container(
          height: 250,
          child: SingleChildScrollView(
            child: Column(
              children: [
                TaInputTextField(
                  title: 'Item sequence',
                  controller: punchlistCntrl.addItemSeqCtrl,
                  keyboard: TextInputType.number,
                  minLines: 1,
                  maxLines: 1,
                  // onChanged: (val) {
                  //   punchlistCntrl.addItemSeqCtrl.text = val;
                  // },
                ),
                TaInputTextField(
                  title: 'Name',
                  controller: punchlistCntrl.addItemNameCtrl,
                  minLines: 1,
                  maxLines: 1,
                ),
                TaInputTextField(
                  title: 'Division',
                  controller: punchlistCntrl.addDivisionCtrl,
                  minLines: 1,
                  maxLines: 1,
                ),
                TaInputTextField(
                  title: 'Item Type',
                  controller: punchlistCntrl.addItemTypeCtrl,
                  minLines: 1,
                  maxLines: 1,
                ),
                TaInputTextField(
                  title: 'Descrption',
                  controller: punchlistCntrl.addDescrpitionCtrl,
                  minLines: 3,
                  maxLines: 5,
                  keyboard: TextInputType.multiline,
                  textInputAct: TextInputAction.newline,
                ),
              ],
            ),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  punchlistCntrl.clearaddfieldsvalue();
                  Get.back();
                },
              ),
              SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                onPressed: () {
                  var seqnum = punchlistCntrl.addItemSeqCtrl.text;
                  if (seqnum == null || seqnum.isEmpty) {
                    ComponentUtils.showsnackbar(text: "Please enter Item sequence...");
                  } else {
                    punchlistCntrl.addPunchlistItem(entityid: entityId, entitytype: entityType);
                  }
                },
              ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  showactions() {
    return Get.defaultDialog(
        title: 'Actions',
        titleStyle: TextStyle(fontSize: 16),
        content: Container(
            margin: EdgeInsets.only(left: 15),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'All Not Applicable',
                      style: TextStyle(
                          //color: primary,
                          fontSize: 12),
                    ),
                    Obx(
                      () => Checkbox(
                        value: punchlistCntrl.allnotapplicableflag.value,
                        onChanged: (val) {
                          punchlistCntrl.allnotapplicableflag.value = val!;
                        },
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'All Complted',
                      style: TextStyle(
                          //color: primary,
                          fontSize: 12),
                    ),
                    Obx(
                      () => Checkbox(
                        value: punchlistCntrl.allcextattr1.value,
                        onChanged: (val) {
                          punchlistCntrl.allcextattr1.value = val!;
                        },
                      ),
                    ),
                  ],
                ),
              ],
            )),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: 'Cancel',
                onPressed: () {
                  punchlistCntrl.allnotapplicableflag.value = false;
                  punchlistCntrl.allcextattr1.value = false;
                  Get.back();
                },
              ),
              SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Save',
                onPressed: () {
                  punchlistCntrl.applyAllFlagActions(entityid: entityId, entitytype: entityType);
                },
              ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }
}
