import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/milestone_resource.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/providers/common/entitymilestones_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';

class MilestoneResourceData extends StatelessWidget {
  final int? milestoneid;
  String? mode;
  MilestoneResourceData({this.milestoneid, this.mode});
  LabelController talabel = Get.find<LabelController>();
  EntityMilestonesController mlsCntrl = Get.find<EntityMilestonesController>();

  Future _refreshresources() async {
    mlsCntrl.loadmilestoneresources(milestoneid);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Positioned.fill(
          child: RefreshIndicator(
            onRefresh: _refreshresources,
            child: GetX<EntityMilestonesController>(
              initState: (state) {
                Get.find<EntityMilestonesController>().loadmilestoneresources(milestoneid);
              },
              builder: (_) {
                return _.isresourcelistloading.isTrue
                    ? ProgressIndicatorCust()
                    : _.mlsresources.length < 1
                        ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return listitem(context, _.mlsresources[index]);
                            },
                            itemCount: _.mlsresources.length,
                          );
              },
            ),
          ),
        ),
        if (mode == 'edit')
          Positioned(
            bottom: 30,
            right: 30,
            child: FloatingActionButton(
              elevation: 5.0,
              child: const Icon(
                Icons.add,
                size: 30,
              ),
              onPressed: () {
                _showResourceform();
              },
            ),
          ),
      ],
    );
  }

  _showResourceform({String? type, MilestoneResource? r}) async {
    await mlsCntrl.getResourceRoleLov();
    debugPrint('--------------------------------------');
    if (type != null && type == 'edit') {
      debugPrint('resourceRole     ${r!.resourceRole}');
      mlsCntrl.selectedresorole.value = r.resourceRole!;
      mlsCntrl.resourcename.text = r.resourceName ?? '';
      mlsCntrl.resourceemail.text = r.email ?? '';
      mlsCntrl.resourceescnoti.value =
          (r.escalationNotification == 'N' || r.escalationNotification == null || r.escalationNotification == '') ? false : true;
      mlsCntrl.resourcecmpltnoti.value =
          (r.completionNotification == 'N' || r.completionNotification == null || r.completionNotification == '') ? false : true;
    } else {
      mlsCntrl.clearresourceform();
    }
    Get.defaultDialog(
        title: 'Resource',
        titleStyle: TextStyle(fontSize: 16),
        content: Container(
          // margin: EdgeInsets.only(left: 1),
          child: Obx(
            () => mlsCntrl.resorceformloading.value
                ? ProgressIndicatorCust()
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_ROLE') != null) resourceRoleLov(Get.context),
                      if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_NAME') != null)
                        TaInputTextField(
                          title: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_NAME')?.value,
                          controller: mlsCntrl.resourcename,
                          readOnly: mode == 'edit' ? false : true,
                          //keyboard: TextInputType.multiline,
                        ),
                      if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_EMAIL') != null)
                        TaInputTextField(
                          title: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_EMAIL')?.value,
                          controller: mlsCntrl.resourceemail, readOnly: mode == 'edit' ? false : true,
                          //keyboard: TextInputType.multiline,
                        ),
                      if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_ESCALATIONNOTIF') != null)
                        Container(
                          margin: EdgeInsets.only(left: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_ESCALATIONNOTIF')?.value ?? '',
                                style: TextStyle(
                                    //color: primary,
                                    fontSize: 12),
                              ),
                              Checkbox(
                                value: mlsCntrl.resourceescnoti.value,
                                onChanged: mode == 'edit'
                                    ? (val) {
                                        mlsCntrl.resourceescnoti.value = val!;
                                      }
                                    : null,
                              ),
                            ],
                          ),
                        ),
                      if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_COMPLETIONNOTIF') != null)
                        Container(
                          margin: EdgeInsets.only(left: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES_COMPLETIONNOTIF')?.value ?? '',
                                style: TextStyle(
                                    //color: primary,
                                    fontSize: 12),
                              ),
                              Checkbox(
                                value: mlsCntrl.resourcecmpltnoti.value,
                                onChanged: mode == 'edit'
                                    ? (val) {
                                        mlsCntrl.resourcecmpltnoti.value = val!;
                                      }
                                    : null,
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  Get.back();
                },
              ),
              if (mode == 'edit')
                SizedBox(
                  width: 4.0,
                ),
              if (mode == 'edit')
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                  onPressed: () {
                    int? resourceid = type == 'edit' ? r!.resourceId : 0;

                    mlsCntrl.addEditresource(milestoneid, type: type, resourceid: resourceid);
                  },
                ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  Widget listitem(BuildContext context, MilestoneResource mr) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    return GestureDetector(
      onTap: () {
        _showResourceform(type: 'edit', r: mr);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),

        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(children: [
                    Icon(
                      Icons.album,
                      color: secondary,
                      size: 15,
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(mr.resourceRoleDesc ?? '', style: TextStyle(color: primary, fontSize: 14, letterSpacing: .1)),
                  ]),
                  SizedBox(
                    height: 2,
                  ),
                  Row(children: [
                    Icon(
                      Icons.person,
                      color: secondary,
                      size: 15,
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(
                      mr.resourceName ?? '',
                      style: TextStyle(color: primary, fontSize: 10),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ]),
                  SizedBox(
                    height: 2,
                  ),
                  Row(children: [
                    Icon(
                      Icons.email,
                      color: secondary,
                      size: 15,
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(mr.email ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                  ]),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget resourceRoleLov(BuildContext? context) {
    return (mlsCntrl.resorolelov.isNotEmpty && mlsCntrl.resorolelov != null)
        ? DropdownButton(
            hint: Text(
              'Select Role',
              style: TextStyle(fontSize: 14, color: Colors.black),
            ),
            onChanged: mode == 'edit'
                ? (dynamic newValue) {
                    debugPrint('newValue          ' + newValue);
                    mlsCntrl.selectedresorole.value = newValue;
                    // mlsCntrl.generatebtnbinding.value = (newValue != null && newValue != '') ? true : false;
                  }
                : null,
            value: mlsCntrl.selectedresorole.value == '' ? null : mlsCntrl.selectedresorole.value,
            items: mlsCntrl.resorolelov.map((LookupValues l) {
              return DropdownMenuItem(
                child: new Text(
                  l.lookupValue!,
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
                value: l.lookupCode,
              );
            }).toList(),
          )
        : DropdownButton(
            hint: Text(
              'Select Role',
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
            },
            value: null,
            items: [
              DropdownMenuItem(
                  child: Text(
                    "Select",
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: null),
            ],
          );
  }
}
