import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/milestone.dart';
import 'package:tangoworkplace/providers/common/entitymilestones_controller.dart';
import 'package:tangoworkplace/screens/common/milestone/milestone_details.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';

class EntityMilestones extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  String? mode;
  EntityMilestones({Key? key, this.entityType, this.entityId, this.mode}) : super(key: key);
  EntityMilestonesController milstoneCntrl = Get.find<EntityMilestonesController>();

  Future _refreshMilestones() async {
    milstoneCntrl.loadEntityMilestones(entityType, entityId);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      // Row(
      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //   crossAxisAlignment: CrossAxisAlignment.center,
      //   children: [
      //     daterangelov(),
      //   ],
      // ),
      milstonelist(),
      // Positioned(
      //   bottom: 30,
      //   right: 30,
      //   child: FloatingActionButton(
      //     elevation: 5.0,
      //     child: const Icon(
      //       Icons.filter_alt_outlined,
      //       size: 30,
      //     ),
      //     onPressed: () {
      //       debugPrint('--------');
      //       scheduleSearch();
      //     },
      //   ),
      // ),

      Positioned(
        bottom: 30,
        right: 30,
        child: Obx(
          () => SpeedDialFloatingButton(icon: Icons.expand_less, iconButton: [
            FloatingIconButton(
                icon: Icons.filter_alt_outlined,
                onPressed: () {
                  scheduleSearch();
                }),
            if (milstoneCntrl.searchflag.isTrue)
              FloatingIconButton(
                  icon: Icons.filter_alt_off_outlined,
                  onPressed: () async {
                    milstoneCntrl.searchflag.value = false;
                    milstoneCntrl.searchController.text = '';
                    await milstoneCntrl.loadEntityMilestones(entityType, entityId);
                  }),
          ]),
        ),
      ),
    ]);
  }

  Widget milstonelist() {
    return Positioned.fill(
      top: 1,
      child: RefreshIndicator(
        onRefresh: _refreshMilestones,
        child: GetX<EntityMilestonesController>(
          initState: (state) {
            milstoneCntrl.loadEntityMilestones(entityType, entityId);
          },
          builder: (_) {
            return _.isloading.isTrue
                ? ProgressIndicatorCust()
                : _.milestones.length < 1
                    ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                    : ListView.builder(
                        itemBuilder: (context, index) {
                          return listitem(context, _.milestones[index]);
                        },
                        itemCount: _.milestones.length,
                      );
          },
        ),
      ),
    );
  }

  Widget listitem(BuildContext context, Milestone m) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(() => MilestoneDetails(
                mls: m,
                mode: this.mode,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
          padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                '${m.milestoneName}',
                //m.milestoneName ?? '',
                style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
              // Text(
              //   m.entitystatus ?? '',
              //   style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
              //   overflow: TextOverflow.ellipsis,
              // ),
              SizedBox(
                height: 5,
              ),
              Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  Text(
                    'Forecast:',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                  SizedBox(
                    width: 10.0,
                  ),
                  Text(
                    m.forecastEnd?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                ]),
                SizedBox(
                  width: 40.0,
                ),
                Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  Text(
                    'Actual:',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                  SizedBox(
                    width: 10.0,
                  ),
                  Text(
                    m.actualFinish?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                ]),
              ]),
              SizedBox(
                height: 5.0,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    m.milestoneType?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                  Text(
                    m.groupName?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                  Text(
                    m.percentComplete?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                ],
              )
            ],
          ),
        ));
  }

  scheduleSearch() {
    Get.defaultDialog(
        title: 'Search',
        titleStyle: TextStyle(fontSize: 16),
        content: Container(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TaInputText(
                title: 'Name',
                controller: milstoneCntrl.searchController,
              ),
              const SizedBox(
                height: 10,
              ),
              daterangelov(),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: 'Reset', //talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () async {
                  Get.back();
                  milstoneCntrl.searchflag.value = false;
                  milstoneCntrl.searchController.text = '';
                  await milstoneCntrl.loadEntityMilestones(entityType, entityId);
                },
              ),
              SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Search', //talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                onPressed: () async {
                  Get.back();
                  if (milstoneCntrl.searchController.text != '' && milstoneCntrl.searchController.text != null) {
                    milstoneCntrl.searchflag.value = true;
                  }
                  await milstoneCntrl.loadEntityMilestones(
                    entityType,
                    entityId,
                    searchText: milstoneCntrl.searchController.text,
                  );
                },
              ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  Widget daterangelov() {
    return Container(
      margin: const EdgeInsets.only(top: 5, right: 7),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Obx(
            () => TaDropSown(
              label: 'Select',
              initvalue: milstoneCntrl.daterange.value,
              items: <String>['ALL', 'WEEK', 'MONTH', 'PASTDUE'].map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value,
                    style: TextStyle(fontSize: 12, color: Colors.black),
                  ),
                );
              }).toList(),
              onChanged: (val) {
                milstoneCntrl.daterange.value = val;
                //milstoneCntrl.loadEntityMilestones(entityType, entityId);
              },
            ),
          ),
        ],
      ),
    );
  }
}
