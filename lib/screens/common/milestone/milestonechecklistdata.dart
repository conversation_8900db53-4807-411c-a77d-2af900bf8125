import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/milestone_checklist.dart';
import 'package:tangoworkplace/providers/common/entitymilestones_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';

class MilestoneChecklistData extends StatelessWidget {
  final int? milestoneid;
  String? mode;
  MilestoneChecklistData({this.milestoneid, this.mode});

  EntityMilestonesController mlsCntrl = Get.find<EntityMilestonesController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshchecklist() async {
    mlsCntrl.loadmilestonechecklist(milestoneid);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Positioned.fill(
          child: RefreshIndicator(
            onRefresh: _refreshchecklist,
            child: GetX<EntityMilestonesController>(initState: (state) async {
              await mlsCntrl.loadmilestonechecklist(milestoneid);
            }, builder: (_) {
              return _.ischecklistloading.value
                  ? const ProgressIndicatorCust()
                  : _.mlschecklist.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.mlschecklist[index]);
                          },
                          itemCount: _.mlschecklist.length,
                        );
            }),
          ),
        ),
        if (mode == 'edit')
          Positioned(
            bottom: 30,
            right: 30,
            child: FloatingActionButton(
              elevation: 5.0,
              child: const Icon(
                Icons.add,
                size: 30,
              ),
              onPressed: () {
                _showchecklistform();
              },
            ),
          ),
      ],
    );
  }

  _showchecklistform({String? type, MilestoneChecklist? c}) {
    if (type != null && type == 'edit') {
      mlsCntrl.chklistoption.value = (c!.checklistOption == 'N' || c.checklistOption == null || c.checklistOption == '') ? false : true;
      mlsCntrl.checklistname.text = c.checklistName ?? '';
    }
    Get.defaultDialog(
        title: type == 'edit' ? 'Edit Checklist' : 'Add Checklist',
        titleStyle: TextStyle(fontSize: 16),
        content: Container(
          // margin: EdgeInsets.only(left: 15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_CHECKLIST_OPTION') != null)
                Container(
                  margin: EdgeInsets.only(left: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_CHECKLIST_OPTION')?.value ?? '',
                        style: const TextStyle(
                            //color: primary,
                            fontSize: 12),
                      ),
                      Obx(
                        () => Checkbox(
                          value: mlsCntrl.chklistoption.value,
                          onChanged: mode == 'edit'
                              ? (val) {
                                  mlsCntrl.chklistoption.value = val!;
                                  //  return null;
                                }
                              : null,
                        ),
                      ),
                    ],
                  ),
                ),
              if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_CHECKLIST_NAME') != null)
                TaInputTextField(
                  title: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_CHECKLIST_NAME')?.value,
                  controller: mlsCntrl.checklistname,
                  keyboard: TextInputType.multiline,
                  readOnly: mode == 'edit' ? false : true,
                ),
            ],
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  Get.back();
                  mlsCntrl.clearchecklistform();
                },
              ),
              if (mode == 'edit')
                SizedBox(
                  width: 4.0,
                ),
              if (mode == 'edit')
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                  onPressed: () async {
                    int? checklistId = type == 'edit' ? c!.checklistId : 0;

                    await mlsCntrl.addEditchecklist(milestoneid, type: type, checklistId: checklistId);
                  },
                ),
              SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  Widget listitem(BuildContext context, MilestoneChecklist mc) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    return GestureDetector(
      onTap: () {
        _showchecklistform(type: 'edit', c: mc);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        mc.checklistName!,
                        style: TextStyle(
                            color: primary,
                            //fontWeight: FontWeight.bold,
                            fontSize: 14),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Row(children: [
                        Icon(
                          Icons.person,
                          color: secondary,
                          size: 15,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Text(mc.lastUpdatedBy ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                      ]),
                      SizedBox(
                        height: 2,
                      ),
                      Row(children: [
                        Icon(
                          Icons.date_range,
                          color: secondary,
                          size: 15,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Text(mc.lastUpdateDate ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                      ]),
                    ],
                  ),
                  Checkbox(
                    value: (mc!.checklistOption == 'N' || mc.checklistOption == null || mc.checklistOption == '') ? false : true,
                    onChanged: null,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
