import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputdate.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/milestone.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/common/entitymilestones_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/comments/entity_commentspg.dart';
import 'package:tangoworkplace/screens/common/milestone/milestonechecklistpg.dart';
import 'package:tangoworkplace/screens/common/milestone/milestoneresourcepg.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';

class MilestoneDetails extends StatelessWidget {
  Milestone? mls;
  String? mode;
  MilestoneDetails({Key? key, this.mls, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  Applabel? label;
  EntityMilestonesController milstoneCntrl = Get.find<EntityMilestonesController>();
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    milstoneCntrl.labelsloading.value = true;
    milstoneCntrl.forecaststartCtrl.text = ComponentUtils.dateToString(mls!.forecastStart);
    milstoneCntrl.forecastendCtrl.text = ComponentUtils.dateToString(mls!.forecastEnd);
    milstoneCntrl.actualstartCtrl.text = ComponentUtils.dateToString(mls!.actualStart);
    milstoneCntrl.actualfinishCtrl.text = ComponentUtils.dateToString(mls!.actualFinish);

    return GetX<LabelController>(
      initState: (state) {
        Future.delayed(Duration.zero, () async {
          milstoneCntrl.oldmilestone.value = mls ?? Milestone();
          await milstoneCntrl.getlabels(talabel);
          milstoneCntrl.loadmilestonechecklist(mls!.milestoneId);
        });
      },
      builder: (_) {
        return milstoneCntrl.labelsloading.value
            ? ComponentUtils.labelLoadScaffold()
            : Scaffold(
                appBar: AppBar(
                  iconTheme: Theme.of(context).appBarTheme.iconTheme,
                  title: Text(
                    talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS')?.value ?? '',
                    style: ComponentUtils.appbartitlestyle,
                  ),
                  backgroundColor: Colors.white,
                  elevation: 5,
                  leading: new IconButton(
                    icon: ComponentUtils.backpageIcon,
                    color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                    onPressed: () {
                      debugPrint('------back-------');
                      Get.back();
                      milstoneCntrl.loadEntityMilestones(mls!.entityType, mls!.entityId);
                    },
                  ),
                ),
                body: Form(key: _formKey, autovalidateMode: AutovalidateMode.onUserInteraction, child: milestoneDetForm()),
                bottomNavigationBar: bottombuttonbar(_formKey),
              );
      },
    );
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            TaButton(
              type: 'elevate',
              buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_BACK')?.value ?? 'Back',
              onPressed: () {
                Get.back();
                milstoneCntrl.loadEntityMilestones(mls!.entityType, mls!.entityId);
              },
            ),
            if (mode == 'edit')
              const SizedBox(
                width: 10,
              ),
            if (mode == 'edit')
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                onPressed: () {
                  final isValid = fkey.currentState!.validate();
                  if (!isValid) {
                    return;
                  }

                  fkey.currentState!.save();
                  debugPrint('------------------saved--------------------');
                  milstoneCntrl.onDetFormsave(mls!);

                  //_saveSrForm();
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget milestoneDetForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_MILESTONENAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_MILESTONENAME')!.value,
              value: mls!.milestoneName ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_GROUPNAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_GROUPNAME')!.value,
              value: mls!.groupName,
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_MILESTONETYPE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_MILESTONETYPE')!.value,
              value: mls!.milestoneType,
              readOnly: true,
            ),
          // TaFormInputDate(
          //   label: 'Forecast Start',
          //   //value: punch.dueDate ?? '',
          //   controller: milstoneCntrl.forecaststartCtrl,
          //   onChanged: (dateval) {
          //     debugPrint('dateval--------- $dateval');
          //     milstoneCntrl.forecaststartCtrl.text = dateval.toString();
          //   },
          //   onSaved: (val) {
          //     milstoneCntrl.forecaststartCtrl.text = val;
          //   },
          //   readOnly: true,
          // ),
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_FORECAST') != null)
            TaFormInputDate(
              label: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_FORECAST')!.value,
              controller: milstoneCntrl.forecastendCtrl,
              onChanged: (dateval) {
                debugPrint('dateval--------- $dateval');
                milstoneCntrl.forecastendCtrl.text = dateval.toString();
              },
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_FORECAST')!.ro : true,
              onSaved: (val) {
                milstoneCntrl.forecastendCtrl.text = val;
              },
            ),
          // TaFormInputDate(
          //   label: 'Actual Start',
          //   //value: punch.dueDate ?? '',
          //   controller: milstoneCntrl.actualstartCtrl,
          //   onChanged: (dateval) {
          //     debugPrint('dateval--------- $dateval');
          //     milstoneCntrl.actualstartCtrl.text = dateval.toString();
          //   },
          //   onSaved: (val) {
          //     milstoneCntrl.actualstartCtrl.text = val;
          //   },
          //   readOnly: true,
          // ),
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_ACTUAL') != null)
            TaFormInputDate(
              label: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_ACTUAL')!.value,
              readOnly: (mode == 'edit' && mls?.milestoneType != 'Excluded')
                  ? talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_ACTUAL')!.ro
                  : true,
              controller: milstoneCntrl.actualfinishCtrl,
              onChanged: (dateval) {
                debugPrint('dateval1--------- $dateval');
                // milstoneCntrl.actualfinishCtrl.text = dateval.toString();
                if (milstoneCntrl.onMilestoneActualDateValidation()) {
                  milstoneCntrl.actualfinishCtrl.text = dateval.toString();
                } else {
                  ComponentUtils.showpopup(msg: 'Checklist is not completed', type: 'Error');
                }
              },
              onSaved: (val) {
                debugPrint('dateval2--------- $val');
                milstoneCntrl.actualfinishCtrl.text = val;
              },
              lastDate: DateTime.now(),
              // validate: (value) {
              //   if (value != null) {
              //     milstoneCntrl.actualfinishCtrl.text = '';
              //     return 'Checklist is not completed';
              //   }
              //   return null;
              // },
            ),

          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_COMPLETE') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_COMPLETE')!.value,
              value: mls!.percentComplete?.toString() ?? '0',
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_DETAILS_COMPLETE')!.ro : true,
              keyboard: TextInputType.number,
              onSaved: (val) {
                milstoneCntrl.percentComplete.value = ComponentUtils.StrToDouble(val)!;
              },
              validate: (val) {
                if (val == null || val == '') {
                  return 'Please enter a value.';
                } else if (val != null && double.parse(val) > 100) {
                  return 'value cannot be exceed more than 100.';
                }
              },
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_COMMENTS') != null)
            comments(talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_COMMENTS')!.value!),
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_CHECKLIST') != null)
            checklist(talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_CHECKLIST')!.value!),
          if (talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES') != null)
            resources(talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES')!.value!),
        ],
      ),
    );
  }

  Widget comments(String labelStr) {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 5, 15, 3),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => EntityCommentsPG(
                    subentityid: mls!.milestoneId,
                    entityType: 'PROJECT_MILESTONE',
                    entityId: mls!.entityId,
                    mode: mode,
                  ));
              // Get.defaultDialog(
              //   title: 'Checklist',
              //   titleStyle: TextStyle(fontSize: 16),
              //   content: MilestoneChecklistData(milestoneid: mls.milestoneId),
              // );
            },
            icon: Icon(
              Icons.message,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget checklist(String labelStr) {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 2, 15, 2),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => MilestoneChecklistPG(
                    milestoneid: mls!.milestoneId,
                    mode: mode,
                  ));
              // Get.defaultDialog(
              //   title: 'Checklist',
              //   titleStyle: TextStyle(fontSize: 16),
              //   content: MilestoneChecklistData(milestoneid: mls.milestoneId),
              // );
            },
            icon: Icon(
              Icons.checklist,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget resources(String labelStr) {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 3, 15, 2),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => MilestoneResourcePG(
                    milestoneid: mls!.milestoneId,
                    mode: mode,
                  ));
              // Get.defaultDialog(
              //   title: 'Resources',
              //   titleStyle: TextStyle(fontSize: 16),
              //   content: MilestoneResourceData(milestoneid: mls.milestoneId),
              // );
            },
            icon: Icon(
              Icons.people_alt,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }
}
