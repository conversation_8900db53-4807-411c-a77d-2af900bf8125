import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/milestone/milestoneresourcedata.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';

class MilestoneResourcePG extends StatelessWidget {
  final int? milestoneid;
  String? mode;
  MilestoneResourcePG({Key? key, this.milestoneid, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(
          talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_RESOURCES')?.value ?? '',
          style: ComponentUtils.appbartitlestyle,
        ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: MilestoneResourceData(
        milestoneid: milestoneid,
        mode: mode,
      ),
    );
  }
}
