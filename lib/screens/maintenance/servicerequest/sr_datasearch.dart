import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/common/entity_data.dart';
import '../../../models/servicerequest/sr_assetinstance.dart';
import '../../../models/servicerequest/sr_space.dart';
import '../../../providers/maintenance/servicerequest_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';

class SrDatasearch extends GetView<ServiceRequestController> {
  final stype;
  final hdrtext;
  final onselect;
  SrDatasearch({Key? key, this.stype, this.hdrtext, this.onselect}) : super(key: key);

  ServiceRequestController srCtrl = Get.find<ServiceRequestController>();
  LabelController talabel = Get.find<LabelController>();
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(
          hdrtext,
          style: ComponentUtils.appbartitlestyle,
        ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            srCtrl.iptlovsearchCtrl.text = '';
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          if (stype == 'location' || stype == 'space' || stype == 'asset_instance')
            TaSearchInputText(
                makeSearch: (searchtext) async {
                  if (stype == 'location')
                    await srCtrl.getStoreEntityData(filter: searchtext.toString());
                  else if (stype == 'space')
                    await srCtrl.getSpacesData(
                        srCtrl.servicerequest.value.entityId?.toString(), srCtrl.servicerequest.value.floorId?.toString(),
                        filter: searchtext?.toString());
                  else if (stype == 'asset_instance') await srCtrl.getAssetInstanceData(filter: searchtext?.toString());
                },
                searchController: srCtrl.iptlovsearchCtrl,
                hintSearch: 'Search '),
          Expanded(
            //child: RefreshIndicator(
            //onRefresh: _refreshProjects,
            child: GetX<ServiceRequestController>(
              initState: (state) async {
                if (stype == 'location')
                  await srCtrl.getStoreEntityData(filter: '');
                else if (stype == 'space')
                  await srCtrl.getSpacesData(
                      srCtrl.servicerequest.value.entityId?.toString(), srCtrl.servicerequest.value.floorId?.toString());
                else if (stype == 'asset_instance') await srCtrl.getAssetInstanceData();
              },
              builder: (_) {
                return stype == 'location' ? locationList(_) : (stype == 'space' ? spaceList(_) : assetInstanceList(_));
              },
            ),
            //),
          ),
        ],
      ),
    );
  }

  Widget locationList(ServiceRequestController _) {
    return _.srinptlovDataLoading.isTrue
        ? ProgressIndicatorCust()
        : _.entityList.length < 1
            ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
            : ListView.builder(
                itemBuilder: (context, index) {
                  return loclistitem(context, _.entityList[index]);
                },
                itemCount: _.entityList.length,
              );
  }

  Widget spaceList(ServiceRequestController _) {
    return _.srinptlovDataLoading.isTrue
        ? ProgressIndicatorCust()
        : _.spacesList.length < 1
            ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
            : ListView.builder(
                itemBuilder: (context, index) {
                  return spacelistitem(context, _.spacesList[index]);
                },
                itemCount: _.spacesList.length,
              );
  }

  Widget assetInstanceList(ServiceRequestController _) {
    return _.srinptlovDataLoading.isTrue
        ? ProgressIndicatorCust()
        : _.assetinstList.length < 1
            ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
            : ListView.builder(
                itemBuilder: (context, index) {
                  return assetInstancelistitem(context, _.assetinstList[index]);
                },
                itemCount: _.assetinstList.length,
              );
  }

  Widget loclistitem(BuildContext context, EntityData e) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          srCtrl.iptlovsearchCtrl.text = '';
          Get.back();

          debugPrint('entityid----------${e.entityId}');
          srCtrl.servicerequest.value.entityId = e.entityId;
          srCtrl.servicerequest.value.storeName = e.entityName;
          srCtrl.servicerequest.value.floorId = null;

          debugPrint('entity name----------${e.entityName}');
          srCtrl.iptlovsearchCtrl.text = '';
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          new Container(
                            padding: EdgeInsets.only(right: 20.0),
                            child: Text(
                              e.entityNumber ?? '',
                              style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: DeviceUtils.taFontSize(1.5, context)),
                            ),
                          ),
                          Flexible(
                            child: Text(
                              '${e.entityName}',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ]),
                    SizedBox(height: 6.0, width: 0),
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            e.address?.toString() ?? '',
                            style: TextStyle(color: primary, fontSize: 10),
                          ),
                        ]),
                    SizedBox(height: 2.0, width: 0),
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(e.city ?? '',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                              )),
                          Text(e.state ?? '',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                              )),
                        ]),
                    // SizedBox(
                    //   height: 0.0,
                    // ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget spacelistitem(BuildContext context, SrSpace s) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.back();

          srCtrl.servicerequest.value.spaceId = s.spaceId;
          srCtrl.servicerequest.value.spaceDisplayName = s.spaceDisplayName;

          srCtrl.servicerequest.value.assetInstanceName = null;
          srCtrl.servicerequest.value.assetInstanceId = null;
          srCtrl.assetInstanceNameCtrl.text = '';

          debugPrint('floor name----------${srCtrl.servicerequest.value.floorId} - ${s.floorName}');

          debugPrint('space name----------${s.spaceDisplayName}');
          srCtrl.iptlovsearchCtrl.text = '';

          this.onselect(s);
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text('${s.spaceDisplayName}',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                fontWeight: FontWeight.bold,
                              )),
                          Text('${s.spaceNumber}',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                fontWeight: FontWeight.bold,
                              )),
                        ]),
                    SizedBox(height: 4.0, width: 0),
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(s.spaceCategory ?? '',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                //fontWeight: FontWeight.bold,
                              )),
                          Text(s.spaceUse ?? '',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                // fontWeight: FontWeight.bold,
                              )),
                        ]),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget assetInstancelistitem(BuildContext context, SrAssetInstance a) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.back();

          srCtrl.servicerequest.value.assetInstanceId = a.assetInstanceId;
          srCtrl.servicerequest.value.assetInstanceName = a.assetInstanceName;

          debugPrint('assetInstanceName----------${a.assetInstanceName}');
          srCtrl.iptlovsearchCtrl.text = '';
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Column(children: [
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_NAME') != null)
              ComponentUtils.listVertRow(
                  talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_NAME')!.value!, '${a.assetInstanceName ?? ''}'),
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_NUMBER') != null)
              ComponentUtils.listVertRow(talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_NUMBER')!.value!,
                  a.assetInstanceNumber?.toString() ?? ''),
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_ASSETTYPE') != null)
              ComponentUtils.listVertRow(
                  talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_ASSETTYPE')!.value!, '${a.assetType ?? ''}'),
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_MASTERNAME') != null)
              ComponentUtils.listVertRow(
                  talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_MASTERNAME')!.value!, a.assetmastername ?? ''),
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_MANUFACTURER') != null)
              ComponentUtils.listVertRow(
                  talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCESEARCH_MANUFACTURER')!.value!, a.manufacturer ?? ''),
          ]),

          // Row(
          //   crossAxisAlignment: CrossAxisAlignment.start,
          //   children: <Widget>[
          //     Expanded(
          //       child: Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: <Widget>[
          //           Row(
          //               mainAxisSize: MainAxisSize.max,
          //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //               crossAxisAlignment: CrossAxisAlignment.center,
          //               children: [
          //                 Text('${a.assetInstanceName}',
          //                     style: TextStyle(
          //                       color: primary,
          //                       fontSize: DeviceUtils.taFontSize(1.5, context),
          //                       fontWeight: FontWeight.bold,
          //                     )),
          //                 Text('${a.assetType}',
          //                     style: TextStyle(
          //                       color: primary,
          //                       fontSize: DeviceUtils.taFontSize(1.5, context),
          //                       fontWeight: FontWeight.bold,
          //                     )),
          //               ]),
          //           SizedBox(height: 4.0, width: 0),
          //           Row(
          //               mainAxisSize: MainAxisSize.max,
          //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //               crossAxisAlignment: CrossAxisAlignment.center,
          //               children: [
          //                 Text(a.assetmastername ?? '',
          //                     style: TextStyle(
          //                       color: primary,
          //                       fontSize: DeviceUtils.taFontSize(1.5, context),
          //                       //fontWeight: FontWeight.bold,
          //                     )),
          //                 Text(a.manufacturer ?? '',
          //                     style: TextStyle(
          //                       color: primary,
          //                       fontSize: DeviceUtils.taFontSize(1.5, context),
          //                       // fontWeight: FontWeight.bold,
          //                     )),
          //               ]),
          //         ],
          //       ),
          //     )
          //   ],
          // ),
        ));
  }
}
