import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/screens/login_screen.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import '../common/component_utils.dart';
import '../common/widgets/components.dart';
import '../utils/common_utils.dart';
import '../utils/preferences_utils.dart';

class SettingsForm extends StatefulWidget {
  static const routName = '/settings';
  @override
  SettingsFormState createState() {
    return SettingsFormState();
  }
}

class SettingsFormState extends State<SettingsForm> {
  final _settingformKey = GlobalKey<FormState>();
  final myController = TextEditingController();
  final List hostlist = CommonUtils.hostlist;
  @override
  void initState() {
    super.initState();
    getVal();
  }

  void getVal() {
    String? text = SharedPrefUtils.readPrefStr(ConstHelper.hostNameVar);
    if (text != null) {
      debugPrint('gethostname:' + text);
      myController.text = text;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Build a Form widget using the _formKey created above.
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(
          'Environment Settings',
          style: ComponentUtils.appbartitlestyle,
        ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Container(
        padding: EdgeInsets.all(20.0),
        child: Form(
            key: _settingformKey,
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
              TextFormField(
                controller: myController,
                decoration: InputDecoration(labelText: 'Host Name(tangoanalytics.com)'),
                // The validator receives the text that the user has entered.
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Please enter the host name';
                  } else if (!hostlist.contains(value?.toUpperCase())) {
                    return 'Please enter correct host name';
                  }
                  return null;
                },
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        final isValid = _settingformKey.currentState!.validate();
                        if (!isValid) {
                          return;
                        }

                        _settingformKey.currentState!.save();
                        _submitform(myController.text, context);
                      },
                      child: Text('Save'),
                    ),
                  ],
                ),
              ),
            ])),
      ),
    );
  }
}

_submitform(String text, BuildContext ctx) {
  debugPrint('Inside save hostname $text');

  SharedPrefUtils.saveStr(ConstHelper.hostNameVar, text);
  SharedPrefUtils.clear();

  debugPrint('Read hostname ${SharedPrefUtils.readPrefStr(ConstHelper.hostNameVar)}');

  Navigator.of(ctx).pushNamed(LoginPage.routName);
}
