import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/aq_widgets/aq_devider.dart';
import 'package:tangoworkplace/common/widgets/aq_widgets/aq_space.dart';
import 'package:tangoworkplace/models/agilequest/types/dates/aq_date_pattern_type.dart';
import 'package:tangoworkplace/providers/agilequest/rating/aq_create_review_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/agilequest/review/aq_create_review_screen.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../models/agilequest/reviews/aq_review_view.dart';
import '../../../providers/agilequest/aqhome_controller.dart';
import '../../../providers/agilequest/rating/aq_review_controller.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';

import '../../../providers/agilequest/user/aq_user_extensions.dart';

class AqReviewScreen extends GetView<AqReviewController> {
  final LabelController talabel = Get.find<LabelController>();
  final AqHomeController aqHomeCtrl = Get.find<AqHomeController>();
  final int resourceId;

  AqReviewScreen({Key? key, required this.resourceId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(AqCreateReviewController());
    controller.initState();
    controller.uploadComments(resourceId);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
      padding: const EdgeInsets.only(top: 20, left: 15, right: 15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Obx(
        () => controller.isLoading.value
            ? const SizedBox(
                height: 100.0, child: Center(child: ProgressIndicatorCust()))
            : Column(
                children: [
                  const AqSpace(),
                  Row(
                    children: [
                      Text(
                        talabel.getAq(
                                key: 'aq.mobile.review.form.rating.title',
                                defaultTxt: 'Rating')!.tag,
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 24),
                      ),
                      const AqSpace(),
                      _ratingView(controller.ratingValue.value),
                      Text(
                        '(${controller.amountValue.value.toString()})',
                        style: const TextStyle(fontSize: 20),
                      ),
                      Expanded(child: Container()),
                      TaButton(
                        type: 'elevate',
                        buttonText: talabel.getAq(
                                key: 'aq.mobile.common.create.review',
                                defaultTxt: 'Create a Review')!.tag,
                        onPressed: () {
                          _showCreateReviewDialog(context, resourceId);
                        },
                      ),
                    ],
                  ),
                  const AqDevider(),
                  Flexible(
                    child: ListView.builder(
                      controller: controller.scrollController,
                      itemCount: controller.isLoadingMore.value
                          ? controller.reviewsList.length + 1
                          : controller.reviewsList.length,
                      scrollDirection: Axis.vertical,
                      shrinkWrap: true,
                      itemBuilder: (BuildContext context, int index) {
                        if (controller.reviewsList.length == index &&
                            controller.hasNext) {
                          return const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        } else {
                          final review = controller.reviewsList[index];
                          return _buildReviewCard(review, context);
                        }
                      },
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Future<void> showDeleteConfirmationDialog(
      BuildContext context, bool isHide, VoidCallback onSubmitted) {
    return showDialog(
        context: context,
        builder: (ctx) => AlertDialog(
              title: const Text("Delete Review"),
              content: isHide
                  ? Text(talabel
                      .getAq(
                          key: 'aq.mobile.review.hide.message',
                          defaultTxt:
                              'Are you sure you want to hide this review? It will be hidden from all users.')!
                      .tag)
                  : Text(talabel
                      .getAq(
                          key: 'aq.mobile.review.delete.message',
                          defaultTxt:
                              'Are you sure you want to remove this review?')!
                      .tag),
              actions: <Widget>[
                TextButton(
                  child: const Text('Yes'),
                  onPressed: () {
                    onSubmitted();
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('No'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ));
  }

  Future<void> _showCreateReviewDialog(BuildContext context, int resourceId,
      {AqReviewView? review}) {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: AqCreateReviewScreen(
            resourceId: resourceId,
            reviewToEdit: review,
            onSubmitted: () {
              controller.refreshReviews();
            },
          ),
        );
      },
    );
  }

  Widget _buildReviewCard(AqReviewView review, BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _ratingView(review.rating.toDouble()),
            const AqSpace(),
            Text(
              review.aqCreateTime?.displayDateString(talabel,
                      withTimeZone: false,
                      pattern: AqDatePatternType.SHORT_DATE) ??
                  '',
              style: const TextStyle(fontSize: 17),
            )
          ],
        ),
        if (review.comment?.isNotEmpty == true) const AqSpace(),
        if (review.comment?.isNotEmpty == true)
          Text(
            review.comment ?? '',
            style: const TextStyle(fontSize: 17),
          ),
        const AqSpace(),
        review.preferredName != null
            ? Text(
                '${review.preferredName} ${review.lastName}',
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 17),
              )
            : Text('${review.firstName} ${review.lastName}',
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 17)),
        if (review.aqStartTime != null)
          Text(
            '${talabel.getAq(key: '', defaultTxt: 'Reserved on')!.tag} ${review.aqStartTime?.displayDateString(talabel, withTimeZone: false, pattern: AqDatePatternType.SHORT_DATE) ?? ''}',
            style: const TextStyle(fontWeight: FontWeight.w200),
          ),
        if (review.sysidUser ==
            (aqHomeCtrl.aqUserData.value.sysidApplicationUser ?? -1))
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.getAq(key: '', defaultTxt: 'Edit')!.tag,
                onPressed: () {
                  _showCreateReviewDialog(context, resourceId, review: review);
                },
              ),
              const AqSpace(),
              TaButton(
                type: 'elevate',
                buttonText: talabel.getAq(key: '', defaultTxt: 'Remove')!.tag,
                onPressed: () async {
                  await showDeleteConfirmationDialog(context, false, () {
                    controller.removeReview(review.sysidReview ?? 0);
                  });
                },
              ),
            ],
          ),
        if (review.sysidUser !=
                (aqHomeCtrl.aqUserData.value.sysidApplicationUser ?? -1) &&
            AqUserExtension.au.canManageReviews())
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.getAq(key: '', defaultTxt: 'Hide')!.tag,
                onPressed: () async {
                  await showDeleteConfirmationDialog(context, true, () {
                    controller.removeReview(review.sysidReview ?? 0);
                  });
                },
              ),
            ],
          ),
        const AqDevider(),
      ],
    );
  }

  Widget _ratingView(double selectedRate) {
    return Row(
        children:
            List.generate(5, (index) => _ratingIcon(index, selectedRate)));
  }

  Widget _ratingIcon(int position, double selectedRate) {
    final icon = (position + 1 <= selectedRate)
        ? SvgPicture.asset(
            'lib/icons/star_selected.svg',
          )
        : (() {
            var restNumber = selectedRate - selectedRate.toInt();
            var isUnselected = (position + 1) >= selectedRate + 1;
            if (restNumber <= 0 || isUnselected) {
              return SvgPicture.asset(
                'lib/icons/star_unselected.svg',
              );
            } else if (restNumber < 0.4) {
              return SvgPicture.asset(
                'lib/icons/less_half_selected.svg',
              );
            } else if (restNumber > 0.6) {
              return SvgPicture.asset(
                'lib/icons/more_half_selected.svg',
              );
            } else {
              return SvgPicture.asset(
                'lib/icons/half_selected_star.svg',
              );
            }
          })();
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: SizedBox(
        width: 15.0,
        height: 15.0,
        child: icon,
      ),
    );
  }
}
