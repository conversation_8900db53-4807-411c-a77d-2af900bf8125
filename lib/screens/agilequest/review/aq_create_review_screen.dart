import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/aq_widgets/aq_devider.dart';
import 'package:tangoworkplace/models/agilequest/reviews/aq_review_view.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/aq_widgets/aq_space.dart';
import '../../../common/widgets/component_widgets/tacheckbox.dart';
import '../../../common/widgets/component_widgets/taforminputdate.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/agilequest/rating/aq_create_review_controller.dart';

class AqCreateReviewScreen extends GetView<AqCreateReviewController> {
  final LabelController talabel = Get.find<LabelController>();
  final int resourceId;
  final AqReviewView? reviewToEdit;
  final VoidCallback onSubmitted;

  AqCreateReviewScreen({Key? key, required this.resourceId, this.reviewToEdit, required this.onSubmitted}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    controller.initialSetup(reviewToEdit, resourceId);
    return NestedScrollView(
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          SliverAppBar(
            elevation: 0,
            title: reviewToEdit != null
                ? Text(talabel.getAq(key: 'aq.mobile.review.form.edit.title', defaultTxt: 'Edit Review')!.tag)
                : Text(talabel.getAq(key: 'aq.mobile.review.form.create.title', defaultTxt: 'Create Review')!.tag),
            backgroundColor: Colors.white,
            titleTextStyle: ComponentUtils.appbartitlestyle,
            stretch: true,
            expandedHeight: 30.0,
            floating: false,
            pinned: true,
          ),
        ];
      },
      body: Material(
        elevation: 5,
        color: ComponentUtils.bodybackground,
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          padding: const EdgeInsets.only(top: 20, left: 15, right: 15),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          child: _reviewWidget(),
        ),
      ),
    );
  }

  Widget _reviewWidget() {
    return SingleChildScrollView(
      child: Obx(
        () => controller.isLoading.value
            ? const SizedBox(height: 100.0, child: Center(child: ProgressIndicatorCust()))
            : Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                    Text(
                      talabel.getAq(key: 'aq.mobile.review.form.rating.title', defaultTxt: 'Rating')!.tag,
                      style: const TextStyle(fontSize: 18),
                    ),
                    const AqSpace(),
                    _ratingView(controller.ratingValueSelected.value),
                    const AqSpace(),
                    const AqDevider(),
                    Text(
                      talabel.getAq(key: 'aq.mobile.review.form.comment.title', defaultTxt: 'Comment')!.tag,
                      style: const TextStyle(fontSize: 18),
                    ),
                    const AqSpace(),
                    TaTextFormField(
                      controller: controller.commentController,
                      onSaved: (val) {
                        controller.commentChanged(val);
                      },
                      onSubmitField: (val) {
                        controller.commentChanged(val);
                      },
                      hintText: talabel
                          .getAq(
                              key: 'aq.mobile.review.form.comment.hint',
                              defaultTxt: 'Be specific. Leave comments that will help others when reserving this asset.')!
                          .tag,
                      maxLines: 20,
                      minLines: 10,
                    ),
                    const AqSpace(),
                    const AqDevider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: Text(
                            talabel.getAq(key: 'aq.mobile.review.form.date.of.reservation', defaultTxt: 'Date of Reservation')!.tag,
                            style: const TextStyle(fontSize: 18),
                          ),
                        ),
                        Flexible(
                          fit: FlexFit.tight,
                          child: CheckboxListTileFormField(
                            initialValue: controller.isReservationDateSwitched.value,
                            onChanged: (value) {
                              controller.switchDateReservation(value ?? false);
                            },
                          ),
                        ),
                      ],
                    ),
                    if (controller.isReservationDateSwitched.value)
                      TaFormInputDate(
                        controller: controller.dateOfReservationController,
                        dateSelect: controller.dateOfReservationSelected.value.toDate(),
                        onChanged: (date) {
                          controller.dateOfReservationChanged(date);
                        },
                        readOnly: false,
                        onSaved: (val) {
                          controller.dateOfReservationChanged(val);
                        },
                        lastDate: DateTime.now(),
                      ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          TaButton(
                            type: 'elevate',
                            buttonText: talabel.getAq(key: 'aq.web.dialogs.cmd.cancel', defaultTxt: 'Cancel')!.tag,
                            readOnly: false,
                            onPressed: () => Get.back(),
                          ),
                          const SizedBox(
                            width: 16,
                          ),
                          TaButton(
                            type: 'elevate',
                            buttonText: talabel.getAq(key: '', defaultTxt: 'Submit')!.tag,
                            readOnly: controller.ratingValueSelected.value == 0,
                            onPressed: () async {
                              await controller.submitData();
                              onSubmitted();
                              Get.back();
                            },
                          ),
                        ],
                      ),
                    ),
                  ]),
      ),
    );
  }

  Widget _ratingView(int selectedRate) {
    return Row(
      children: List.generate(
        5,
        (index) => _ratingIcon(index, selectedRate, () {
          controller.ratingChanged(index);
        }),
      ),
    );
  }

  Widget _ratingIcon(int position, int selectedRate, VoidCallback onSelected) {
    final icon = (position + 1 <= selectedRate)
        ? SvgPicture.asset(
            'lib/icons/star_selected.svg',
          )
        : (() {
            var restNumber = selectedRate - selectedRate.toInt();
            var isUnselected = (position + 1) >= selectedRate + 1;
            if (restNumber <= 0 || isUnselected) {
              return SvgPicture.asset(
                'lib/icons/star_unselected.svg',
              );
            }
          })();
    return GestureDetector(
      onTap: onSelected,
      child: Padding(
        padding: const EdgeInsets.all(2.0),
        child: SizedBox(
          width: 35.0,
          height: 35.0,
          child: icon,
        ),
      ),
    );
  }
}
