import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/resource/aq_resource_images_data.dart';
import 'package:tangoworkplace/providers/agilequest/photo/aq_photo_viewer_controller.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../common/widgets/dots_indicator.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';

class AqPhotoViewerScreen extends GetView<AqPhotoViewerController> {
  final LabelController talabel = Get.find<LabelController>();
  final List<AqResourceImagesData> imagesToDisaply;

  AqPhotoViewerScreen({Key? key, required this.imagesToDisaply}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    controller.initialSetup();

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(talabel.getAq(key: '', defaultTxt: 'Photos')!.tag),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        titleTextStyle: ComponentUtils.appbartitlestyle,
      ),
      body: Material(elevation: 5, color: ComponentUtils.darkBackground, child: _buildPhotoPager()),
    );
  }

  Widget _buildPhotoPager() {
    return Column(
      children: [
        Expanded(
          child: PageView.builder(
              controller: controller.pageController,
              itemBuilder: (BuildContext context, int itemIndex) {
                return _buildPhotoItem(itemIndex);
              }),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Obx(
            () => DotsIndicator(
              itemCount: imagesToDisaply.length,
              selectedPosition: controller.imageIndexSelected.value,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPhotoItem(int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Image.network(
            imagesToDisaply[index].imageSource ?? '',
            fit: BoxFit.contain,
            height: double.infinity,
            width: double.infinity,
            errorBuilder: (context, error, stackTrace) => const Image(
              image: AssetImage('lib/icons/aq_tango_rsrv.jpg'),
            ),
          ),
        ),
        SingleChildScrollView(
          child: Container(
            color: ComponentUtils.darkBackground.withAlpha(200),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                imagesToDisaply[index].description ?? '',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
