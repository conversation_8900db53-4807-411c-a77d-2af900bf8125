import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/agilequest/reserve_manage/aq_manage_reserve_screen.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import '../../../common/component_utils.dart';
import '../../../providers/agilequest/aqhome_controller.dart';
import '../../home/<USER>';

class AqHome extends StatelessWidget {
  AqHome({Key? key}) : super(key: key);
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  //AqHomeController aqhState = Get.put(AqHomeController());
  AqHomeController aqHomeCtrl = Get.find<AqHomeController>();
  late double sWidth;

  AqHomeController aqhomeCtrlr = Get.find<AqHomeController>();
  LabelController talabel = Get.find<LabelController>();

  @override
  Widget build(BuildContext context) {
    int cac = iconsizesetup();
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Tango Reserve Home', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        // actions: [
        //   IconButton(
        //     color: ComponentUtils.primecolor,
        //     icon: const Icon(Icons.my_location),
        //     onPressed: () async {
        //       Position p = await _determinePosition();
        //       debugPrint('latitude     ${p.latitude}');
        //     },
        //   ),
        // ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
      ),
      key: _scaffoldKey,
      body: Container(
        padding: EdgeInsets.fromLTRB(0, 10, 0, 0),
        // color: Colors.white,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
          child: Column(
            children: [
              SectionTile(
                child: GridView.count(
                    physics: NeverScrollableScrollPhysics(),
                    crossAxisCount: cac,
                    childAspectRatio: 1.0,
                    //padding: const EdgeInsets.all(10.0),
                    mainAxisSpacing: 8,
                    crossAxisSpacing: 8,
                    shrinkWrap: true,
                    children: <Widget>[
                      GestureDetector(
                        //onTap: () => ComponentUtils.mapviewer(context),
                        child: aqHomeIcon(iconName: 'aq_reserve_add', label: 'Make a reservation'),
                      ),
                      GestureDetector(
                        onTap: () => Get.to(() => AqManageReserve()),
                        child: aqHomeIcon(iconName: 'aq_reserve', label: 'Manage reservations'),
                      ),
                      GestureDetector(
                        // onTap: () => ComponentUtils.mapviewer(context),
                        child: aqHomeIcon(iconName: 'lease', label: 'Check In'),
                      ),
                      GestureDetector(
                        //  onTap: () => ComponentUtils.mapviewer(context),
                        child: aqHomeIcon(iconName: 'locations', label: 'Floor Plans'),
                      ),
                      GestureDetector(
                        // onTap: () => ComponentUtils.mapviewer(context),
                        child: aqHomeIcon(iconName: 'target', label: 'Scan QR Code'),
                      ),
                      GestureDetector(
                        //  onTap: () => ComponentUtils.mapviewer(context),
                        child: aqHomeIcon(iconName: 'find', label: 'My Profile'),
                      ),
                    ]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int iconsizesetup() {
    bool isWeb = GetPlatform.isWeb;
    bool isLargeScreen = Get.width > 600;
    sWidth = Get.width;
    //debugPrint('screenwidth>>>>> $sWidth');
    return isLargeScreen
        ? isWeb
            ? (sWidth < 900.0
                ? 5
                : sWidth < 1000
                    ? 6
                    : sWidth < 1100
                        ? 7
                        : sWidth < 1200
                            ? 8
                            : sWidth < 1300
                                ? 9
                                : 10)
            : 5
        : (sWidth > 350 ? 3 : 2);
  }
}

class aqHomeIcon extends StatelessWidget {
  final iconName;
  final label;
  String? count;

  aqHomeIcon({
    this.iconName,
    this.label,
    this.count,
  });

  @override
  Widget build(BuildContext context) {
    bool isLargeScreen = CommonUtils.isTablet(context);

    return isLargeScreen
        ? Container(
            //margin: EdgeInsets.fromLTRB(1, 1, 1, 1),
            //color: Colors.black38,
            height: double.infinity,
            width: double.infinity,

            child: Stack(
              alignment: AlignmentDirectional.bottomCenter,
              children: [
                Positioned.fill(
                    top: isLargeScreen ? -40 : -50,
                    //bottom: -10,
                    child: FittedBox(
                      fit: BoxFit.fitWidth,
                      child: Image(
                        image: AssetImage('lib/icons/$iconName.png'),
                        width: 250,
                        height: 250,

                        // fit: BoxFit.cover,
                      ),
                    )),
                Row(
                  //crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Container(
                        //color: Colors.blueAccent,
                        padding: EdgeInsets.fromLTRB(0, 0, 0, 30),
                        //margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                        child: Text(
                          label!.value ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: isLargeScreen ? 17 : 12, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            //),
          )
        : Container(
            //margin: EdgeInsets.fromLTRB(1, 1, 1, 1),
            // color: Colors.amberAccent,
            height: double.infinity,
            width: double.infinity,
            //color: Colors.black12,
            child: Stack(
              alignment: AlignmentDirectional.bottomCenter,
              children: [
                Positioned.fill(
                  top: -20,
                  //bottom: -10,
                  child: FittedBox(
                    fit: BoxFit.fitWidth,
                    child: Image(
                      image: AssetImage('lib/icons/$iconName.png'),
                      width: 130,
                      height: 130,

                      // fit: BoxFit.cover,
                    ),
                  ),
                ),
                Row(
                  //crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Container(
                        //color: Colors.blueAccent,
                        padding: EdgeInsets.fromLTRB(0, 0, 0, 8),
                        //margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                        child: Text(
                          label ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: isLargeScreen ? 22 : 12, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            //),
          );
  }
}
