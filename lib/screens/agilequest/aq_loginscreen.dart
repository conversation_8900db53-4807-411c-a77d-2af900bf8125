import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/aq_widgets/aq_space.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import '../../common/component_utils.dart';
import '../../common/widgets/component_widgets/taforminputtext.dart';
import '../../providers/agilequest/aqlogin_controller.dart';

class AqLoginScreen extends StatelessWidget {
  final String? entitytype;

  AqLoginScreen({Key? key, this.entitytype}) : super(key: key);

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final AqLoginController aqloginState = Get.put(AqLoginController());
  final AqLoginController aqLoginCtrlr = Get.find<AqLoginController>();
  final LabelController talabel = Get.find<LabelController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Sign in', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<AqLoginController>(
        initState: (state) async {
          Future.delayed(const Duration(seconds: 0), () async {
            aqLoginCtrlr.aqsigninerror.value = '';
            await aqLoginCtrlr.aqApploginSetup();
          });
        },
        builder: (_) {
          return _.isFormLoading.isTrue
              ? const ProgressIndicatorCust()
              : Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: aqLoginForm(),
                );
        },
      ),
    );
  }

  Widget aqLoginForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 100),
      child: Obx(
        () => Column(
          children: <Widget>[
            TaFormInputText(
              label: 'User Name',
              //readOnly: true,
              value: aqLoginCtrlr.username.value,
              onChanged: (val) {
                aqLoginCtrlr.username.value = val;
              },
              onSaved: (val) {
                aqLoginCtrlr.username.value = val;
              },
            ),
            const AqSpace(),
            TaFormInputText(
              label: 'Password',
              //readOnly: true,
              value: aqLoginCtrlr.password.value,
              obscureText: true,
              onChanged: (val) {
                aqLoginCtrlr.password.value = val;
              },
              onSaved: (val) {
                aqLoginCtrlr.password.value = val;
              },
            ),
            const AqSpace(),
            TaButton(
              type: 'elevate',
              buttonText: 'Continue...',
              onPressed: () async {
                //Get.to(AqHome());
                await aqLoginCtrlr.aqUserLoginSetup();
              },
            ),
            const AqSpace(),
            Obx(
              () => Text(aqLoginCtrlr.aqsigninerror.value,
                  style: const TextStyle(color: Colors.red, fontSize: 15, fontWeight: FontWeight.bold)),
            ),
          ],
        ),
      ),
    );
  }
}
