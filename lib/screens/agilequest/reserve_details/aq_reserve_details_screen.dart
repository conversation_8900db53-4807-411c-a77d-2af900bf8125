import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/aq_widgets/aq_space.dart';
import 'package:tangoworkplace/providers/agilequest/photo/aq_photo_viewer_controller.dart';
import 'package:tangoworkplace/providers/agilequest/rating/aq_review_controller.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/aq_reservation_actions_controller.dart';
import 'package:tangoworkplace/screens/agilequest/participants/aq_reservation_participants_screen.dart';
import 'package:tangoworkplace/screens/agilequest/photo/aq_photo_viewer_screen.dart';
import 'package:tangoworkplace/screens/agilequest/reserve_details/aq_reservation_actions_screen.dart';
import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/component_widgets/tasilverappbardelegate.dart';
import '../../../common/widgets/components.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/aq_reserve_const.dart';
import '../../../models/agilequest/types/reserve/aq_reserve_details_view_mode.dart';
import '../../../providers/agilequest/reserve/aq_reserve_details_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';
import '../review/aq_review_screen.dart';
import 'aq_reservation_info_screen.dart';

class AqReserveDetails extends GetView<AqReserveDetailsController> {
  final AqReserveViewData reservationView;
  final AqReserveDetailsViewMode mode;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final LabelController talabel = Get.find<LabelController>();

  final VoidCallback updateSearchResults;

  AqReserveDetails(
      {Key? key, required this.reservationView, this.mode = AqReserveDetailsViewMode.STANDARD, required this.updateSearchResults})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    controller.initialSetup(reservationView, mode);

    Get.put(AqReviewController());
    Get.put(AqReservationActionsController());

    return Scaffold(
      body: DefaultTabController(
        length: 6,
        child: Obx(
          () => controller.isLoading.value
              ? const Center(
                  child: Center(child: ProgressIndicatorCust()),
                )
              : NestedScrollView(
                  headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                    return <Widget>[
                      SliverAppBar(
                        elevation: 0,
                        title: Text(reservationView.resourceName ?? 'Details'),
                        backgroundColor: Colors.white,
                        titleTextStyle: ComponentUtils.appbartitlestyle,
                        leading: IconButton(
                          icon: ComponentUtils.backpageIcon,
                          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                          onPressed: () {
                            Get.back();
                          },
                        ),
                        stretch: true,
                        expandedHeight: 300.0,
                        floating: false,
                        pinned: true,
                        flexibleSpace: FlexibleSpaceBar(
                          centerTitle: true,
                          background: controller.imagesData.isEmpty
                              ? Container(
                                  margin: const EdgeInsets.only(
                                    top: 55,
                                  ),
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage("lib/icons/aq_tango_rsrv.jpg"),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  alignment: Alignment.center,
                                  height: 300,
                                )
                              : Container(
                                  margin: const EdgeInsets.only(
                                    top: 55,
                                  ),
                                  alignment: Alignment.center,
                                  height: 300,
                                  width: double.infinity,
                                  child: GestureDetector(
                                    onTap: () => _showPhotoDialog(context),
                                    child: ClipRRect(
                                      // borderRadius: BorderRadius.circular(12.0),
                                      child: Image.network(
                                        reservationView.resourceImage.toString(),
                                        fit: BoxFit.fill,
                                        errorBuilder: (context, error, stackTrace) => const Image(
                                          image: AssetImage('lib/icons/aq_tango_rsrv.jpg'),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                        ),
                      ),
                      SliverPersistentHeader(
                        delegate: SliverAppBarDelegate(
                          //color: Colors.white,
                          tabBar: TabBar(
                            isScrollable: true,
                            labelColor: ComponentUtils.tablabelcolor,
                            unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                            indicatorColor: ComponentUtils.tabindicatorColor,
                            indicatorSize: TabBarIndicatorSize.tab,
                            labelStyle: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontStyle: FontStyle.normal,
                                fontSize: 14,
                                color: ComponentUtils.tablabelcolor),
                            tabs: const [
                              Tab(text: 'General'),
                              Tab(text: 'Invitees'),
                              Tab(text: 'Asset Details'),
                              Tab(text: 'Reviews'),
                              Tab(text: 'Location(s)'),
                              Tab(text: 'Actions'),
                              Tab(text: 'Activity'),
                            ],
                          ),
                        ),
                        pinned: true,
                      ),
                    ];
                  },
                  body: Material(
                    elevation: 5,
                    color: ComponentUtils.bodybackground,
                    child: Form(
                      key: _formKey,
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      child: Column(children: [
                        Expanded(
                          child: TabBarView(
                            children: [
                              AqReservationInfoScreen(
                                reservationView: reservationView,
                                mode: mode,
                              ),
                              AqReservationParticipantsScreen(),
                              Container(),
                              AqReviewScreen(
                                resourceId: reservationView.sysidResource ?? 0,
                              ),
                              Container(),
                              AqReservationActionsScreen(
                                reservation: reservationView,
                                actionPerformed: updateSearchResults,
                              ),
                              Container(),
                            ],
                          ),
                        ),
                        const AqSpace(),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              if (reservationView.sysidPurposeType == AqReservationPurposeType.draft.id)
                                TaButton(
                                  type: 'elevate',
                                  buttonText: talabel.getAq(key: '', defaultTxt: 'Save Darft Reservation')!.tag,
                                  readOnly: false,
                                  onPressed: () {
                                    Get.back();
                                  },
                                ),
                              if (reservationView.sysidPurposeType == AqReservationPurposeType.draft.id) const AqSpace(),
                              TaButton(
                                type: 'elevate',
                                buttonText: talabel.getAq(key: '', defaultTxt: 'Submit')!.tag,
                                readOnly: !controller.isSubmitEnabled.value,
                                onPressed: () {
                                  Get.back();
                                },
                              ),
                            ],
                          ),
                        ),
                      ]),
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Future<void> _showPhotoDialog(BuildContext context) {
    Get.put(AqPhotoViewerController());

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: AqPhotoViewerScreen(imagesToDisaply: controller.imagesData.toList()),
        );
      },
    );
  }
}
