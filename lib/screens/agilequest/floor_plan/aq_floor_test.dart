import 'dart:ui' as shape;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:path_drawing/path_drawing.dart';
import 'package:svg_path_parser/svg_path_parser.dart';

import '../../../providers/agilequest/floorplan/aq_floor_plan_controller.dart';
import 'package:touchable/touchable.dart';

class Aqtest extends StatelessWidget {
  final XmlTag? xmltag;

  Aqtest({
    Key? key,
    this.xmltag,
  }) : super(key: key);
  var points = <Offset>[];
  Path path = Path();
  var paint = Paint();

  double getcoord(double x) {
    String result = x.toStringAsFixed(2);
    x = double.parse(result);
    return x;
  }

  void fetchData() {
    try {
      var pointsData = xmltag?.dpoints ?? '';
      // paint = Paint()
      //   ..style = PaintingStyle.stroke
      //   ..strokeWidth = 0.5;
      paint = svgStyleToPaint(xmltag?.style ?? '');
      if (xmltag?.layer == 'basePlanLayer_1') {
        // debugPrint('--------------basePlanLayer_1--------------');
        if (xmltag?.tag == 'polyline') {
          points = getPointsFromPolypoints(pointsData);
        } else if (xmltag?.tag == 'path') {
          path = parseSvgPathData(xmltag?.dpoints ?? '');
        }
      } else if (xmltag?.layer == 'spacePolygonLayer_1') {
        debugPrint('--------------spacePolygonLayer_1--------------');
        path = getPathFromPolypoints(xmltag?.dpoints ?? '');
      }
    } catch (e) {
      debugPrint('error in fetchdate>>>>> $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    fetchData();
    return Center(
      child: CanvasTouchDetector(
        gesturesToOverride: const [
          GestureType.onTapDown,
        ],
        builder: (context) => CustomPaint(
          painter: DataPainter(
            context: context,
            points: points,
            type: xmltag?.tag,
            paintdata: paint,
            path: path,
            layer: xmltag?.layer,
          ),
          child: Container(),
        ),
      ),
    );
  }

  Path getPathFromPolypoints(String pointsData) {
    Path path = Path();
    var coords, x, y;
    int i = 0;
    for (var pair in pointsData.split(' ')) {
      i++;
      coords = pair.split(',');
      if (coords[0] != null && coords[0] != '') {
        x = getcoord(double.parse(coords[0]));
        y = getcoord(double.parse(coords[1]));

        if (i == 1) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      // if (i > 0) {
      //   path.close();
      // }
    }
    return path;
  }

  List<Offset> getPointsFromPolypoints(String pointsData) {
    var points = <Offset>[];
    var coords, x, y;
    int i = 0;
    for (var pair in pointsData.split(' ')) {
      i++;
      coords = pair.split(',');
      if (coords[0] != null && coords[0] != '') {
        x = getcoord(double.parse(coords[0]));
        y = getcoord(double.parse(coords[1]));
        //points.add(Offset(getcoord(double.parse(coords[0])), getcoord(double.parse(coords[1]))));
        points.add(Offset(x, y));
      }
    }
    return points;
  }

  Paint svgStyleToPaint(String svgStyleString) {
    Map<String, String> svgStyle = {};
    svgStyle = svgStyleStringToMap(svgStyleString);
    Paint paint = Paint();

    // Color
    if (svgStyle.containsKey('fill')) {
      paint.style = PaintingStyle.fill;
      paint.color = const Color(0xffc215d5).withOpacity(0.5);
    }

    // Stroke style
    if (svgStyle.containsKey('stroke')) {
      if (svgStyle.containsKey('fill')) {
        paint.style = PaintingStyle.fill;
      } else if (svgStyle.length == 1) {
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 0.5;
      } else {
        paint.style = PaintingStyle.stroke;
      }
    }
    //if (paint.color != Colors.black) paint.style = PaintingStyle.fill;
    // debugPrint('paint>>>>>$paint');

    return paint;
  }

  Map<String, String> svgStyleStringToMap(String svgStyleString) {
    Map<String, String> svgStyle = {};

    svgStyleString.split(';').forEach((styleDeclaration) {
      styleDeclaration = styleDeclaration.trim();

      if (styleDeclaration.contains(':')) {
        final indexOfColon = styleDeclaration.indexOf(':');
        final propertyName = styleDeclaration.substring(0, indexOfColon).trim();
        final propertyValue = styleDeclaration.substring(indexOfColon + 1).trim();

        svgStyle[propertyName] = propertyValue;
      }
    });

    return svgStyle;
  }
}

class DataPainter extends CustomPainter {
  final BuildContext context;
  List<Offset>? points;
  String? type;
  Paint? paintdata;
  Path? path;
  String? layer;
  // Widget? child;

  DataPainter({
    required this.context,
    this.points,
    this.type,
    this.paintdata,
    this.path,
    this.layer,
    //this.child,
  });

  @override
  void paint(Canvas canvas, Size size) {
    var myCanvas = TouchyCanvas(context, canvas);
    if (layer == 'basePlanLayer_1') {
      if (type == 'polyline') {
        canvas.drawPoints(shape.PointMode.polygon, points ?? <Offset>[], paintdata!);
      } else if (type == 'path') {
        canvas.drawPath(path ?? Path(), paintdata ?? Paint());
      }
    } else if (layer == 'spacePolygonLayer_1') {
      //debugPrint('--------------spacePolygonLayer_1------ui--------');
      myCanvas.drawPath(path ?? Path(), paintdata ?? Paint(), onTapDown: (details) {
        debugPrint('>>>>>>>>>${details.localPosition}');
      }, onPanUpdate: (detail) {
        print('Black line Swiped'); //do cooler things here. Probably change app state or animate
      });
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;

  // @override
  // bool hitTest(Offset position) {
  //   return true;
  // }
}
