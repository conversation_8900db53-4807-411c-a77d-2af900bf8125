import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/location/aq_location_data.dart';
import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/agilequest/location/aq_location_search_controller.dart';
import '../../../utils/common_utils.dart';

class AqLocationSearchScreen extends GetView<AqLocationSearchController> {
  final Function onSearchResult;

  final AqLocationData? initialLocationSelection;

  const AqLocationSearchScreen({Key? key, required this.onSearchResult, this.initialLocationSelection}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    controller.uploadLocations();

    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: const Text(""),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Obx(
        () {
          if (controller.isLoading.value) {
            return const ProgressIndicatorCust();
          } else {
            return Column(
              children: [
                TaSearchInputText(
                    makeSearch: (searchText) {
                      controller.filterLocations(searchText);
                    },
                    searchController: controller.searchController,
                    hintSearch: 'Search '),
                Expanded(
                  child: ListView.separated(
                      itemCount: controller.locationsToDisplay.length,
                      separatorBuilder: (context, index) {
                        return const Divider();
                      },
                      itemBuilder: (context, index) {
                        return ListTile(
                          onTap: () {
                            Get.back();
                            onSearchResult(
                              controller.locationsToDisplay[index],
                            );
                          },
                          title: Text(
                            controller.locationsToDisplay[index].name,
                            style: TextStyle(
                                color: controller.locationsToDisplay[index].sysidLocatorNode == initialLocationSelection?.sysidLocatorNode
                                    ? const Color(0XFFb10c00)
                                    : Colors.black,
                                fontSize: 14),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }),
                ),
              ],
            );
          }
        },
      ),
    );
  }
}
