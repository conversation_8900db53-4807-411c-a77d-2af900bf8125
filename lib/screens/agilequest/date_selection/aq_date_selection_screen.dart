import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtime.dart';
import 'package:tangoworkplace/models/agilequest/objects/dates/aq_dates_selection_criteria.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_utils.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_end_reccurrence_type.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_monthly_day_type.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_montly_day_occurrence_type.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/aq_widgets/aq_devider.dart';
import '../../../common/widgets/component_widgets/tacheckbox.dart';
import '../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../common/widgets/component_widgets/taforminputdate.dart';
import '../../../common/widgets/component_widgets/tasilverappbardelegate.dart';
import '../../../common/widgets/components.dart';
import '../../../models/agilequest/types/dates/aq_weekday.dart';
import '../../../providers/agilequest/date/aq_date_selection_controller.dart';
import '../../../providers/agilequest/date/utils/aq_month_occurrence_type.dart';
import '../../../providers/agilequest/date/utils/aq_reccurrence_selection_type.dart';
import '../../../providers/ta_admin/label_controller.dart';

class AqDateSelectionScreen extends GetView<AqDateSelectionController> {
  final AqDatesSelectionCriteria initialDatesCriteria;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final Function onDateSelected;

  final LabelController talabel = Get.find<LabelController>();

  AqDateSelectionScreen({Key? key, required this.initialDatesCriteria, required this.onDateSelected}) : super(key: key);

  void onTapTabBar(int index) {
    if (initialDatesCriteria.isAvailableOnlyForSelectedMode) {
      if (initialDatesCriteria.dateAndTime != null) {
        controller.tabController.index = 0;
      } else {
        controller.tabController.index = 1;
      }
    } else {
      controller.tabController.index = index;
    }
  }

  @override
  Widget build(BuildContext context) {
    controller.setupInitialValues(context, initialDatesCriteria);

    return NestedScrollView(
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          SliverAppBar(
            elevation: 0,
            title: const Text('Date selection'),
            backgroundColor: Colors.white,
            titleTextStyle: ComponentUtils.appbartitlestyle,
            stretch: true,
            expandedHeight: 30.0,
            floating: false,
            pinned: true,
          ),
          SliverPersistentHeader(
            delegate: SliverAppBarDelegate(
              //color: Colors.white,
              tabBar: TabBar(
                isScrollable: false,
                controller: controller.tabController,
                labelColor: ComponentUtils.tablabelcolor,
                unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                indicatorColor: ComponentUtils.tabindicatorColor,
                indicatorSize: TabBarIndicatorSize.tab,
                onTap: onTapTabBar,
                labelStyle:
                    TextStyle(fontWeight: FontWeight.bold, fontStyle: FontStyle.normal, fontSize: 14, color: ComponentUtils.tablabelcolor),
                tabs: tabsWidgets(initialDatesCriteria),
              ),
            ),
            pinned: true,
          ),
        ];
      },
      body: Material(
        elevation: 5,
        color: ComponentUtils.bodybackground,
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          padding: const EdgeInsets.only(top: 20, left: 15, right: 15),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.disabled,
            child: TabBarView(
              controller: controller.tabController,
              physics: initialDatesCriteria.isAvailableOnlyForSelectedMode ? const NeverScrollableScrollPhysics() : null,
              children: [
                _standardDateWidget(context),
                _recurringDateWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Tab> tabsWidgets(AqDatesSelectionCriteria initialCriteria) {
    if (initialCriteria.isAvailableOnlyForSelectedMode) {
      final colorForStandard = initialCriteria.dateAndTime != null ? Colors.grey[850] : Colors.grey;
      final colorForRecurring = initialCriteria.recurrenceRule != null ? Colors.grey[850] : Colors.grey;
      return [
        Tab(
            child: Text(
          talabel.getAq(key: 'aq.mobile.dateSelection.single.multiDay.tab.title', defaultTxt: 'Standard')!.tag,
          style: TextStyle(color: colorForStandard),
        )),
        Tab(
            child: Text(talabel.getAq(key: 'aq.mobile.dateSelection.recurring.tab.title', defaultTxt: 'Recurring')!.tag,
                style: TextStyle(color: colorForRecurring))),
      ];
    } else {
      return [
        Tab(child: Text(talabel.getAq(key: 'aq.mobile.dateSelection.single.multiDay.tab.title', defaultTxt: 'Standard')!.tag)),
        Tab(child: Text(talabel.getAq(key: 'aq.mobile.dateSelection.recurring.tab.title', defaultTxt: 'Recurring')!.tag)),
      ];
    }
  }

  Widget _standardDateWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Obx(
        () => Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
          TaFormInputDate(
            label: talabel.getAq(key: 'aq.mobile.common.title.startDate', defaultTxt: 'Start Date')!.tag,
            controller: controller.startDateStandardController,
            dateSelect: controller.startDateStandardSelected.value.toDate(),
            firstDate: initialDatesCriteria.borderDates?.startDateTime?.toDate() ?? DateTime.now(),
            lastDate: initialDatesCriteria.borderDates?.endDateTime?.toDate() ?? DateTime.now().add(const Duration(days: 3650)),
            onChanged: (start) {
              controller.updateStartDateStandard(start);
            },
            readOnly: false,
            onSaved: (val) {
              controller.updateStartDateStandard(val);
            },
          ),
          TaFormInputTime(
            label: talabel.getAq(key: 'aq.mobile.common.title.startTime', defaultTxt: 'Start Time')!.tag,
            controller: controller.startTimeStandardController,
            readOnly: false,
            onChanged: (val) {
              controller.updateStartTimeStandard(val);
            },
            onSaved: (val) {
              controller.updateStartTimeStandard(val);
            },
            validate: (value) {
              if (value == null || value == '') {
                return 'This attribute is Required';
              }
              return null;
            },
          ),
          const AqDevider(
            customSpace: 24.0,
          ),
          if (controller.noEndDateCheckBox.isFalse)
            TaFormInputDate(
              label: talabel.getAq(key: 'aq.mobile.common.title.endDate', defaultTxt: 'End Date')!.tag,
              controller: controller.endDateStandardController,
              dateSelect: controller.endDateStandardSelected.value.toDate(),
              firstDate: initialDatesCriteria.borderDates?.startDateTime?.toDate(),
              lastDate: initialDatesCriteria.borderDates?.endDateTime?.toDate() ?? DateTime.now().add(const Duration(days: 3650)),
              onChanged: (end) {
                controller.updateEndDateStandard(end);
              },
              readOnly: false,
              onSaved: (val) {
                controller.updateEndDateStandard(val);
              },
            ),
          if (controller.noEndDateCheckBox.isFalse)
            TaFormInputTime(
              label: talabel.getAq(key: 'aq.mobile.common.title.endTime', defaultTxt: 'End Time')!.tag,
              controller: controller.endTimeStandardController,
              readOnly: false,
              onChanged: (val) {
                controller.updateEndTimeStandard(val);
              },
              onSaved: (val) {
                controller.updateEndTimeStandard(val);
              },
              validate: (value) {
                if (value == null || value == '') {
                  return 'This attribute is Required';
                }
                return null;
              },
            ),
          CheckboxListTileFormField(
            title: Text(talabel.getAq(key: 'aq.web.calendar.datetime.label.noEndDate', defaultTxt: 'No End Date')!.tag),
            initialValue: controller.noEndDateCheckBox.value,
            enabled: !initialDatesCriteria.isAvailableOnlyForSelectedMode,
            onChanged: (value) {
              controller.updateNoEndDateStandard(value ?? false);
            },
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.getAq(key: 'aq.web.dialogs.cmd.cancel', defaultTxt: 'Cancel')!.tag,
                  readOnly: false,
                  onPressed: () => Get.back(),
                ),
                const SizedBox(
                  width: 16,
                ),
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.getAq(key: '', defaultTxt: 'Submit')!.tag,
                  readOnly: false,
                  onPressed: () {
                    final datesToSubmit = controller.submitStandardButtonPressed();
                    onDateSelected(datesToSubmit);
                    Get.back();
                  },
                ),
              ],
            ),
          ),
        ]),
      ),
    );
  }

  Widget _recurringDateWidget() {
    return SingleChildScrollView(
      child: Obx(
        () => Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
          TaFormDropdown(
              label: talabel.getAq(key: 'aq.mobile.dateSelection.recurring.tab.title', defaultTxt: 'Recurring')!.tag,
              readonly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
              emptttext: controller.recurrencePatternTypeSelected.value.displayName,
              onChanged: (selectedTypeId) {
                if (selectedTypeId != null) {
                  controller.recurrencePatternTypeChanged(selectedTypeId);
                }
              },
              onSaved: (selectedTypeId) {
                if (selectedTypeId != null) {
                  controller.recurrencePatternTypeChanged(selectedTypeId);
                }
              },
              listflag: true,
              items: AQDateUtils.allRecurrenceTypes.map((AqRecurrenceSelectionType type) {
                return DropdownMenuItem(
                  value: type.position,
                  child: Text(
                    type.displayName,
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                  ),
                );
              }).toList()),
          TaInputNumber(
            title: controller.intervalPatternTypeLabel.value,
            minRange: AQDateUtils.startRangeReccurence,
            maxRange: AQDateUtils.endRangeReccurence,
            readOnly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
            value: controller.intervalPatternSelected.value.toString(),
            onChanged: (value) => controller.intervalChanged(value),
          ),
          if (controller.recurrencePatternTypeSelected.value == AqRecurrenceSelectionType.WEEKLY) weeklyPatternSelectionWidget(),
          if (controller.recurrencePatternTypeSelected.value == AqRecurrenceSelectionType.MONTHLY) montlyPatternSelectionWidget(),
          const AqDevider(
            customSpace: 24.0,
          ),
          TaFormInputDate(
            label: talabel.getAq(key: 'aq.mobile.recurring.startsOn.title', defaultTxt: 'Starts on')!.tag,
            controller: controller.startDateRecurringController,
            readOnly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
            dateSelect: controller.startDateRecurringSelected.value.toDate(),
            onChanged: (start) {
              controller.updateStartDateRecurring(start);
            },
            onSaved: (val) {
              controller.updateStartDateRecurring(val);
            },
          ),
          TaFormInputTime(
            label: talabel.getAq(key: 'aq.mobile.common.title.startTime', defaultTxt: 'Start Time')!.tag,
            controller: controller.startTimeRecurringController,
            readOnly: false,
            onChanged: (val) {
              controller.updateStartTimeRecurring(val);
            },
            onSaved: (val) {
              controller.updateStartTimeRecurring(val);
            },
            validate: (value) {
              if (value == null || value == '') {
                return 'This attribute is Required';
              }
              return null;
            },
          ),
          TaFormInputTime(
            label: talabel.getAq(key: 'aq.mobile.common.title.endTime', defaultTxt: 'End Time')!.tag,
            controller: controller.endTimeRecurringController,
            readOnly: false,
            onChanged: (val) {
              controller.updateEndTimeRecurring(val);
            },
            onSaved: (val) {
              controller.updateEndTimeRecurring(val);
            },
            validate: (value) {
              if (value == null || value == '') {
                return 'This attribute is Required';
              }
              return null;
            },
          ),
          const AqDevider(
            customSpace: 24.0,
          ),
          TaFormDropdown(
              label: talabel.getAq(key: 'aq.mobile.end.pattern.title', defaultTxt: 'End')!.tag,
              emptttext: controller.recurrenceEndPatternTypeSelected.value.displayNameForWidget,
              onChanged: (selectedEndTypeId) {
                if (selectedEndTypeId != null) {
                  controller.recurrencePatternEndTypeChanged(selectedEndTypeId);
                }
              },
              onSaved: (selectedEndTypeId) {
                if (selectedEndTypeId != null) {
                  controller.recurrencePatternEndTypeChanged(selectedEndTypeId);
                }
              },
              listflag: true,
              readonly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
              items: AQDateUtils.allRecurrenceEndTypes.map((AqEndReccurrenceType type) {
                return DropdownMenuItem(
                  value: type.index,
                  child: Text(
                    type.displayNameForWidget,
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                  ),
                );
              }).toList()),
          if (controller.recurrenceEndPatternTypeSelected.value == AqEndReccurrenceType.AFTER)
            TaInputNumber(
              title: talabel.getAq(key: '', defaultTxt: 'Occurrences')!.tag,
              value: controller.occurencesPatternSelected.value.toString(),
              readOnly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
              onChanged: controller.occurencesChanged,
              minRange: AQDateUtils.startRangeReccurence,
              maxRange: AQDateUtils.endRangeRecurrenceAfterType,
            ),
          if (controller.recurrenceEndPatternTypeSelected.value == AqEndReccurrenceType.BY)
            TaFormInputTime(
              label: '',
              controller: controller.endByDateRecurringController,
              readOnly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
              onChanged: (val) {
                controller.updateEndByDate(val);
              },
              onSaved: (val) {
                controller.updateEndByDate(val);
              },
              validate: (value) {
                if (value == null || value == '') {
                  return 'This attribute is Required';
                }
                return null;
              },
            ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.getAq(key: 'aq.web.dialogs.cmd.cancel', defaultTxt: 'Cancel')!.tag,
                  readOnly: false,
                  onPressed: () => Get.back(),
                ),
                const SizedBox(
                  width: 16,
                ),
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.getAq(key: '', defaultTxt: 'Submit')!.tag,
                  readOnly: !controller.isSubmitRecurringEnabled.value,
                  onPressed: () {
                    final datesToSubmit = controller.submitRecurringButtonPressed(initialDatesCriteria);
                    if (datesToSubmit != null) {
                      onDateSelected(datesToSubmit);
                      Get.back();
                    }
                  },
                ),
              ],
            ),
          ),
        ]),
      ),
    );
  }

  Widget montlyPatternSelectionWidget() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TaFormDropdown(
            label: talabel.getAq(key: 'aq.mobile.common.on.title', defaultTxt: 'On')!.tag,
            emptttext: talabel
                .getAq(
                    key: controller.monthlyPatternTypeSelected.value.tagForWidget,
                    defaultTxt: controller.monthlyPatternTypeSelected.value.displayNameForWidget)!
                .tag,
            onChanged: (selectedTypeId) {
              if (selectedTypeId != null) {
                controller.monthlyOccurenceTypeChanged(selectedTypeId);
              }
            },
            onSaved: (selectedTypeId) {
              if (selectedTypeId != null) {
                controller.monthlyOccurenceTypeChanged(selectedTypeId);
              }
            },
            listflag: true,
            readonly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
            items: AQDateUtils.allMonthDayOccurrencePatternTypes.map((AqMonthOccurrenceType type) {
              return DropdownMenuItem(
                value: type.index,
                child: Text(
                  talabel.getAq(key: type.tagForWidget, defaultTxt: type.displayNameForWidget)!.tag,
                  style: const TextStyle(fontSize: 14, color: Colors.black),
                ),
              );
            }).toList()),
        if (controller.monthlyPatternTypeSelected.value == AqMonthOccurrenceType.DAY)
          TaInputNumber(
            title: talabel.getAq(key: 'aq.mobile.month.pattern.type.day.title', defaultTxt: 'Day')!.tag,
            minRange: AQDateUtils.startRangeReccurence,
            maxRange: AQDateUtils.endRangeReccurence,
            readOnly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
            value: controller.monthlyDayPatternSelected.value.toString(),
            onChanged: (value) => controller.montlyDayPatternChanged(int.parse(value)),
          ),
        if (controller.monthlyPatternTypeSelected.value == AqMonthOccurrenceType.THE)
          TaFormDropdown(
              label: '',
              emptttext: talabel
                  .getAq(
                      key: controller.monthlyDayOccurencePatternTypeSelected.value.tagForWidget,
                      defaultTxt: controller.monthlyDayOccurencePatternTypeSelected.value.displayNameForWidget)!
                  .tag,
              onChanged: (selectedTypeId) {
                if (selectedTypeId != null) {
                  controller.monthlyDayOccurenceTypeChanged(selectedTypeId);
                }
              },
              onSaved: (selectedTypeId) {
                if (selectedTypeId != null) {
                  controller.monthlyDayOccurenceTypeChanged(selectedTypeId);
                }
              },
              listflag: true,
              readonly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
              items: AQDateUtils.allMonthDayOccurrenceTypes.map((MonthlyDayOccurrenceType type) {
                return DropdownMenuItem(
                  value: type.id,
                  child: Text(
                    talabel.getAq(key: type.tagForWidget, defaultTxt: type.displayNameForWidget)!.tag,
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                  ),
                );
              }).toList()),
        if (controller.monthlyPatternTypeSelected.value == AqMonthOccurrenceType.THE)
          TaFormDropdown(
              label: '',
              emptttext: talabel
                  .getAq(
                      key: controller.monthlyDayTypeSelected.value.tagForWidget,
                      defaultTxt: controller.monthlyDayTypeSelected.value.displayNameForWidget)!
                  .tag,
              onChanged: (selectedTypeId) {
                if (selectedTypeId != null) {
                  controller.monthlyDayTypeChanged(selectedTypeId);
                }
              },
              onSaved: (selectedTypeId) {
                if (selectedTypeId != null) {
                  controller.monthlyDayTypeChanged(selectedTypeId);
                }
              },
              listflag: true,
              readonly: initialDatesCriteria.isAvailableOnlyForSelectedMode,
              items: AQDateUtils.allMonthDayTypes.map((AqMonthlyDayType type) {
                return DropdownMenuItem(
                  value: type.id,
                  child: Text(
                    talabel.getAq(key: type.tagForWidget, defaultTxt: type.displayNameForWidget)!.tag,
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                  ),
                );
              }).toList()),
      ],
    );
  }

  Widget weeklyPatternSelectionWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: SizedBox(
        height: 110,
        child: GridView.count(
          crossAxisCount: 5,
          scrollDirection: Axis.vertical,
          children: List.generate(
            controller.weekdaysToDisplay.toList().length,
            (index) {
              final dayId = controller.weekdaysToDisplay[index].id;
              final isSelected = controller.recurrenceWeekdaysSelected.toList().contains(dayId);
              return weekDayButton(
                  controller.weekdaysToDisplay.toList()[index], isSelected, initialDatesCriteria.isAvailableOnlyForSelectedMode, () {
                controller.weekDaySelected(dayId);
              });
            },
          ),
        ),
      ),
    );
  }

  Widget weekDayButton(AqWeekday day, bool isSelected, bool readOnly, VoidCallback onClick) {
    final isEnabledColor = readOnly ? Colors.grey[800] : const Color(0XFFb10c00);
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: SizedBox(
        width: 40.0,
        height: 40.0,
        child: RawMaterialButton(
          fillColor: isSelected ? isEnabledColor : const Color(0xFFFFFFFF),
          focusColor: readOnly ? Colors.transparent : const Color(0XFFb10c00),
          hoverColor: readOnly ? Colors.transparent : const Color.fromARGB(255, 119, 5, 5),
          splashColor: readOnly ? Colors.transparent : const Color.fromARGB(255, 119, 5, 5),
          onPressed: () => readOnly ? null : onClick(),
          shape: CircleBorder(side: BorderSide(color: isEnabledColor!, width: 0.5)),
          child: Text(
            talabel.getAq(key: day.getWeekDaysShortenTagKey, defaultTxt: day.displayName)!.tag,
            style: TextStyle(
              color: isSelected ? const Color(0xFFFFFFFF) : isEnabledColor,
            ),
          ),
        ),
      ),
    );
  }
}
