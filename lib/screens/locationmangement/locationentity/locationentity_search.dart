import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';

import '../../../common/component_utils.dart';
import '../../../models/location/locationentity/locationentity.dart';
import '../../../providers/contracts/lease/lease_home_controller.dart';
import '../../../providers/locationmanagement/locationentity/locationentitysearch_controller.dart';
import 'locationentity_details.dart';

class LocationEntitySearch extends StatelessWidget {
  final String? entitytype;
  LocationEntitySearch({Key? key, this.entitytype}) : super(key: key);
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  LocationEntitySearchController locentState = Get.put(LocationEntitySearchController());

  LocationEntitySearchController locEntityCtrlr = Get.find<LocationEntitySearchController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshList() async {
    await locEntityCtrlr.getLocEntitySearchData(searchText: locEntityCtrlr.searchController.text, entitytype: entitytype);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_LOCATIONS')?.value ?? 'Locations',
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        // actions: [
        //   IconButton(
        //     color: ComponentUtils.primecolor,
        //     icon: const Icon(Icons.my_location),
        //     onPressed: () async {
        //       Position p = await _determinePosition();
        //       debugPrint('latitude     ${p.latitude}');
        //     },
        //   ),
        // ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          TaSearchInputText(
              makeSearch: (searchtext) {
                locEntityCtrlr.getLocEntitySearchData(searchText: searchtext.toString(), entitytype: entitytype);
              },
              searchController: locEntityCtrlr.searchController,
              hintSearch: 'Search '),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshList,
              child: GetX<LocationEntitySearchController>(
                initState: (state) async {
                  await locEntityCtrlr.getLocEntitySearchData(entitytype: entitytype);
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? ProgressIndicatorCust()
                      : _.locentitylist.length < 1
                          ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.locentitylist[index]);
                              },
                              itemCount: _.locentitylist.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, LocationEntity le) {
    final primary = Color(0xff696b9e);
    final secondary = Color(0xfff29a94);

    return GestureDetector(
        onTap: () {
          Get.to(() => LocationEntityDetails(
                nav_from: 'search',
                entityid: le.storeId,
                entityname: le.storeName,
                locEntity: le,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${le?.storeName ?? ''}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.shield,
                            color: secondary,
                            size: 15,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Text(le.storeNumber?.toString() ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        // Row(
                        //   children: <Widget>[
                        //     Icon(
                        //       Icons.bolt,
                        //       color: secondary,
                        //       size: 15,
                        //     ),
                        //     SizedBox(
                        //       width: 5,
                        //     ),
                        Text(le.status ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        //   ],
                        // ),
                      ],
                    ),
                    // SizedBox(
                    //   height: 6,
                    // ),
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   children: <Widget>[
                    //     Row(children: [
                    //       Icon(
                    //         Icons.assistant_photo,
                    //         color: secondary,
                    //         size: 15,
                    //       ),
                    //       SizedBox(
                    //         width: 5,
                    //       ),
                    //       Text(s.entityType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                    //     ]),
                    //     Text(p.ownershipType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                    //   ],
                    // ),
                    SizedBox(
                      height: 6,
                    ),
                    Row(
                      children: <Widget>[
                        Icon(
                          Icons.location_on,
                          color: secondary,
                          size: 15,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Expanded(
                          child: Text('${le.address ?? ''}' + ' ' + '${le.city ?? ''}' + ' ' + '${le.state ?? ' '}',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: primary,
                                fontSize: 12,
                                letterSpacing: .1,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
