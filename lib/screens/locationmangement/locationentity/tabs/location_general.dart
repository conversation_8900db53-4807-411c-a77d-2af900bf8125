import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/location/locationentity/locationentity.dart';
import '../../../../providers/locationmanagement/locationentity/locationgeneral_controller.dart';

class LocationGeneral extends GetView<LocationGeneralController> {
  final LocationEntity? loc;
  LocationGeneral({Key? key, this.loc}) : super(key: key);

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  LocationGeneralController genCtrl = Get.find<LocationGeneralController>();

  @override
  Widget build(BuildContext context) {
    genCtrl.labelsloading.value = true;

    return Stack(children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await genCtrl.getlabels(talabel);
            });
          },
          builder: (_) {
            return genCtrl.labelsloading.value ? ProgressIndicatorCust() : generalTab(loc);
          },
        ),
      ),
    ]);
  }

  Widget generalTab(LocationEntity? le) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STORENUM') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STORENUM')!.value,
              value: le!.storeNumber.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STORENAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STORENAME')!.value,
              value: le!.storeName.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STATUS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STATUS')!.value,
              value: le!.status?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_LATITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_LATITUDE')!.value,
              value: le!.latitude?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_LONGITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_LONGITUDE')!.value,
              value: le!.longitude.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_ADDRESS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_ADDRESS')!.value,
              value: le!.address?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_CITY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_CITY')!.value,
              value: le!.city?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_STATE')!.value,
              value: le!.state?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_ZIPCODE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_ZIPCODE')!.value,
              value: le!.zipCode?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_DMANAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_DMANAME')!.value,
              value: le!.openDate?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_LOCATIONS_GENERAL_SHOPPINGCENTER') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_LOCATIONS_GENERAL_SHOPPINGCENTER')!.value,
              value: le!.shoppingCenter?.toString(),
              readOnly: true,
            ),
        ],
      ),
    );
  }
}
