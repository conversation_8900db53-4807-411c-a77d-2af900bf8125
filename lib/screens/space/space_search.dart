import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/models/recent_reservations.dart';
import 'package:tangoworkplace/models/space.dart';
import 'package:tangoworkplace/providers/reservation_provider.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

class SpaceSearch extends StatefulWidget {
  static const routName = '/spacesearch';
  @override
  _SpaceSearchState createState() => _SpaceSearchState();
}

class _SpaceSearchState extends State<SpaceSearch> {
  // final formKey = new GlobalKey<FormState>();
  // final key = new GlobalKey<ScaffoldState>();
  final TextEditingController _filter = new TextEditingController();
  String _searchText = "";
  List? filteredNames;
  List<Space>? spaceLov;
  String? startdate, enddate;
  int? buildingId, floorId;
  var requestId;

  _SpaceSearchState() {
    _filter.addListener(() {
      if (_filter.text.isEmpty) {
        setState(() {
          _searchText = "";
          filteredNames = spaceLov;
        });
      } else {
        setState(() {
          _searchText = _filter.text;
        });
      }
    });
  }

  @override
  void initState() {
    _filter.clear();
    super.initState();
  }

  @override
  Future<void> didChangeDependencies() async {
    bool _isInit = true;
    if (_isInit) {
      List? arguments = ModalRoute.of(context)!.settings.arguments as List?;
      if (arguments != null) {
        startdate = arguments[0];
        enddate = arguments[1];
        buildingId = arguments[2];
        floorId = arguments[3];
        if (arguments[4] != null) requestId = arguments[4].toString();
      }
      print('Inside property $startdate $enddate');

      this._getNames(startdate, enddate, buildingId, floorId);
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(
            elevation: 0, centerTitle: true, title: Text("Search Space", style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold))),
        body: Stack(children: <Widget>[
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
                  color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                  shape: BoxShape.rectangle),
              height: 115,
            ),
          ),
          Positioned(
              top: 00,
              right: 15,
              left: 15,
              bottom: 10,
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(9),
                  child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.red,
                      child: Container(
                          color: Colors.white,
                          child: Center(
                              child: Container(
                            child: Column(children: <Widget>[
                              SizedBox(
                                child: new TextField(
                                    style: TextStyle(fontSize: 12),
                                    controller: _filter,
                                    decoration: InputDecoration(
                                      labelText: 'Enter Search Text',
                                      filled: true,
                                      fillColor: Colors.white70,
                                    )),
                              ),
                              Expanded(child: _buildList()),
                            ]),
                          ))))))
        ]));
  }

  Widget _buildBar(BuildContext context) {
    return new AppBar(
        elevation: 0,
        centerTitle: true,
        title: new TextField(
            style: TextStyle(fontSize: 12, color: Colors.white),
            controller: _filter,
            decoration: InputDecoration(
                labelText: 'Search...',
                icon: Icon(Icons.search),
                filled: true,
                fillColor: Colors.white70,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                  borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(10.0)),
                  borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                ))));
  }

  Widget _buildList() {
    print(_searchText);
    if (_searchText.isNotEmpty) {
      print('Inside$filteredNames');
      List tempList = [];
      for (int i = 0; i < filteredNames!.length; i++) {
        print(filteredNames!.elementAt(i));
        Space p = filteredNames!.elementAt(i);
        if (p.spaceDisplayName!.toLowerCase().contains(_searchText.toLowerCase())) {
          tempList.add(filteredNames!.elementAt(i));
        }
      }
      filteredNames = tempList;
    } else {
      filteredNames = spaceLov;
    }
    if (filteredNames != null) {
      return ListView.separated(
        itemCount: filteredNames != null ? filteredNames!.length : 0,
        itemBuilder: (context, index) {
          Space p = filteredNames!.elementAt(index);
          return new ListTile(
            title: Text('${p.spaceDisplayName}',
                style: TextStyle(
                  fontSize: 14.0,
                  fontWeight: FontWeight.bold,
                )),
            //  subtitle:
            //    Text('${p.spaceNumber}', style: TextStyle(fontSize: 14.0)),
            onTap: () {
              print(p.spaceDisplayName);
              Navigator.pop(context, p);
            },
          );
        },
        separatorBuilder: (BuildContext context, int index) => Divider(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
      );
    } else {
      return Center(child: CircularProgressIndicator());
    }
  }

  void _getNames(String? startdate, String? enddate, int? buildingId, int? floorId) async {
    print('start$startdate');
    print('end$enddate');
    spaceLov = await Provider.of<ReservationProvider>(context, listen: false)
        .getSpace(context, buildingId, floorId, startdate, enddate, "", requestId: requestId);

    /* List tempList = new List();
    for (int i = 0; i < response.data['results'].length; i++) {
      tempList.add(response.data['results'][i]);
    }*/
    setState(() {
      filteredNames = spaceLov;
    });
  }
}
