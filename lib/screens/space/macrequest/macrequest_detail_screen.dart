import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import '../../../common/component_utils.dart';
import '../../../models/building.dart';
import '../../../models/floor.dart';
import '../../../models/macrequests.dart';
import '../../../models/property.dart';
import '../../../models/space.dart';
import '../../../providers/macrequests_provider.dart';
import '../../../utils/constvariables.dart';
import '../../../utils/preferences_utils.dart';
import '../../../utils/common_utils.dart';
import 'package:intl/intl.dart';

class MacRequestDetail extends StatefulWidget {
  static const routName = '/macrequestDetail';
  @override
  _MacRequestDetailState createState() => _MacRequestDetailState();
}

class _MacRequestDetailState extends State<MacRequestDetail> {
  var _macObj = MacRequest(
      requestId: null,
      requestNumber: '',
      tospaceDisplayName: '',
      fromspaceDisplayName: '',
      status: '',
      requestedStartDate: '',
      requestedFor: '',
      propertyId: 0,
      floorId: 0,
      spaceId: 0,
      buildingId: 0,
      justification: '',
      approver: '',
      noOfBoxes: '');

  var _initValues = {
    'requestId': null,
    'requestNumber': '',
    'tospaceDisplayName': '',
    'fromspaceDisplayName': '',
    'status': '',
    'requestedStartDate': '',
    'requestedFor': '',
    'propertyId': 0,
    'floorId': 0,
    'spaceId': 0,
    'buildingId': 0,
    'justification': '',
    'approver': '',
    'noOfBoxes': ''
  };
  var _isInit = true;
  var _isLoading = false;
  var _reserv = true;

  final _formKey = GlobalKey<FormState>();
  final myController = TextEditingController();
  final requestorController = TextEditingController();
  int? _propertyname = 0, _buildingname, _floorname, _spacename;
  var _spacedisplayname;

  String? dateTime;
  DateTime selectedStartDate = DateTime.now();
  DateTime selectedEndDate = DateTime.now();
  DateTime selectedDate = DateTime.now();
  TimeOfDay selectedTime = TimeOfDay(hour: 00, minute: 00);

  TextEditingController _dateController = TextEditingController();
  TextEditingController _dateController1 = TextEditingController();

  TextEditingController _startdateController = TextEditingController();
  TextEditingController _enddateController = TextEditingController();

  var prov;
  Future<List<Property>?>? propertyLov;
  List<Building>? buildingLov = [];
  List<Floor>? floorLov = [];
  Future<List<Space>?>? spaceLov;

  final _endDateFocusNode = FocusNode();

  Future<Null> _selectDate(BuildContext context, String source) async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: selectedDate,
        initialEntryMode: DatePickerEntryMode.calendar,
        firstDate: DateTime(2015),
        lastDate: DateTime(2101));
    if (picked != null) {
      setState(() {
        selectedStartDate = picked;
        DateTime dt = DateTime(selectedEndDate.year, selectedEndDate.month, selectedEndDate.day);
        print(dt);
        String d = DateFormat('MM/dd/yyyy').format(dt);

        print(d);

        _dateController.text = d;
      });
      //  _selectTime(context, source);
    }
  }

  Future<Null> _selectTime(BuildContext context, String source) async {
    final TimeOfDay? picked = await showTimePicker(context: context, initialTime: selectedTime, initialEntryMode: TimePickerEntryMode.dial);
    if (picked != null)
      setState(() {
        selectedTime = picked;

        if (source == 'end') {
          DateTime dt = DateTime(selectedEndDate.year, selectedEndDate.month, selectedEndDate.day, selectedTime.hour, selectedTime.minute);
          print(dt);
          String d = DateFormat('MM/dd/yyyy kk:mm:a').format(dt);

          print(d);

          _dateController1.text = d;

          DateTime datedt = DateTime(selectedEndDate.year, selectedEndDate.month, selectedEndDate.day);
          print(datedt);
          String d1 = DateFormat('yyyy-MM-dd').format(datedt);
          print(d1);
          _startdateController.text = d1;
        } else {
          DateTime dt =
              DateTime(selectedStartDate.year, selectedStartDate.month, selectedStartDate.day, selectedTime.hour, selectedTime.minute);
          print(dt);
          String d1 = DateFormat('MM/dd/yyyy kk:mm:a').format(dt);

          print(d1);
          _dateController.text = d1;

          DateTime dated1t = DateTime(selectedStartDate.year, selectedStartDate.month, selectedStartDate.day);
          print(dated1t);
          String d2 = DateFormat('yyyy-MM-dd').format(dated1t);
          print(d2);
          _enddateController.text = d2;
        }
      });
  }

  @override
  void initState() {
    super.initState();
    propertyLov = Provider.of<MacRequestProvider>(context, listen: false).getPropertyLov(context);
  }

  @override
  Future<void> didChangeDependencies() async {
    if (_isInit) {
      final requestId = ModalRoute.of(context)!.settings.arguments as int?;

      print('Inside macrequest detail $requestId');

      if (requestId != null) {
      } else {
        myController.text = await SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
        requestorController.text = await SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
      }
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    myController.dispose();
    _dateController.dispose();
    _dateController1.dispose();
    _endDateFocusNode.dispose();
    super.dispose();
  }

  Future<void> _saveForm() async {
    final isValid = _formKey.currentState!.validate();
    if (!isValid) {
      return;
    }
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text(_macObj.requestId != null ? 'Update MAC Request ' : 'New MAC Request',
              style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
          backgroundColor: Colors.white,
          elevation: 5,
          leading: new IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: Stack(children: <Widget>[
          Positioned(
            top: 00,
            right: 0,
            left: 0,
            bottom: 0,
            child: ClipRRect(
              //borderRadius: BorderRadius.circular(9),
              child: Container(
                color: Colors.white,
                padding: EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 0.0),
                child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                        TextFormField(
                          enabled: false,
                          controller: requestorController,
                          style: TextStyle(fontSize: 12),
                          decoration: InputDecoration(
                              labelText: 'Requestor',
                              icon: Icon(Icons.person),
                              filled: true,
                              fillColor: Colors.white70,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                              )),
                        ),
                        SizedBox(
                          height: 5.0,
                        ),
                        TextFormField(
                          style: TextStyle(fontSize: 12),
                          controller: myController,
                          textInputAction: TextInputAction.next,

                          onSaved: (value) {
                            myController.text = value!;
                          },
                          decoration: InputDecoration(
                              labelText: 'Requested for',
                              icon: Icon(Icons.person),
                              filled: true,
                              fillColor: Colors.white70,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                              )),
                          // The validator receives the text that the user has entered.
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please enter requested for';
                            }
                            return null;
                          },
                        ),
                        SizedBox(
                          height: 5.0,
                        ),
                        TextFormField(
                          style: TextStyle(fontSize: 12),
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please select Requested Date';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                              labelText: 'Requested Date',
                              icon: Icon(Icons.calendar_today),
                              filled: true,
                              fillColor: Colors.white70,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                              )),
                          controller: _dateController,
                          onTap: () {
                            _selectDate(context, 'start');
                          },
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        TextFormField(
                          enabled: false,
                          initialValue: 'Test User',
                          style: TextStyle(fontSize: 12),
                          decoration: InputDecoration(
                              labelText: 'Approver',
                              icon: Icon(Icons.approval),
                              filled: true,
                              fillColor: Colors.white70,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                              )),
                          // The validator receives the text that the user has entered.
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        TextFormField(
                          style: TextStyle(fontSize: 12),
                          decoration: InputDecoration(
                              labelText: 'Description',
                              icon: Icon(Icons.description),
                              filled: true,
                              fillColor: Colors.white70,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                              )),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        TextFormField(
                          initialValue: 'Test Location',
                          enabled: false,
                          style: TextStyle(fontSize: 12),
                          decoration: InputDecoration(
                              labelText: 'Location',
                              icon: Icon(Icons.location_city),
                              filled: true,
                              fillColor: Colors.white70,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                              )),
                          // The validator receives the text that the user has entered.
                        ),
                        SizedBox(
                          height: 5.0,
                        ),
                        Container(
                            child: FutureBuilder<List<Property>?>(
                                future: propertyLov,
                                builder: (context, AsyncSnapshot snapshot) {
                                  if (snapshot.data == null) {
                                    return CircularProgressIndicator();
                                  } else {
                                    return DropdownButtonFormField<int>(
                                      hint: Text(
                                        'Select a Site',
                                        style: TextStyle(fontSize: 12),
                                      ),
                                      onSaved: (val) => _propertyname = val,
                                      value: _macObj.requestId != null ? _initValues['propertyId'] as int? : null,
                                      items: snapshot.data.map<DropdownMenuItem<int>>((Property x) {
                                        return DropdownMenuItem<int>(
                                          value: x.propertyId,
                                          child: Text(
                                            x.propertyName!,
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (val) {
                                        setState(() {
                                          _floorname = null;
                                          _buildingname = null;
                                          _propertyname = null;
                                          _propertyname = val;
                                          buildingLov = null;
                                          buildingLov = Provider.of<MacRequestProvider>(context, listen: false).getBuildingByProperty(val);
                                        });
                                      },
                                      validator: (value) {
                                        if (value == null) {
                                          return 'Please select a property';
                                        }
                                        return null;
                                      },
                                      decoration: InputDecoration(
                                          labelStyle: TextStyle(fontSize: 12),
                                          floatingLabelBehavior: FloatingLabelBehavior.always,
                                          labelText: 'To Site',
                                          icon: Icon(Icons.build),
                                          filled: true,
                                          fillColor: Colors.white70,
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                            borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                            borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                          )),
                                    );
                                  }
                                })),
                        SizedBox(
                          height: 5.0,
                        ),
                        Consumer<MacRequestProvider>(builder: (BuildContext context, resvProvider, Widget? child) {
                          return DropdownButtonFormField(
                            hint: Text(
                              'Select a Building',
                              style: TextStyle(fontSize: 12),
                            ),
                            onSaved: (dynamic val) => _buildingname = val,
                            value: _macObj.requestId != null ? _initValues['buildingId'] : _buildingname,
                            items: buildingLov!.map<DropdownMenuItem<int>>((Building dropDownStringItem) {
                              print('Generating Building $dropDownStringItem');
                              return DropdownMenuItem<int>(
                                value: dropDownStringItem.buildingId,
                                child: Text(dropDownStringItem.buildingName!, style: TextStyle(fontSize: 12)),
                              );
                            }).toList(),
                            onChanged: (dynamic val) {
                              setState(() {
                                _floorname = null;
                                floorLov = null;
                                _buildingname = null;
                                _buildingname = val;

                                floorLov = Provider.of<MacRequestProvider>(context, listen: false).getFloorByBuilding(val);
                              });
                            },
                            validator: (dynamic value) {
                              if (value == null) {
                                return 'Please select a building';
                              }
                              return null;
                            },
                            decoration: InputDecoration(
                                floatingLabelBehavior: FloatingLabelBehavior.always,
                                labelStyle: TextStyle(fontSize: 12),
                                labelText: 'To Building',
                                icon: Icon(Icons.build),
                                filled: true,
                                fillColor: Colors.white70,
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                  borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                  borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                )),
                          );
                        }),
                        SizedBox(
                          height: 5.0,
                        ),
                        Consumer<MacRequestProvider>(builder: (BuildContext context, resvProvider, Widget? child) {
                          return DropdownButtonFormField(
                            hint: Text('Select a Floor', style: TextStyle(fontSize: 12)),
                            onSaved: (dynamic val) => _floorname = val,
                            value: _macObj.requestId != null ? _initValues['floorId'] : _floorname,
                            items: floorLov!.map<DropdownMenuItem<int>>((Floor dropDownStringItem) {
                              print('Generating Floor $dropDownStringItem');
                              return DropdownMenuItem<int>(
                                value: dropDownStringItem.floorId,
                                child: Text(dropDownStringItem.floorName!, style: TextStyle(fontSize: 12)),
                              );
                            }).toList(),
                            onChanged: (dynamic val) {
                              setState(() {
                                print('Floor value $val');
                                _floorname = val;

                                spaceLov = Provider.of<MacRequestProvider>(context, listen: false)
                                    .getSpace(context, _buildingname, _floorname, _startdateController.text, _enddateController.text);
                              });
                            },
                            validator: (dynamic value) {
                              if (value == null) {
                                return 'Please select a floor';
                              }
                              return null;
                            },
                            decoration: InputDecoration(
                                floatingLabelBehavior: FloatingLabelBehavior.always,
                                labelStyle: TextStyle(fontSize: 12),
                                labelText: 'To Floor',
                                icon: Icon(Icons.build_circle),
                                filled: true,
                                fillColor: Colors.white70,
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                  borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                  borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                )),
                          );
                        }),
                        SizedBox(
                          height: 5.0,
                        ),
                        Consumer<MacRequestProvider>(builder: (BuildContext context, resvProvider, Widget? child) {
                          return FutureBuilder<List<Space>?>(
                              future: spaceLov,
                              builder: (context, AsyncSnapshot snapshot) {
                                if (snapshot.data == null) {
                                  return DropdownButtonFormField(
                                      hint: Text('Select a Space', style: TextStyle(fontSize: 12)),
                                      value: 'xxxx',
                                      onChanged: (val) {},
                                      items: [
                                        DropdownMenuItem(
                                          value: 'xxxx',
                                          child: Text('Select a space', style: TextStyle(fontSize: 12)),
                                        )
                                      ],
                                      validator: (dynamic? value) {
                                        print('Validing space val $value');
                                        if (value == 'xxxx') {
                                          return 'Please select a space';
                                        }
                                        return null;
                                      },
                                      decoration: InputDecoration(
                                          floatingLabelBehavior: FloatingLabelBehavior.always,
                                          labelText: 'To Space',
                                          icon: Icon(Icons.shop),
                                          filled: true,
                                          fillColor: Colors.white70,
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                            borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                            borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                          )));
                                } else {
                                  // print(
                                  //     'Inside items $_spacename ${_reservationObj.requestId}');
                                  return DropdownButtonFormField(
                                    hint: Text('To Space', style: TextStyle(fontSize: 12)),
                                    onSaved: (dynamic val) => _spacename = val,
                                    value: _macObj.requestId != null ? _initValues['spaceId'] : _spacename,
                                    items: snapshot.data.map<DropdownMenuItem<int>>((Space x) {
                                      //  print(' ${x.spaceId}');
                                      return DropdownMenuItem<int>(
                                        value: x.spaceId,
                                        child: Text('${x.spaceDisplayName}', style: TextStyle(fontSize: 12)),
                                      );
                                    }).toList(),
                                    onChanged: (dynamic val) {
                                      setState(() {
                                        _spacename = val;
                                      });
                                    },
                                    validator: (dynamic value) {
                                      print('Validing space va; $value');
                                      if (value == null) {
                                        return 'Please select a space';
                                      }
                                      return null;
                                    },
                                    decoration: InputDecoration(
                                        floatingLabelBehavior: FloatingLabelBehavior.always,
                                        labelStyle: TextStyle(fontSize: 12),
                                        labelText: 'To Space',
                                        icon: Icon(Icons.shop),
                                        filled: true,
                                        fillColor: Colors.white70,
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                          borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                          borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                        )),
                                  );
                                }
                              });
                        }),
                        SizedBox(
                          height: 5,
                        ),
                        TextFormField(
                          keyboardType: TextInputType.number,
                          style: TextStyle(fontSize: 12),
                          decoration: InputDecoration(
                              labelText: 'No of Boxes',
                              icon: Icon(Icons.add_box),
                              filled: true,
                              fillColor: Colors.white70,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                              )),
                          // The validator receives the text that the user has entered.
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please enter no of Boxes';
                            }
                            return null;
                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10.0),
                          child: ElevatedButton(
                              style: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                (Set<MaterialState> states) {
                                  if (states.contains(MaterialState.pressed)) return CommonUtils.createMaterialColor(Color(0XFF394251));
                                  return CommonUtils.createMaterialColor(Color(0XFF394251)); // Use the component's default.
                                },
                              )),
                              onPressed: _saveForm,
                              child: Text(
                                'Submit',
                                style: TextStyle(fontSize: 12),
                              )),
                        )
                      ]),
                    )),
              ),
            ),
          )
        ]));
  }
}
