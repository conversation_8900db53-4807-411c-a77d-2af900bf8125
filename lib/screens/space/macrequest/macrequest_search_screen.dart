import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/device_util.dart';
import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/web_view_screen.dart';
import '../../../models/macrequests.dart';
import '../../../providers/macrequests_provider.dart';
import '../../../utils/request_api_utils.dart';
import '../../home/<USER>';
import '../../../utils/constvariables.dart';
import '../../../utils/preferences_utils.dart';
import '../../../utils/common_utils.dart';
import 'macrequest_detail_screen.dart';

class MacRequestSearchScreen extends StatefulWidget {
  static const routName = '/macrequests';

  @override
  _MacRequestSearchScreenState createState() => _MacRequestSearchScreenState();
}

class _MacRequestSearchScreenState extends State<MacRequestSearchScreen> {
  int selectedIndex = 0; //to handle which item is currently selected in the bottom app bar
  var _isLoading = false;
  var _isInit = true;
  var sessionid;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    if (_isInit) {
      setState(() {
        _isLoading = true;
      });
      sessionid = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
      Provider.of<MacRequestProvider>(context, listen: false).getMacRequestsData(context).then((_) {
        setState(() {
          _isLoading = false;
        });
      });
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  _cadviewer(String buildingid, String floorid, String spaceid) {
    /*var user = await SharedPrefUtils.readPrefStr(userNamevar);
    var sessionid = await SharedPrefUtils.readPrefStr(sessionIdvar);
    Map<String, String> headers = new Map();

    var temp = await SharedPrefUtils.readPrefStr(authVar);

    headers['Cookie'] = 'JSESSIONID=$sessionid';
    print(headers);
    final Uri url = Uri(
        scheme: 'https',
        path: 'dev.tangoanalytics.com/spacemgmt',
        queryParameters: {
          'floorId': '$floorid',
          'spaceId': '$spaceid',
          'buildingId': '$buildingid',
          'action': 'MAC',
          'JSESSIONID': '$sessionid'
        });
    print('cad viewer url $url');
    if (await canLaunch(url.toString())) {
      await launch(url.toString(),
          forceWebView: false,
          enableJavaScript: true,
          enableDomStorage: true,
          headers: headers);
    } else {
      throw 'Could not launch $url';
    }*/
    var host = ApiService.getServerurl();
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => WebViewContainer(
                '$cadViewerurl?floorId=$floorid&spaceId=$spaceid&buildingId=$buildingid&action=MAC', 'View Floor Plan', host)));
  }

  @override
  Widget build(BuildContext context) {
    final macProvider = Provider.of<MacRequestProvider>(context, listen: true);
    return Scaffold(
        floatingActionButton: FloatingActionButton(
            mini: true,
            backgroundColor: const Color(0XFFb10c00),
            foregroundColor: Colors.white,
            onPressed: () => showMenu(),
            child: Icon(Icons.add)),
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text('My MAC Requests ', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
          backgroundColor: Colors.white,
          elevation: 5,
          leading: new IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
            onPressed: () {
              Get.off(() => HomeScreen());
            },
          ),
        ),
        body: Stack(children: <Widget>[
          Positioned(
              top: 00,
              right: 0,
              left: 0,
              bottom: 0,
              child: ClipRRect(
                  // borderRadius: BorderRadius.circular(9),
                  child: Container(
                      //color: Colors.white,
                      child: Builder(builder: (context) {
                if (!_isLoading) {
                  print('Inside macrequests search build');
                  return macProvider.macRequestsList != null && macProvider.macRequestsList!.isNotEmpty
                      ? ListView.builder(
                          //  controller: _findSpacesScrollCtrl,
                          itemBuilder: (context, index) {
                            if (index == macProvider.macRequestsList!.length) {
                              return ProgressIndicatorCust();
                            }
                            return listTile(macProvider.macRequestsList![index]!);
                          },
                          itemCount: macProvider.macRequestsList!.length,
                        )
                      : Center(
                          child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                        );

                  // ListView.separated(
                  //   itemCount: macProvider.macRequestsList.length,
                  //   itemBuilder: (context, index) {
                  //     MacRequest mac = macProvider.macRequestsList[index];
                  //     return listTile(mac);
                  //   },
                  //   separatorBuilder: (BuildContext context, int index) =>
                  //       Divider(thickness: 1, color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                  // );
                } else {
                  return ProgressIndicatorCust();
                }
              })
                      // By default, show a loading spinner.
                      ))),
        ]));
  }

  Widget listTile(MacRequest reser) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      //padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      child: Column(
        children: <Widget>[
          // Widget to display the list of project
          ListTile(
              key: UniqueKey(),
              onTap: () {
                Navigator.of(context).pushNamed(
                  MacRequestDetail.routName,
                  arguments: reser.requestId,
                );
              },
              focusColor: Colors.blueGrey,
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text('MAC-${reser.requestNumber}',
                                style:
                                    TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: DeviceUtils.taFontSize(1.8, context))),
                            SizedBox(height: 10.0, width: 0),
                            Text('Request Type   ${reser.requestType}',
                                style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.3, context))),
                            SizedBox(height: 10.0, width: 0),
                            Text('Requested        ${(reser.requestedStartDate)}',
                                style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.3, context))),
                            SizedBox(
                              height: 10.0,
                            ),
                            Text('Status                ${reser.status}',
                                style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.3, context))),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          IconButton(
                              padding: EdgeInsets.zero,
                              tooltip: 'view floorplan',
                              icon: Image.asset('lib/icons/cadviewer.png'),
                              iconSize: 30,
                              color: CommonUtils.createMaterialColor(Color(0XFF394251)),
                              onPressed: () => _cadviewer('${reser.buildingId}', '${reser.floorId}', '${reser.spaceId}')),
                          IconButton(
                            padding: EdgeInsets.zero,
                            icon: new Icon(Icons.cancel_presentation),
                            iconSize: 30,
                            color: CommonUtils.createMaterialColor(Color(0XFF394251)),
                            onPressed: () {},
                          )
                        ],
                      ),
                    ],
                  ),
                ],
              ))
        ],
      ),
    );
  }

  showMenu() {
    showModalBottomSheet(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
        context: context,
        builder: (BuildContext context) {
          return ClipRRect(
            child: Container(
              height: 250,
              child: ListView(
                physics: NeverScrollableScrollPhysics(),
                children: <Widget>[
                  ListTile(
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).pushNamed(MacRequestDetail.routName);
                    },
                    title: Text(
                      "New Move Request",
                      style: TextStyle(color: Colors.white),
                    ),
                    leading: Icon(
                      Icons.move_to_inbox,
                      color: Colors.white,
                    ),
                  ),
                  ListTile(
                    title: Text(
                      "New Add Request",
                      style: TextStyle(color: Colors.white),
                    ),
                    leading: Icon(
                      Icons.add_box,
                      color: Colors.white,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).pushNamed(MacRequestDetail.routName);
                    },
                  ),
                  ListTile(
                    title: Text(
                      "New Exit Request",
                      style: TextStyle(color: Colors.white),
                    ),
                    leading: Icon(
                      Icons.close_outlined,
                      color: Colors.white,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).pushNamed(MacRequestDetail.routName);
                    },
                  ),
                  ListTile(
                    title: Text(
                      "New Update Request",
                      style: TextStyle(color: Colors.white),
                    ),
                    leading: Icon(
                      Icons.update,
                      color: Colors.white,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).pushNamed(MacRequestDetail.routName);
                    },
                  ),
                ],
              ),
            ),
          );
        });
  }
}
