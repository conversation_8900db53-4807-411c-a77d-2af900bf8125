import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';

import '../../../common/component_utils.dart';
import '../../../models/lookup_values.dart';
import '../../../models/spacemgmt/reservationlist_data.dart';
import '../../../providers/spacemanagement/reservation/reservation_controller.dart';
import 'package:toggle_switch/toggle_switch.dart';

import 'reservationdetails_screen.dart';

class Reservations extends StatelessWidget {
  Reservations({super.key});
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  ReservationController rsrvState = Get.put(ReservationController());

  ReservationController rsrvCtrl = Get.find<ReservationController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshData() async {
    //await rsrvCtrl.getSiteSearchData(searchText: rsrvCtrl.searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_RESERVATION')!.value!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
        actions: [
          IconButton(
            onPressed: () {
              debugPrint('hiiiii');
            },
            color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
            icon: const Icon(
              Icons.add_circle,
              //size: 30,
            ),
          )
        ],
      ),
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     debugPrint('hiiiii');
      //   },
      //   elevation: 5.0,
      //   child: const Icon(
      //     Icons.add,
      //     size: 30,
      //   ),
      // ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          Obx(
            () => Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  margin: const EdgeInsets.only(left: 15, top: 10, bottom: 10),
                  child: ToggleSwitch(
                    minWidth: 100.0,
                    minHeight: 30.0,
                    customTextStyles: const [
                      TextStyle(fontWeight: FontWeight.bold),
                      TextStyle(fontWeight: FontWeight.bold),
                    ],
                    fontSize: ComponentUtils.getFontSize(1.3),
                    radiusStyle: true,
                    cornerRadius: 20.0,
                    initialLabelIndex: rsrvCtrl.rsrvListStatus.value,
                    activeBgColor: [ComponentUtils.primecolor],
                    activeFgColor: Colors.white,
                    inactiveBgColor: Colors.white60,
                    inactiveFgColor: Colors.grey[900],
                    totalSwitches: 2,
                    labels: const ['Existed', 'Completed'],
                    onToggle: (index) async {
                      await rsrvCtrl.getRsrvListData(rsrvlistindex: index);
                    },
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(right: 15, top: 5, bottom: 5),
                  child: rsrvType(context),
                ),
              ],
            ),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshData,
              child: GetX<ReservationController>(
                initState: (state) {
                  rsrvCtrl.getRsrvListData(action: 'onLoad');
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.rsrvlist.isEmpty
                          ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.rsrvlist[index]);
                              },
                              itemCount: _.rsrvlist.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget rsrvType(BuildContext? context) {
    return (rsrvCtrl.rsrvtypelov.isNotEmpty && rsrvCtrl.rsrvtypelov != null)
        ? DropdownButton(
            hint: const Text(
              'Select ',
              style: TextStyle(fontSize: 14, color: Colors.black),
            ),
            onChanged: (dynamic newValue) async {
              debugPrint('newValue>>> $newValue');
              rsrvCtrl.rsrvtype.value = newValue;
              await rsrvCtrl.getRsrvListData();
            },
            value: rsrvCtrl.rsrvtype.value,
            items: rsrvCtrl.rsrvtypelov.map((LookupValues l) {
              return DropdownMenuItem(
                value: l.lookupCode,
                child: Text(
                  l.lookupValue!,
                  style: const TextStyle(fontSize: 14, color: Colors.black),
                ),
              );
            }).toList(),
          )
        : DropdownButton(
            hint: const Text(
              'Select',
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
            },
            value: null,
            items: const [
              DropdownMenuItem(
                  value: null,
                  child: Text(
                    "Select",
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  )),
            ],
          );
  }

  Widget listitem(BuildContext context, ReservationListData rsrv) {
    final primary = Color(0xff696b9e);
    final secondary = Color(0xfff29a94);

    return GestureDetector(
        onTap: () {
          Get.to(() => ReservationDetails(
                action: 'edit',
                rsrvData: rsrv,
                hdrtext: 'Details',
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          //padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Container(
                      padding: const EdgeInsets.only(bottom: 10, top: 10, left: 10, right: 10),
                      margin: const EdgeInsets.only(bottom: 10),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                        color: rsrv.reservationType == 'WORKSPACE' ? Color.fromARGB(255, 3, 37, 99) : Colors.green,
                      ),
                      child: Row(
                        children: [
                          Text(
                            rsrv.reservationNumber ?? '',
                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: ComponentUtils.getFontSize(1.5)),
                          ),
                          const SizedBox(
                            width: 2,
                          ),
                          Text(
                            rsrv.reservationTitle ?? '',
                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: ComponentUtils.getFontSize(1.5)),
                          ),
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const SizedBox(
                          width: 10,
                        ),
                        Icon(
                          Icons.workspaces,
                          size: 25,
                          color: secondary,
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${rsrv.buildingName} - ${rsrv.floorName}',
                                style: TextStyle(
                                  color: primary,
                                  fontSize: ComponentUtils.getFontSize(1.5),
                                )),
                            const SizedBox(
                              height: 5,
                            ),
                            Text('${rsrv.spaceDisplayName}',
                                style: TextStyle(
                                  color: primary,
                                  fontSize: ComponentUtils.getFontSize(1.5),
                                )),
                          ],
                        ),
                        // Expanded(
                        //   child: IconButton(
                        //     alignment: Alignment.centerRight,
                        //     padding: const EdgeInsets.only(right: 20, left: 5, top: 5, bottom: 5),
                        //     tooltip: 'view floorplan',
                        //     icon: //isLargeScreen ?
                        //         // const Image(image: AssetImage('lib/icons/plan-icon.png'), width: 45, height: 45, fit: BoxFit.fill)
                        //         //:
                        //         const Image(image: AssetImage('lib/icons/plan-icon.png')),
                        //     iconSize: 35,
                        //     color: Colors.black38, //   CommonUtils.createMaterialColor(Color(0XFF394251)),
                        //     onPressed: () => CommonUtils.reservationCadViewer(Get.context!, '${rsrv.buildingId}', '${rsrv.floorId}',
                        //         '${rsrv.spaceId}', '${rsrv.startDateTime}', '${rsrv.endDateTime}'),
                        //   ),
                        // ),
                      ],
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const SizedBox(
                              width: 10,
                            ),
                            Icon(
                              Icons.schedule,
                              size: 25,
                              color: secondary,
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(rsrv.startDateTime ?? '',
                                    style: TextStyle(
                                      color: primary,
                                      fontSize: ComponentUtils.getFontSize(1.3),
                                    )),
                                const SizedBox(
                                  height: 7,
                                ),
                                Text(rsrv.endDateTime ?? '',
                                    style: TextStyle(
                                      color: primary,
                                      fontSize: ComponentUtils.getFontSize(1.3),
                                    )),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    if (rsrvCtrl.rsrvListStatus.value == 1)
                      Container(
                        margin: const EdgeInsets.only(left: 18, right: 5, bottom: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.only(right: 20, left: 5, top: 5, bottom: 5),
                              tooltip: 'view floorplan',
                              icon: //isLargeScreen ?
                                  // const Image(image: AssetImage('lib/icons/plan-icon.png'), width: 45, height: 45, fit: BoxFit.fill)
                                  //:
                                  const Image(image: AssetImage('lib/icons/plan-icon.png')),
                              iconSize: 35,
                              color: Colors.black38, //   CommonUtils.createMaterialColor(Color(0XFF394251)),
                              onPressed: () => CommonUtils.reservationCadViewer(Get.context!, '${rsrv.buildingId}', '${rsrv.floorId}',
                                  '${rsrv.spaceId}', '${rsrv.startDateTime}', '${rsrv.endDateTime}'),
                            ),
                            Container(
                              margin: const EdgeInsets.only(right: 10.0),
                              child: TaButton(
                                type: 'elevate',
                                buttonText: 'Rebook',
                                onPressed: () async {
                                  // Get.to(() => ReservationDetails(
                                  //       action: 'rebook',
                                  //       rsrvData: rsrv,
                                  //     ));
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (rsrvCtrl.rsrvListStatus.value == 0)
                      Container(
                        margin: const EdgeInsets.only(left: 18, right: 5, bottom: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                IconButton(
                                  alignment: Alignment.centerRight,
                                  padding: const EdgeInsets.only(right: 20, left: 5, top: 5, bottom: 5),
                                  tooltip: 'view floorplan',
                                  icon: //isLargeScreen ?
                                      // const Image(image: AssetImage('lib/icons/plan-icon.png'), width: 45, height: 45, fit: BoxFit.fill)
                                      //:
                                      const Image(image: AssetImage('lib/icons/plan-icon.png')),
                                  iconSize: 35,
                                  color: Colors.black38, //   CommonUtils.createMaterialColor(Color(0XFF394251)),
                                  onPressed: () => CommonUtils.reservationCadViewer(Get.context!, '${rsrv.buildingId}', '${rsrv.floorId}',
                                      '${rsrv.spaceId}', '${rsrv.startDateTime}', '${rsrv.endDateTime}'),
                                ),
                                const SizedBox(
                                  width: 20,
                                ),
                                IconButton(
                                  onPressed: () {
                                    ComponentUtils.showwarnpopup(
                                        cancelBtn: 'No',
                                        confirmBtn: 'Yes',
                                        contenttext: 'Do you really want to cancel the reservation:  ${rsrv.reservationTitle} ?',
                                        title: 'Confirmation:',
                                        confirmfunction: () {
                                          Get.back();
                                          rsrvCtrl.updateStatus(rsrv.reservationId, 'CANCELED');
                                        });
                                  },
                                  color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                                  icon: const Icon(
                                    Icons.cancel,
                                    //size: 30,
                                  ),
                                ),
                              ],
                            ),
                            if (rsrvCtrl.rsrvListStatus.value == 0 &&
                                rsrv.status != 'CHECK-IN' &&
                                isCurrrentDate(rsrv.startDateTime ?? '', rsrv.endDateTime ?? '', rsrv.status))
                              Container(
                                margin: const EdgeInsets.only(right: 10.0),
                                child: TaButton(
                                  type: 'elevate',
                                  buttonText: 'Check In',
                                  onPressed: () async {
                                    rsrvCtrl.updateStatus(rsrv.reservationId, 'CHECK-IN');
                                  },
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  bool isCurrrentDate(String startdate, String enddate, String? status) {
    bool showcheckin = false;
    try {
      DateTime today = DateTime.now();
      DateTime start = DateFormat('MM/dd/yyyy hh:mm a').parse(startdate);
      DateTime end = DateFormat('MM/dd/yyyy hh:mm a').parse(enddate);

      if ((today.isBefore(end) && today.isAfter(start)) && (status != 'CHECK-IN')) {
        debugPrint("Inside current");
        showcheckin = true;
      }
    } catch (error) {
      debugPrint('isCurrrentDate>>>>>>>>$error');
    }
    return showcheckin;
  }
}
