import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/spacemgmt/property/activeproperties_data.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import '../../../common/component_utils.dart';
import '../../../common/widgets/component_widgets/tacheckbox.dart';
import '../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../common/widgets/component_widgets/taforminputtext.dart';
import '../../../models/lookup_values.dart';
import '../../../models/spacemgmt/buildings/activereservbuildings_data.dart';
import '../../../models/spacemgmt/buildings/activereservfloors_data.dart';
import '../../../models/spacemgmt/reservationlist_data.dart';
import '../../../providers/spacemanagement/reservation/reservationdetails_controller.dart';
import 'reserv_spaces_screen.dart';

class ReservationDetails extends StatelessWidget {
  final action;
  final restype;
  ReservationListData? rsrvData;
  final String? hdrtext;

  ReservationDetails({Key? key, this.action, this.rsrvData, this.hdrtext, this.restype}) : super(key: key);
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  ReservationDetailsController rsrvDetState = Get.put(ReservationDetailsController());

  ReservationDetailsController rsrvDetCtrl = Get.find<ReservationDetailsController>();
  LabelController talabel = Get.find<LabelController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GetX<LabelController>(
      initState: (state) async {
        rsrvDetCtrl.isdetloading.value == true;
        rsrvDetCtrl.rsrvRow.value = rsrvData ?? ReservationListData();
        await rsrvDetCtrl.setCurrentRow(action: action, resType: restype);
        Future.delayed(const Duration(seconds: 1), () async {
          //await rsrvDetCtrl.getlabels(talabel);
          //if (action == 'create') {
          // rsrvDetCtrl.rsrvRow.value = rsrvData ?? ReservationListData();
          //  await rsrvDetCtrl.setCurrentRow(action: action, resType: restype);
          //   } else if (action == 'rebook') {
          // rsrvDetCtrl.rsrvRow.value = rsrvData ?? ReservationListData();
          //  await rsrvDetCtrl.setCurrentRow(action: action);
          //  }
        });
        rsrvDetCtrl.isdetloading.value == false;
      },
      builder: (_) {
        return (rsrvDetCtrl.isdetloading.value)
            ? ComponentUtils.labelLoadScaffold()
            : Scaffold(
                appBar: AppBar(
                  iconTheme: Theme.of(context).appBarTheme.iconTheme,
                  title: Text(hdrtext ?? '', style: ComponentUtils.appbartitlestyle),
                  backgroundColor: Colors.white,
                  elevation: 5,
                  leading: IconButton(
                    icon: ComponentUtils.backpageIcon,
                    color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                    onPressed: () {
                      debugPrint('------back-------');
                      Get.back();
                    },
                  ),
                ),
                body: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: rsrvDetCtrl.isdetloading.value ? const ProgressIndicatorCust() : reservationDetForm(),
                ),
                //bottomNavigationBar: bottombuttonbar(_formKey),
                // bottomSheet: bottombuttonbar(_formKey),
              );
      },
    );
  }

  Widget reservationDetForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Obx(
        () => Column(
          children: <Widget>[
            // if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJNAME') != null)
            TaFormInputText(
              //label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJNAME')!.value,
              label: 'Title',
              readOnly: rsrvDetCtrl.rsrvRow.value.status == 'COMPLETED' ||
                  rsrvDetCtrl.rsrvRow.value.status == 'CHECK-IN', //talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJNAME').ro,
              value: rsrvDetCtrl.rsrvRow.value.reservationTitle ?? '',
            ),

            // if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH') != null)
            TaFormDropdown(
              readonly: rsrvDetCtrl.rsrvRow.value.status == 'COMPLETED' || rsrvDetCtrl.rsrvRow.value.status == 'CHECK-IN',
              label: 'Reservation Type', //talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.value,
              emptttext: 'Select',
              onChanged: //(mode != 'edit' || talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.ro!)
                  //? null :
                  (newValue) {
                debugPrint('Reservation Type>>>' + newValue);
                rsrvDetCtrl.rsrvRow.value.reservationType = newValue;
              },
              value: (rsrvDetCtrl.rsrvRow.value.reservationType == '' || rsrvDetCtrl.rsrvRow.value.reservationType == 'null')
                  ? null
                  : rsrvDetCtrl.rsrvRow.value.reservationType,
              listflag: (rsrvDetCtrl.rsrvtypelov.value.isNotEmpty && rsrvDetCtrl.rsrvtypelov.value != null),
              items: rsrvDetCtrl.rsrvtypelov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  value: l.lookupCode,
                  child: Text(
                    l.lookupValue!,
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                  ),
                );
              }).toList(),
            ),

            // if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER') != null)
            TaFormInputText(
              label: 'Requested By',
              readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER').ro,
              value: rsrvDetCtrl.rsrvRow.value.createdBy ?? '',
            ),
            //self reserv
            TaFormCheckBoxicon(
              label: 'Self Reserve', //talabel.get('TMCMOBILE_COMMON_ADHOCTASKS_DETAILS_SENDEMAIL')!.value,
              enabled: rsrvDetCtrl.rsrvRow.value.status == 'COMPLETED' || rsrvDetCtrl.rsrvRow.value.status == 'CHECK-IN' ? false : true,
              value: rsrvDetCtrl.rsrvRow.value.selfReserve?.toUpperCase() == 'Y',
              onChanged: (val) {
                if (val == true) {
                  if (rsrvDetCtrl.user.value.personId == null || rsrvDetCtrl.user.value.personId == 0) {
                    rsrvDetCtrl.rsrvRow.value.selfReserve = 'N';
                    ComponentUtils.showpopup(type: 'Error', msg: "You cannot self reserve. No person information found!");
                  }
                }
              },
            ),
//all day
            TaFormCheckBoxicon(
              label: 'All Day?', //talabel.get('TMCMOBILE_COMMON_ADHOCTASKS_DETAILS_SENDEMAIL')!.value,
              enabled: rsrvDetCtrl.rsrvRow.value.status == 'COMPLETED' || rsrvDetCtrl.rsrvRow.value.status == 'CHECK-IN' ? false : true,
              value: rsrvDetCtrl.rsrvRow.value.allDay?.toUpperCase() == 'Y',
              onChanged: (val) {
                rsrvDetCtrl.allDayVCL(val ?? false);
              },
            ),
//start date
//             TextFormField(
//               readOnly: true,
//               //key: startDateFieldState,
//               style: const TextStyle(fontSize: 14),
//               validator: (value) {
//                 if (value!.isEmpty) {
//                   return 'Please select Start Date';
//                 }
//                 return null;
//               },
//               decoration: InputDecoration(
//                   labelStyle: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//                   border: InputBorder.none,
//                   labelText: 'Start Date',
//                   //prefixIcon: Icon(Icons.date_range_outlined, color: ComponentUtils.primecolor,
//                   filled: true,
//                   fillColor: Colors.white,
//                   suffixIcon: IconButton(
//                     icon: const Icon(Icons.date_range_outlined),
//                     onPressed: () {
//                       rsrvDetCtrl.selectDate(Get.context!, 'start');
//                     },
//                   )),
//               controller: rsrvDetCtrl.startdatecntrl,
//               // onFieldSubmitted: (_) {
//               //   FocusScope.of(context).requestFocus(_endDateFocusNode);
//               // },
//             ),
//             //end date
//             TextFormField(
//               readOnly: true,
//               //key: startDateFieldState,
//               style: const TextStyle(fontSize: 14),
//               validator: (value) {
//                 if (value!.isEmpty) {
//                   return 'Please select End Date';
//                 }
//                 return null;
//               },
//               decoration: InputDecoration(
//                   labelStyle: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//                   border: InputBorder.none,
//                   labelText: 'End Date',
//                   //prefixIcon: Icon(Icons.date_range_outlined, color: ComponentUtils.primecolor,
//                   filled: true,
//                   fillColor: Colors.white,
//                   suffixIcon: IconButton(
//                     icon: const Icon(Icons.date_range_outlined),
//                     onPressed: () {
//                       rsrvDetCtrl.selectDate(Get.context!, 'end');
//                     },
//                   )),
//               controller: rsrvDetCtrl.enddatecntrl,
//               // onFieldSubmitted: (_) {
//               //   FocusScope.of(context).requestFocus(_endDateFocusNode);
//               // },
//             ),
//             //property
//             TaFormDropdown(
//               readonly: rsrvDetCtrl.rsrvRow.value.status == 'COMPLETED' || rsrvDetCtrl.rsrvRow.value.status == 'CHECK-IN',
//               label: 'Property', //talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.value,
//               emptttext: 'Select',
//               onChanged: //(mode != 'edit' || talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.ro!)
//                   //? null :
//                   (newValue) {
//                 debugPrint('Property >>>' + newValue);
//                 rsrvDetCtrl.rsrvRow.value.propertyId = newValue;
//               },
//               value: (rsrvDetCtrl.rsrvRow.value.propertyId == '' || rsrvDetCtrl.rsrvRow.value.propertyId == 'null')
//                   ? null
//                   : rsrvDetCtrl.rsrvRow.value.propertyId,
//               listflag: (rsrvDetCtrl.propertyLOV.value.isNotEmpty && rsrvDetCtrl.propertyLOV.value != null),
//               items: rsrvDetCtrl.propertyLOV.value.map((ActiveProperties l) {
//                 return DropdownMenuItem(
//                   value: l.propertyId,
//                   child: Text(
//                     l.propertyName ?? '',
//                     style: const TextStyle(fontSize: 14, color: Colors.black),
//                   ),
//                 );
//               }).toList(),
//             ),
//             //building
//             TaFormDropdown(
//               readonly: rsrvDetCtrl.rsrvRow.value.status == 'COMPLETED' || rsrvDetCtrl.rsrvRow.value.status == 'CHECK-IN',
//               label: 'Building', //talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.value,
//               emptttext: 'Select',
//               onChanged: //(mode != 'edit' || talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.ro!)
//                   //? null :
//                   (newValue) {
//                 debugPrint('Building >>>>$newValue');
//                 rsrvDetCtrl.rsrvRow.value.buildingId = newValue;
//               },
//               value: (rsrvDetCtrl.rsrvRow.value.buildingId == '' || rsrvDetCtrl.rsrvRow.value.buildingId == 'null')
//                   ? null
//                   : rsrvDetCtrl.rsrvRow.value.buildingId,
//               listflag: (rsrvDetCtrl.buildingLOV.value.isNotEmpty && rsrvDetCtrl.buildingLOV.value != null),
//               items: rsrvDetCtrl.buildingLOV.value.map((ActiveReserveBuilding l) {
//                 return DropdownMenuItem(
//                   value: l.storeId,
//                   child: Text(
//                     l.storeName ?? '',
//                     style: const TextStyle(fontSize: 14, color: Colors.black),
//                   ),
//                 );
//               }).toList(),
//             ),
//             //Floor
//             TaFormDropdown(
//               readonly: rsrvDetCtrl.rsrvRow.value.status == 'COMPLETED' || rsrvDetCtrl.rsrvRow.value.status == 'CHECK-IN',
//               label: 'Floor', //talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.value,
//               emptttext: 'Select',
//               onChanged: //(mode != 'edit' || talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.ro!)
//                   //? null :
//                   (newValue) {
//                 debugPrint('Floor >>>>$newValue');
//                 rsrvDetCtrl.rsrvRow.value.floorId = newValue;
//               },
//               value: (rsrvDetCtrl.rsrvRow.value.floorId == '' || rsrvDetCtrl.rsrvRow.value.floorId == 'null')
//                   ? null
//                   : rsrvDetCtrl.rsrvRow.value.floorId,
//               listflag: (rsrvDetCtrl.floorsLOV.value.isNotEmpty && rsrvDetCtrl.floorsLOV.value != null),
//               items: rsrvDetCtrl.floorsLOV.value.map((ActiveReserveFloors l) {
//                 return DropdownMenuItem(
//                   value: l.storeId,
//                   child: Text(
//                     l.storeName ?? '',
//                     style: const TextStyle(fontSize: 14, color: Colors.black),
//                   ),
//                 );
//               }).toList(),
//             ),
//             //Space Id
//             //  if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE') != null)
//             TaFormInputText(
//               label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')!.value,
//               readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER').ro,
//               value: rsrvDetCtrl.rsrvRow.value.spaceDisplayName ?? '',
//               controller: rsrvDetCtrl.spaceNameCtrl,
//               suffixbutton: IconButton(
//                 onPressed: () async {
//                   //if (!srCtrl.readonly.value && !talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')!.ro!) {
//                   await Get.to(
//                     () => ReserveSpaces(),
//                   );
//                 },
//                 icon: const Icon(Icons.search),
//               ),
//               onChanged: (val) {
//                 debugPrint('space val--------$val');

//                 // srCtrl.servicerequest.value.assetInstanceName = null;
//                 // srCtrl.servicerequest.value.assetInstanceId = null;
//                 // srCtrl.assetInstanceNameCtrl.text = '';

//                 // srCtrl.spaceNameCtrl.text = val;
//                 // srCtrl.servicerequest.value.spaceDisplayName = val;
//               },
//               inputtype: //(srCtrl.readonly.value || talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')!.ro!) ? '' :
//                   'inputlov',
//               validate: (String value) {
//                 if (value.isEmpty) {
//                   return 'Space is Required';
//                 }
//                 return null;
//               },
//             ),
          ],
        ),
      ),
    );
  }
}
