import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/spacemgmt/buildings/building_data.dart';
import '../../../providers/spacemanagement/buildings/buildingdetails_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';

class BuildingDetails extends StatelessWidget {
  final storeId;
  final storeName;
  final String? subEntityType;
  BuildingData? building;
  BuildingDetails({super.key, this.storeId, this.storeName, this.building, this.subEntityType});
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  BuildingDetailsController buildingsSearchState = Get.put(BuildingDetailsController());

  BuildingDetailsController buildingCtrlr = Get.find<BuildingDetailsController>();
  LabelController talabel = Get.find<LabelController>();
  final primary = Color(0xff696b9e);
  final secondary = Color(0xfff29a94);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(storeName ?? '', style: ComponentUtils.appbartitlestyle),
        actions: [
          IconButton(
              onPressed: () async {},
              icon: Icon(
                Icons.menu,
                color: ComponentUtils.primecolor,
              )),
        ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            // Get.off(() => HomeScreen());
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<BuildingDetailsController>(
        initState: (state) {
          buildingCtrlr.isDetailsLoading.value = true;
          Future.delayed(const Duration(seconds: 1), () async {
            try {
              await talabel.getlabels('TMCMOBILE_BUILDINGDETAILS_GENERAL', 'Building_details');
              await buildingCtrlr.getBuildingRecord(storeId?.toString());
            } finally {
              buildingCtrlr.isDetailsLoading.value = false;
            }
          });
        },
        builder: (_) {
          return (buildingCtrlr.isDetailsLoading.value)
              ? const ProgressIndicatorCust()
              : Obx(() => SingleChildScrollView(
                    child: Column(
                      children: [
                        headerSection(),
                        occupencySection(),
                        Section(label: 'Map', child: Text('hiiiiii')),
                      ],
                    ),
                  ));
        },
      ),
    );
  }

  Widget occupencySection() {
    return Section(label: 'Occupency', showlabel: true, child: Text('hiiiiii'));
  }

  Widget headerSection() {
    return Section(
        label: 'Headers',
        child: Column(
          children: [
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_BUILDINGNUM') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_BUILDINGNUM')?.value ?? 'Store Number',
                subEntityType?.toUpperCase() == 'FLOOR'
                    ? buildingCtrlr.buildingRec.value.buildingNumber?.toString() ?? ''
                    : (subEntityType?.isEmpty ?? true)
                        ? buildingCtrlr.buildingRec.value.storeNumber?.toString() ?? ''
                        : '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_PROPERTYNAME') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_PROPERTYNAME')?.value ?? 'Property Name:',
                subEntityType?.toUpperCase() == 'FLOOR'
                    ? buildingCtrlr.buildingRec.value.bldgPropName?.toString() ?? ''
                    : (subEntityType?.isEmpty ?? true)
                        ? buildingCtrlr.buildingRec.value.propertyName?.toString() ?? ''
                        : '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_ENTITYTYPE') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_ENTITYTYPE')?.value ?? 'Entity Type:',
                buildingCtrlr.buildingRec.value.locationTypeCode?.toString() ?? '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_STATUS') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_STATUS')?.value ?? 'Status:',
                buildingCtrlr.buildingRec.value.status?.toString() ?? '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_CADRENTAREA') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_CADRENTAREA')?.value ?? 'Cad Rentable Area:',
                buildingCtrlr.buildingRec.value.spaceUom?.toLowerCase() == 'sqft'
                    ? buildingCtrlr.buildingRec.value.cadRentableSf?.toString() ?? ''
                    : buildingCtrlr.buildingRec.value.spaceUom?.toLowerCase() == 'sqm'
                        ? buildingCtrlr.buildingRec.value.cadRentableSm?.toString() ?? ''
                        : '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_TOTHEADCOUNT') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_TOTHEADCOUNT')?.value ?? 'Total Head Count',
                buildingCtrlr.buildingRec.value.totalHeadcount?.toString() ?? '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_CONSTRACTUALTYPE') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_CONSTRACTUALTYPE')?.value ?? 'Contractual Type',
                buildingCtrlr.buildingRec.value.contractualType?.toString() ?? '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_LEASEEXPDATE') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_LEASEEXPDATE')?.value ?? 'Lease Expiry Date',
                buildingCtrlr.buildingRec.value.leaseExpDate?.toString() ?? '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_BRANDNAME') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_BRANDNAME')?.value ?? 'Brand Name',
                buildingCtrlr.buildingRec.value.brandName?.toString() ?? '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            if (talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_SPACEUOM') != null)
              ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_BUILDINGDETAILS_GENERAL_HDR_SPACEUOM')?.value ?? 'Space Uom',
                buildingCtrlr.buildingRec.value.spaceUom?.toString() ?? '',
                labelStyle: TextStyle(
                  color: primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
          ],
        ));
  }

  Widget Section({String? label, bool? showlabel, Widget? child, double? height}) {
    return Container(
        padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
        margin: const EdgeInsets.fromLTRB(20, 20, 20, 0),
        // height: 200,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.0),
            boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 5, offset: Offset(0, 0))]),
        // height: height ?? 200,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showlabel ?? false)
              Text(
                label!,
                style: const TextStyle(
                  // color: Colors.grey,
                  fontSize: 15, //isLargeScreen ? 25 : 15,
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (label != null)
              const SizedBox(
                height: 1,
              ),
            child!
          ],
        ));
  }
}
