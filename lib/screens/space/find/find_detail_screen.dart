import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import '../../../common/component_utils.dart';
import '../../../models/find.dart';
import '../../../providers/find_provider.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../home/<USER>';
import '../../../utils/constvariables.dart';
import '../../../utils/preferences_utils.dart';
import '../../../utils/common_utils.dart';

class FindDetails extends StatefulWidget {
  static const routName = '/finddetails';

  @override
  _FindDetailsState createState() => _FindDetailsState();
}

class _FindDetailsState extends State<FindDetails> {
  var _isInit = true;
  var _isLoading = true;
  FindPerson? personData;

  @override
  void didChangeDependencies() async {
    if (_isInit) {
      setState(() {
        _isLoading = true;
      });

      final args = ModalRoute.of(context)!.settings.arguments as FindDetArgmnts;
      if (args.findType == 'Person') {
        Provider.of<FindProvider>(context, listen: false).getFindPersonDeatils(args.findId).then((_) {
          setState(() {
            _isLoading = false;
          });
        });
      } else {
        Provider.of<FindProvider>(context, listen: false).getFindSpaceDeatils(args.findId).then((_) {
          setState(() {
            _isLoading = false;
          });
        });
      }
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as FindDetArgmnts;
    final findProvState = Provider.of<FindProvider>(context, listen: true);
    return Scaffold(
        //backgroundColor: Colors.grey,
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text(args.findType + ' Details', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
          backgroundColor: Colors.white,
          elevation: 5,
          leading: new IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
            onPressed: () {
              Get.back();
            },
          ),
          // ),
        ),
        body: Stack(children: <Widget>[
          Positioned(
            top: 00,
            right: 0,
            left: 0,
            bottom: 0,
            child: ClipRRect(
              // borderRadius: BorderRadius.circular(9),
              child: Container(
                //color: Colors.white,
                padding: EdgeInsets.fromLTRB(10.0, 10.0, 10.0, 0.0),
                child: Form(
                    child: SingleChildScrollView(
                  padding: EdgeInsets.only(top: 10),
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                    _isLoading
                        ? ProgressIndicatorCust()
                        : args.findType == 'Person'
                            ? personDataColumn(findProvState.personData)
                            : spaceDataColumn(findProvState.spaceData),
                  ]),
                )),
              ),
            ),
          )
        ]));
  }

  Widget spaceDataColumn(FindSpace? fs) {
    return Column(
      children: [
        TaInputText(
          title: "Space Name",
          value: fs?.spaceName.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Space",
          value: fs?.spaceDisplayName.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Space Number",
          value: fs?.spaceNumber.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Floor Number",
          value: fs?.floorNumber.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Occupancy Status",
          value: fs?.occupencyStatus.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Space Type",
          value: fs?.spaceType.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Space Category",
          value: fs?.spaceCategory.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Space Use",
          value: fs?.spaceUse.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Charge Type",
          value: fs?.chargeType.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Actual SF",
          value: fs?.actualSf.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Usable SF",
          value: fs?.usableSf.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Cad Rentable SF",
          value: fs?.cadRentableSf.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Capacity Planned",
          value: fs?.capacityPlanned.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Total Head Count",
          value: fs?.totalHeadcount.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Capacity Available",
          value: fs?.capacityAvailable.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Department Id",
          value: fs?.departmentId.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Department Name",
          value: fs?.departmentName.toString(),
          readOnly: true,
        ),
      ],
    );
  }

  Widget personDataColumn(FindPerson? fp) {
    return Column(
      children: [
        TaInputText(
          title: "Last Name",
          value: fp?.lastName.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "First Name",
          value: fp?.firstName.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Nick Name",
          value: fp?.nickname?.toString(),
          readOnly: true,
        ),
        // TaInputText(
        //   title: "Employee Number",
        //   value: fp.employeeNumber.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "Building Assigned",
        //   value: fp.assignedBuildingId.toString(),
        //   readOnly: true,
        // ),
        TaInputText(
          title: "Phone Number",
          value: fp?.primaryPhone.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Mobile Number",
          value: fp?.mobileNumber.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Email Address",
          value: fp?.primaryEmail.toString(),
          readOnly: true,
        ),
        TaInputText(
          title: "Title",
          value: fp?.title.toString(),
          readOnly: true,
        ),
        // TaInputText(
        //   title: "Grade Level",
        //   value: fp.gradeLevel.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "Shift Worker",
        //   value: fp.shiftWorker.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "Employee Type",
        //   value: fp.employeeType.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "Work from Home",
        //   value: fp.workFromHome.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "Status",
        //   value: fp.status.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "Hire Date",
        //   value: fp.hireDate.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "End Date",
        //   value: fp.endDate.toString(),
        //   readOnly: true,
        // ),
        // TaInputText(
        //   title: "Manger Name",
        //   value: fp.managerName.toString(),
        //   readOnly: true,
        // ),
      ],
    );
  }
}

class FindDetArgmnts {
  final String findType;
  final int? findId;

  FindDetArgmnts(this.findType, this.findId);
}

class InputTextAttr extends StatefulWidget {
  final Function(String)? onChanged;
  final String? title;
  final String? value;
  final IconData? icon;
  final bool readOnly;
  final bool dateSelect;
  final int? maxLines;
  final TextEditingController? controller;
  InputTextAttr(
      {this.title, this.value, this.icon, this.readOnly = false, this.onChanged, this.controller, this.dateSelect = true, this.maxLines});
  @override
  _StateInputTextField createState() => _StateInputTextField();
}

class _StateInputTextField extends State<InputTextAttr> {
  TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    _controller.text = widget.controller != null ? widget.controller!.text : widget.value!;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 5,
        vertical: 1,
      ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextField(
          controller: _controller,
          maxLines: widget.maxLines,
          readOnly: widget.readOnly,
          cursorColor: Theme.of(context).primaryColor,
          style: TextStyle(color: Colors.black, fontSize: 14),
          // onChanged: (text) {
          //   widget.onChanged(text);
          // },
          decoration: InputDecoration(
            suffixIcon: Container(
              height: 0,
              width: 0,
            ),
            labelText: widget.title,
            border: widget.readOnly
                ? InputBorder.none
                : new OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(17)), borderSide: new BorderSide(color: Colors.red)),
            contentPadding: EdgeInsets.symmetric(horizontal: 25, vertical: 13),
            hintStyle: TextStyle(color: Colors.black87, fontSize: 15),
            //floatingLabelBehavior:
          ),
        ),
      ),
    );
  }
}
