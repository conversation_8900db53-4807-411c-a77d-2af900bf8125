import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/models/person_list.dart';
import 'package:tangoworkplace/providers/reservation_provider.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

class AssignedUserSearch extends StatefulWidget {
  static const routName = '/assignedusersearch';
  @override
  _AssignedUserSearchState createState() => _AssignedUserSearchState();
}

class _AssignedUserSearchState extends State<AssignedUserSearch> {
  final TextEditingController _filter = new TextEditingController();
  final TextEditingController _selectedController = new TextEditingController();
  String _searchText = "";
  List? filteredNames;
  List<PersonList>? assignedtoLov;
  String selectedUsers = "";
  String selectedPersonid = "";
  List? arguments;

  @override
  void initState() {
    _filter.clear();
    super.initState();
  }

  @override
  Future<void> didChangeDependencies() async {
    bool _isInit = true;
    if (_isInit) {
      arguments = ModalRoute.of(context)!.settings.arguments as List?;
      if (arguments != null) {
        debugPrint('arguments>>>$arguments');
        selectedUsers = arguments![0];
        if (arguments?[1] != null) {
          selectedPersonid = arguments![1] ?? null;
          _selectedController.text = selectedUsers;
        }
        debugPrint('selectedUsers>>>>$selectedUsers');
        debugPrint('selectedPersonid>>>>$selectedPersonid');
      }

      ReservationProvider prov = Provider.of<ReservationProvider>(context, listen: false);
      prov.getRecentAssignedToLov(context).then((_) {
        setState(() {
          filteredNames = prov.recentAssignedToLov;
        });
      });
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(actions: <Widget>[
          Padding(
              padding: EdgeInsets.only(right: 20.0),
              child: GestureDetector(
                onTap: () {
                  Navigator.pop(context, [selectedUsers, selectedPersonid]);
                },
                child: Icon(
                  Icons.check,
                  size: 26.0,
                ),
              ))
        ], elevation: 0, centerTitle: true, title: Text("Search Persons", style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold))),
        body: Stack(children: <Widget>[
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
                  color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                  shape: BoxShape.rectangle),
              height: 115,
            ),
          ),
          Positioned(
              top: 00,
              right: 15,
              left: 15,
              bottom: 10,
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(9),
                  child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.red,
                      child: Container(
                          color: Colors.white,
                          child: Center(
                              child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            child: Column(children: <Widget>[
                              SizedBox(
                                width: 10,
                              ),
                              new TextField(
                                  textInputAction: TextInputAction.search,
                                  style: TextStyle(fontSize: 12),
                                  controller: _filter,
                                  onSubmitted: (value) {
                                    _getNames(value);
                                  },
                                  decoration: InputDecoration(
                                    labelText: 'Enter Search Text',
                                    filled: true,
                                    fillColor: Colors.white70,
                                  )),
                              if (selectedUsers.isNotEmpty)
                                TextField(
                                  readOnly: true,
                                  style: TextStyle(fontSize: 12),
                                  controller: _selectedController,
                                  decoration: InputDecoration(
                                    hintText: "Selected Persons",
                                    suffixIcon: IconButton(
                                      onPressed: () {
                                        _selectedController.clear();
                                        setState(() {
                                          selectedUsers = "";
                                          selectedPersonid = "";
                                        });
                                      },
                                      icon: Icon(Icons.clear),
                                    ),
                                  ),
                                ),
                              SizedBox(height: 10),
                              Text(_filter.text.isNotEmpty ? 'Suggested Persons' : 'Recent Persons',
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                              SizedBox(height: 10),
                              Expanded(child: _buildList()),
                            ]),
                          ))))))
        ]));
  }

  Widget _buildList() {
    print(_searchText);
    if (filteredNames != null) {
      if (filteredNames!.isNotEmpty) {
        return ListView.separated(
          itemCount: filteredNames != null ? filteredNames!.length : 0,
          itemBuilder: (context, index) {
            PersonList p = filteredNames!.elementAt(index);
            return ListTile(
              minLeadingWidth: 10,
              leading: Icon(Icons.person_sharp),
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${p.firstName} ${p.lastName}',
                      style: TextStyle(
                        fontSize: 12.0,
                        fontWeight: FontWeight.bold,
                      )),
                  Text('${p.departmentName} \n${p.location}', style: TextStyle(fontSize: 12.0)),
                ],
              ),
              onTap: () {
                print(p.personId);
                if (selectedUsers.isNotEmpty) {
                  selectedUsers = selectedUsers + '  ;  ';
                  selectedPersonid = selectedPersonid + '  ;  ';
                }
                selectedUsers = selectedUsers + p.lastName! + ' , ' + p.firstName!;
                selectedPersonid = selectedPersonid + '${p.personId}';
                setState(() {
                  selectedUsers = selectedUsers;
                  _selectedController.text = selectedUsers;
                });
              },
            );
          },
          separatorBuilder: (BuildContext context, int index) => Divider(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
        );
      } else {
        return Text('No Persons found', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blueGrey, fontSize: 12));
      }
    } else {
      return Center(child: CircularProgressIndicator());
    }
  }

  void _getNames(String searchText) {
    ReservationProvider prov = Provider.of<ReservationProvider>(context, listen: false);
    print('searchText $searchText');
    if (searchText.isNotEmpty) {
      prov.getAssignedToLov(context, searchText).then((_) {
        setState(() {
          filteredNames = prov.assignedToLov;
        });
      });
    } else {
      setState(() {
        print('No search Text ');
        filteredNames = prov.recentAssignedToLov;
      });
    }
  }
}
