import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/models/building.dart';
import 'package:tangoworkplace/models/comments.dart';
import 'package:tangoworkplace/models/floor.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/property.dart';
import 'package:tangoworkplace/models/recent_reservations.dart';
import 'package:tangoworkplace/models/space.dart';
import 'package:tangoworkplace/providers/reservation_provider.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import '../../common/component_utils.dart';

class PropertyScreen extends StatefulWidget {
  static const routName = '/property';
  @override
  _PropertyScreenState createState() => _PropertyScreenState();
}

class _PropertyScreenState extends State<PropertyScreen> {
  final TextEditingController _filter = new TextEditingController();
  String _searchText = "";
  List? filteredNames;
  List<RecentReservations>? propertyLov;
  List<RecentReservations>? locationInfo;
  String? startdate, enddate, d1, d2;
  bool isLoading = false;
  String? selectedAttributes;
  List<LookupValues>? spaceAttributes;
  late var _items;
  var requestId;

  @override
  void initState() {
    _filter.clear();
    super.initState();
  }

  @override
  Future<void> didChangeDependencies() async {
    bool _isInit = true;
    if (_isInit) {
      List? arguments = ModalRoute.of(context)!.settings.arguments as List?;
      if (arguments != null) {
        startdate = arguments[0];
        enddate = arguments[1];
        if (arguments[2] != null) requestId = arguments[2].toString();
        d1 = arguments[3];
        d2 = arguments[4];
      }

      print('Inside property $startdate $enddate');
      ReservationProvider prov = Provider.of<ReservationProvider>(context, listen: false);
      prov.getLookupValues(context, 'SPACE_ATTRIBUTES').then((_) {
        prov.getRecentReservationLoc(context, startdate!, enddate!).then((_) {
          setState(() {
            filteredNames = prov.recentReservationsLov;
            spaceAttributes = prov.spaceAttributesLov;
            selectedAttributes = null;
            _items = spaceAttributes!.map((lookup) => MultiSelectItem<String?>(lookup.lookupCode, lookup.lookupValue!)).toList();
          });
        });
      });
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(
            elevation: 0, centerTitle: true, title: Text("Search Location", style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold))),
        body: Stack(children: <Widget>[
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
                  color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                  shape: BoxShape.rectangle),
              height: 115,
            ),
          ),
          Positioned(
              top: 00,
              right: 15,
              left: 15,
              bottom: 10,
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(9),
                  child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.red,
                      child: Container(
                          color: Colors.white,
                          child: Center(
                              child: Container(
                            child: Column(children: <Widget>[
                              MultiSelectDialogField(
                                items: _items,
                                buttonIcon: Icon(
                                  Icons.arrow_downward,
                                ),
                                buttonText: Text(
                                  "Select Space Attributes",
                                  style: TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                                onConfirm: (results) {
                                  selectedAttributes = results.join(",");
                                  print('selectedAttributes $selectedAttributes');
                                },
                              ),
                              Row(
                                children: [
                                  Flexible(
                                    child: SizedBox(
                                      child: new TextField(
                                          style: TextStyle(fontSize: 12),
                                          controller: _filter,
                                          textInputAction: TextInputAction.search,
                                          onSubmitted: (value) {
                                            _getNames(value);
                                          },
                                          decoration: InputDecoration(
                                            hintStyle: TextStyle(color: Colors.black87, fontSize: 15),
                                            labelText: 'Enter Search Text',
                                            filled: true,
                                            fillColor: Colors.white70,
                                          )),
                                    ),
                                  ),
                                  IconButton(
                                    alignment: Alignment.centerRight,
                                    padding: EdgeInsets.zero,
                                    tooltip: 'Filter',
                                    icon: Icon(Icons.filter_alt),
                                    iconSize: 35,
                                    color: CommonUtils.createMaterialColor(Color(0XFF394251)),
                                    onPressed: () {
                                      showAdvancedFilter(selectedAttributes);
                                    },
                                  )
                                ],
                              ),
                              // Text(_filter.text.isNotEmpty ? 'Suggested Locations' : 'Recent Locations (5)',
                              //     style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                              Text('Location(s)', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                              SizedBox(height: 10),
                              Expanded(child: _buildList()),
                            ]),
                          ))))))
        ]));
  }

  Widget _buildList() {
    print(_searchText);
    print('fil $filteredNames');
    if (filteredNames != null) {
      if (filteredNames!.isNotEmpty) {
        return ListView.separated(
          itemCount: filteredNames != null ? filteredNames!.length : 0,
          itemBuilder: (context, index) {
            RecentReservations p = filteredNames!.elementAt(index);
            return new ListTile(
              trailing: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: () {
                      // CommonUtils.cadviewer(context, '${p.buildingid}', '${p.floorid}', '${p.spaceid}');
                      CommonUtils.reservationCadViewer(context, '${p.buildingid}', '${p.floorid}', '${p.spaceid}', d1 ?? '', d2 ?? '');
                    },
                    child: Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.all(5.0),
                          child: Image(image: AssetImage('lib/icons/plan-icon.png')),
                        )),
                  ),
                  GestureDetector(
                    onTap: () {
                      showPictures(p.spaceid, p.spaceDisplayName);
                    },
                    child: ComponentUtils.apiImage(p.picId, height: 50.0, width: 50.0),
                  ),
                ],
              ),
              contentPadding: EdgeInsets.fromLTRB(10, 0, 10, 0),
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${p.propertyname}',
                      style: TextStyle(
                        fontSize: 12.0,
                        fontWeight: FontWeight.bold,
                      )),
                  Text('${p.buildingname} - ${p.floorname}',
                      style: TextStyle(
                        fontSize: 12.0,
                      )),
                  Text('${p.spaceDisplayName}',
                      style: TextStyle(
                        fontSize: 12.0,
                      ))
                ],
              ),
              subtitle: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (p.noOfReviews != 0) showCommentsList(p.spaceid, '${p.spaceDisplayName}');
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text('${p.noOfReviews ?? 'No'} Reviews',
                          style: TextStyle(
                            decoration: p.noOfReviews != 0 ? TextDecoration.underline : TextDecoration.none,
                            fontSize: 12.0,
                            color: Colors.blueGrey,
                          )),
                    ),
                  ),
                  if (p.spaceAttributes != null)
                    new Row(
                        children: p.spaceAttributes!
                            .map((item) => Padding(
                                  padding: const EdgeInsets.all(5.0),
                                  child: Image(
                                    width: 32,
                                    height: 32,
                                    image: AssetImage('lib/icons/$item.png'),
                                  ),
                                ))
                            .toList())
                ],
              ),
              onTap: () {
                print(p.spaceAttributes);
                Navigator.pop(context, p);
              },
            );
          },
          separatorBuilder: (BuildContext context, int index) => Divider(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
        );
      } else {
        return Text('No Locations found', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blueGrey, fontSize: 12));
      }
    } else {
      return Center(child: CircularProgressIndicator());
    }
  }

  _getNames(String searchText) {
    ReservationProvider prov = Provider.of<ReservationProvider>(context, listen: false);
    if (searchText.isNotEmpty) {
      isLoading = false;
      print('start$startdate end$enddate');

      print(_filter.text);
      print(propertyLov);
      String s = selectedAttributes.toString();
      print('Selected Attributes' + s);
      Provider.of<ReservationProvider>(context, listen: false)
          .getPropertyBuildingFloorLoc(context, startdate, enddate, searchText, selectedAttributes)
          .then((_) {
        setState(() {
          filteredNames = prov.locationsLov;
        });
      });
    } else {
      setState(() {
        filteredNames = prov.recentReservationsLov;
      });
    }
  }

  showCommentsList(int? spaceid, String location) {
    bool _isCommentsLoaded = false;
    final reservProvider = Provider.of<ReservationProvider>(context, listen: false);
    //spaceid = 95369;
    reservProvider.getComments(context, spaceid, 'SPACE').then((_) {
      setState(() {
        _isCommentsLoaded = true;
      });
    });
    showModalBottomSheet(
        // isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: CommonUtils.createMaterialColor(Colors.white),
        context: context,
        builder: (BuildContext context) {
          return GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);

              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: SingleChildScrollView(
              child: ClipRRect(
                  child: Container(
                padding: const EdgeInsets.all(10),
                height: 250,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Reviews for $location', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.blueGrey)),
                    SizedBox(height: 5),
                    Expanded(
                      child: Consumer<ReservationProvider>(
                        builder: (BuildContext context, resvProvider, Widget? child) {
                          return Builder(builder: (context) {
                            if (_isCommentsLoaded) {
                              print('Inside comments build');
                              return MediaQuery.removePadding(
                                removeTop: true,
                                removeBottom: true,
                                context: context,
                                child: ListView.separated(
                                  padding: EdgeInsets.zero,
                                  itemCount: reservProvider.commentsList == null ? 0 : reservProvider.commentsList!.length,
                                  itemBuilder: (context, index) {
                                    Comments c = reservProvider.commentsList![index];
                                    return ListTile(
                                        contentPadding: EdgeInsets.only(top: 0.0, bottom: 0.0, left: 8, right: 8),
                                        key: UniqueKey(),
                                        focusColor: Colors.blueGrey,
                                        title: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                                              Icon(
                                                Icons.person,
                                                size: 25,
                                                color: Colors.grey[400],
                                              ),
                                              SizedBox(width: 5),
                                              SizedBox(
                                                width: 5.0,
                                              ),
                                              Align(
                                                alignment: Alignment.topRight,
                                                child: Text(c.reviewedBy!,
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                    )),
                                              ),
                                            ]),
                                            SizedBox(
                                              height: 5.0,
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: [
                                                Icon(
                                                  Icons.timer_sharp,
                                                  size: 25,
                                                  color: Colors.grey[400],
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Text(c.reviewDate!,
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                    )),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5.0,
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: [
                                                Text(c.comments!,
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                    )),
                                              ],
                                            ),
                                          ],
                                        ));
                                  },
                                  separatorBuilder: (BuildContext context, int index) => Divider(
                                      indent: 5, endIndent: 5, thickness: 2, color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                ),
                              );
                            } else {
                              return ProgressIndicatorCust();
                            }
                          });
                        },
                      ),
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  showPictures(int? spaceid, String? location) {
    int _currentIndex = 0;
    bool _isPicturesLoaded = false;
    final reservProvider = Provider.of<ReservationProvider>(context, listen: false);

    reservProvider.getPictures(context, spaceid, 'SPACE').then((_) {
      setState(() {
        _isPicturesLoaded = true;
      });
    });
    showModalBottomSheet(
        // isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: CommonUtils.createMaterialColor(Colors.white),
        context: context,
        builder: (BuildContext context) {
          return GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);

              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: SingleChildScrollView(
              child: ClipRRect(
                  child: Container(
                padding: const EdgeInsets.all(10),
                height: MediaQuery.of(context).size.height / 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Photos of $location', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.blueGrey)),
                    SizedBox(height: 5),
                    Expanded(
                      child: Consumer<ReservationProvider>(
                        builder: (BuildContext context, resvProvider, Widget? child) {
                          return Builder(builder: (context) {
                            if (_isPicturesLoaded) {
                              print('Inside pictures build');
                              return CarouselSlider(
                                  options: CarouselOptions(
                                      // height: 200.0,
                                      autoPlay: true,
                                      autoPlayInterval: Duration(seconds: 3),
                                      autoPlayAnimationDuration: Duration(milliseconds: 800),
                                      autoPlayCurve: Curves.fastOutSlowIn,
                                      pauseAutoPlayOnTouch: true,
                                      //aspectRatio: 2.0,
                                      onPageChanged: (index, reason) {
                                        setState(() {
                                          _currentIndex = index;
                                        });
                                      }),
                                  items: reservProvider.picturesList!.map((p) {
                                    return Builder(builder: (BuildContext context) {
                                      return Container(
                                        height: MediaQuery.of(context).size.height * 0.30,
                                        width: MediaQuery.of(context).size.width,
                                        child: Card(
                                          color: Colors.white70,
                                          child: ComponentUtils.apiImage(p.picId, height: 200.0, width: 200.0),
                                        ),
                                      );
                                    });
                                  }).toList());
                            } else {
                              return ProgressIndicatorCust();
                            }
                          });
                        },
                      ),
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  showAdvancedFilter(String? pickedAttributes) {
    final formKey = GlobalKey<FormState>();
    Property? _propertyname;
    Building? _buildingname;
    Floor? _floorname;
    Space? _spacename;
    Future<List<Property>> propertyLov;
    List<Building>? buildingLov = [];
    List<Floor>? floorLov = [];
    Future<List<Space>?>? spaceLov;
    final reservProvider = Provider.of<ReservationProvider>(context, listen: false);
    reservProvider.setSpaceLovLoading(false);
    bool _isPropertyLov = false;
    reservProvider.getPropertyLov(context).then((_) {
      setState(() {
        _isPropertyLov = true;
      });
    });

    //propertyLov = reservProvider.getPropertyLov(context);
    showModalBottomSheet(
        // isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: CommonUtils.createMaterialColor(Colors.white),
        context: context,
        builder: (context) {
          //final formKey = GlobalKey<FormState>();
          return GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);

              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: SingleChildScrollView(
              child: ClipRRect(
                  child: Container(
                padding: const EdgeInsets.all(10),
                height: 405,
                child: Form(
                  key: formKey,
                  child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
                    Consumer<ReservationProvider>(builder: (BuildContext context, reservProvider, Widget? child) {
                      print('inside property--------');
                      return Builder(builder: (context) {
                        if (!_isPropertyLov) {
                          return CircularProgressIndicator();
                        } else {
                          return Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
                            new DropdownButtonFormField<Property>(
                              hint: Text(
                                'Select a Property',
                                style: TextStyle(fontSize: 14, color: Colors.black),
                              ),
                              //onSaved: (val) => _propertyname = val,
                              value: _propertyname,
                              items: reservProvider.propertyLov!.map<DropdownMenuItem<Property>>((Property x) {
                                    return DropdownMenuItem<Property>(
                                      value: x,
                                      child: Text(
                                        x.propertyName!,
                                        style: TextStyle(fontSize: 12),
                                      ),
                                    );
                                  }).toList() ??
                                  [],
                              onChanged: (val) {
                                if (val != null)
                                  setState(() {
                                    _floorname = null;
                                    _buildingname = null;
                                    _propertyname = null;
                                    _propertyname = val;
                                    _spacename = null;
                                    buildingLov = null;

                                    buildingLov =
                                        Provider.of<ReservationProvider>(context, listen: false).getBuildingByProperty(val.propertyId);
                                  });
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'Please select a property';
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                  labelStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                  floatingLabelBehavior: FloatingLabelBehavior.always,
                                  labelText: 'Property',
                                  filled: true,
                                  fillColor: Colors.white70,
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                  )),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            Consumer<ReservationProvider>(builder: (BuildContext context, reservProvider, Widget? child) {
                              return (buildingLov == null)
                                  ? CircularProgressIndicator()
                                  : (buildingLov?.isEmpty == true
                                      ? DropdownButtonFormField(
                                          hint: Text('Select a Building', style: TextStyle(fontSize: 14, color: Colors.black)),
                                          value: null,
                                          items: [
                                            DropdownMenuItem<Floor>(
                                              value: null,
                                              child: Text('Select a Building', style: TextStyle(fontSize: 14, color: Colors.black)),
                                            )
                                          ],
                                          validator: (dynamic value) {
                                            print('Validing building val $value');
                                            if (value == null) {
                                              return 'Please select a building';
                                            }
                                            return null;
                                          },
                                          onChanged: (dynamic val) {
                                            if (val != null)
                                              setState(() {
                                                _floorname = null;
                                                floorLov = null;
                                                spaceLov = null;
                                                _buildingname = null;
                                                _buildingname = val;
                                                _spacename = null;

                                                floorLov = Provider.of<ReservationProvider>(context, listen: false)
                                                    .getFloorByBuilding(val.buildingId);
                                              });
                                          },
                                          decoration: InputDecoration(
                                              labelStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                              floatingLabelBehavior: FloatingLabelBehavior.always,
                                              labelText: 'Building',
                                              filled: true,
                                              fillColor: Colors.white70,
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                              )))
                                      : DropdownButtonFormField(
                                          hint: Text(
                                            'Select a building',
                                            style: TextStyle(fontSize: 14, color: Colors.black),
                                          ),
                                          //  onSaved: (val) => _buildingname = val,
                                          value: _buildingname,
                                          items: buildingLov!.map<DropdownMenuItem<Building>>((Building dropDownStringItem) {
                                                print('Generating Building $dropDownStringItem');
                                                return DropdownMenuItem<Building>(
                                                  value: dropDownStringItem,
                                                  child: Text(dropDownStringItem.buildingName!,
                                                      style: TextStyle(fontSize: 14, color: Colors.black)),
                                                );
                                              }).toList() ??
                                              [],
                                          onChanged: (dynamic val) {
                                            print('Inside building val');
                                            if (val != null)
                                              setState(() {
                                                _floorname = null;
                                                floorLov = null;
                                                _buildingname = null;
                                                _buildingname = val;
                                                _spacename = null;

                                                floorLov = Provider.of<ReservationProvider>(context, listen: false)
                                                    .getFloorByBuilding(val.buildingId);

                                                print('Fetched $floorLov');
                                                spaceLov = Provider.of<ReservationProvider>(context, listen: false).getSpace(
                                                    context, _buildingname!.buildingId, null, startdate, enddate, pickedAttributes,
                                                    requestId: requestId);
                                              });
                                          },
                                          validator: (dynamic value) {
                                            if (value == null) {
                                              return 'Please select a building';
                                            }
                                            return null;
                                          },

                                          decoration: InputDecoration(
                                              floatingLabelBehavior: FloatingLabelBehavior.always,
                                              labelStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                              labelText: 'Building',
                                              filled: true,
                                              fillColor: Colors.white70,
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                              )),
                                        ));
                            }),
                            SizedBox(height: 5),
                            Consumer<ReservationProvider>(builder: (BuildContext context, reservProvider, Widget? child) {
                              return (floorLov == null)
                                  ? CircularProgressIndicator()
                                  : (floorLov?.isEmpty == true
                                      ? DropdownButtonFormField(
                                          hint: Text('Select a Floor', style: TextStyle(fontSize: 14, color: Colors.black)),
                                          value: null,
                                          items: [
                                            DropdownMenuItem<Floor>(
                                              value: null,
                                              child: Text('Select a Floor', style: TextStyle(fontSize: 14, color: Colors.black)),
                                            )
                                          ],
                                          // validator: (value) {
                                          //   print('Validing floor val $value');
                                          //   if (value == null) {
                                          //     return 'Please select a floor';
                                          //   }
                                          //   return null;
                                          // },
                                          onChanged: (dynamic val) {
                                            if (val != null)
                                              setState(() {
                                                print('Floor value $val');
                                                _floorname = val;
                                                _spacename = null;
                                                print('space attributes $pickedAttributes');
                                                spaceLov = Provider.of<ReservationProvider>(context, listen: false).getSpace(context,
                                                    _buildingname!.buildingId, _floorname!.floorId, startdate, enddate, pickedAttributes,
                                                    requestId: requestId);
                                              });
                                          },
                                          decoration: InputDecoration(
                                              labelStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                              floatingLabelBehavior: FloatingLabelBehavior.always,
                                              labelText: 'Floor',
                                              filled: true,
                                              fillColor: Colors.white70,
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                              )))
                                      : Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Flexible(
                                              child: DropdownButtonFormField(
                                                iconEnabledColor: Colors.black,
                                                hint: Text('Select a Floor', style: TextStyle(fontSize: 14, color: Colors.black)),
                                                //onSaved: (val) => _floorname = val,
                                                value: _floorname,
                                                items: floorLov!.map<DropdownMenuItem<Floor>>((Floor dropDownStringItem) {
                                                      print('Generating Floor $dropDownStringItem');
                                                      return DropdownMenuItem<Floor>(
                                                        value: dropDownStringItem,
                                                        child: Text(dropDownStringItem.floorName!, style: TextStyle(fontSize: 12)),
                                                      );
                                                    }).toList() ??
                                                    [],
                                                onChanged: (dynamic val) {
                                                  if (val != null)
                                                    setState(() {
                                                      print('Floor value $val');
                                                      _floorname = val;
                                                      _spacename = null;
                                                      print('space attributes $pickedAttributes');
                                                      spaceLov = Provider.of<ReservationProvider>(context, listen: false).getSpace(
                                                          context,
                                                          _buildingname!.buildingId,
                                                          _floorname!.floorId,
                                                          startdate,
                                                          enddate,
                                                          pickedAttributes,
                                                          requestId: requestId);
                                                    });
                                                },
                                                // validator: (value) {
                                                //   if (value == null) {
                                                //     return 'Please select a floor';
                                                //   }
                                                //   return null;
                                                // },
                                                decoration: InputDecoration(
                                                    floatingLabelBehavior: FloatingLabelBehavior.always,
                                                    labelStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                                    labelText: 'Floor',
                                                    filled: true,
                                                    fillColor: Colors.white70,
                                                    focusedBorder: OutlineInputBorder(
                                                      borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                                      borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                                    )),
                                              ),
                                            ),
                                            if (_floorname != null)
                                              GestureDetector(
                                                onTap: () {
                                                  // CommonUtils.cadviewer(
                                                  //     context, _buildingname!.buildingId.toString(), _floorname!.floorId.toString(), "");
                                                  CommonUtils.reservationCadViewer(context, _buildingname!.buildingId.toString(),
                                                      _floorname!.floorId.toString(), "", d1 ?? '', d2 ?? '');
                                                },
                                                child: Align(
                                                    alignment: Alignment.centerRight,
                                                    child: Padding(
                                                      padding: const EdgeInsets.all(5.0),
                                                      child: Image(image: AssetImage('lib/icons/plan-icon.png')),
                                                    )),
                                              ),
                                          ],
                                        ));
                            }),
                            Consumer<ReservationProvider>(builder: (BuildContext context, reservProvider, Widget? child) {
                              print('inside spaces--------');

                              return Builder(builder: (context) {
                                if (reservProvider.isspaceLovLoading) {
                                  return CircularProgressIndicator();
                                } else if (reservProvider.spaceLov == null || reservProvider.spaceLov?.isEmpty == true) {
                                  return DropdownButtonFormField(
                                      hint: Text('Select a Space', style: TextStyle(fontSize: 14, color: Colors.black)),
                                      value: null,
                                      items: [
                                        DropdownMenuItem<Space>(
                                          value: null,
                                          child: Text('Select a space', style: TextStyle(fontSize: 14, color: Colors.black)),
                                        )
                                      ],
                                      onChanged: (dynamic val) {
                                        if (val != null)
                                          setState(() {
                                            _spacename = val;
                                          });
                                      },
                                      validator: (dynamic value) {
                                        print('Validing space val $value');
                                        if (value == null) {
                                          return 'Please select a space';
                                        }
                                        return null;
                                      },
                                      decoration: InputDecoration(
                                          labelStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                          floatingLabelBehavior: FloatingLabelBehavior.always,
                                          labelText: 'Space',
                                          filled: true,
                                          fillColor: Colors.white70,
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                            borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                          )));
                                } else {
                                  return DropdownButtonFormField(
                                    hint: Text('Select a Space', style: TextStyle(fontSize: 14)),
                                    //onSaved: (val) => _spacename = val,
                                    value: _spacename,
                                    items: reservProvider.spaceLov!.map<DropdownMenuItem<Space>>((Space x) {
                                      //  print(' ${x.spaceId}');
                                      return DropdownMenuItem<Space>(
                                        value: x,
                                        child: Row(
                                          children: [
                                            Text('${x.spaceDisplayName}', style: TextStyle(fontSize: 12)),
                                            if (x.spaceAttributes != null)
                                              Container(
                                                child: new Row(
                                                    mainAxisAlignment: MainAxisAlignment.start,
                                                    children: x.spaceAttributes!
                                                            .map((item) => Image(
                                                                  width: 32,
                                                                  height: 32,
                                                                  image: AssetImage('lib/icons/$item.png'),
                                                                ))
                                                            .toList() ??
                                                        []),
                                              ),
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (dynamic val) {
                                      if (val != null)
                                        setState(() {
                                          _spacename = val;
                                        });
                                    },
                                    validator: (dynamic value) {
                                      print('Validing space va; $value');
                                      if (value == null) {
                                        return 'Please select a space';
                                      }
                                      return null;
                                    },
                                    decoration: InputDecoration(
                                        floatingLabelBehavior: FloatingLabelBehavior.always,
                                        labelStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                        hintStyle: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                        labelText: 'Space',
                                        filled: true,
                                        fillColor: Colors.white70,
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.all(Radius.circular(10.0)),
                                          borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                        )),
                                  );
                                }
                              });
                            }),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 5.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Align(
                                      alignment: Alignment.bottomRight,
                                      child: ElevatedButton(
                                          style: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                            (Set<MaterialState> states) {
                                              if (states.contains(MaterialState.pressed))
                                                return CommonUtils.createMaterialColor(Colors.redAccent);
                                              return CommonUtils.createMaterialColor(Color(0XFFb10c00));
                                              // Use the component's default.
                                            },
                                          )),
                                          onPressed: () {
                                            final isValid = formKey.currentState!.validate();
                                            if (!isValid) {
                                              return;
                                            }

                                            formKey.currentState!.save();
                                            print(_propertyname);

                                            RecentReservations p = RecentReservations(
                                                propertyid: _propertyname!.propertyId,
                                                buildingid: _buildingname!.buildingId,
                                                floorid: _floorname != null ? _floorname!.floorId : _spacename!.floorId,
                                                spaceid: _spacename!.spaceId,
                                                spaceDisplayName: _spacename!.spaceDisplayName,
                                                propertyname: _propertyname!.propertyName,
                                                buildingname: _buildingname!.buildingName,
                                                floorname: _floorname != null ? _floorname!.floorName : _spacename!.floorName,
                                                spaceAttributes: _spacename!.spaceAttributes);
                                            Navigator.pop(context, p);
                                            Navigator.pop(context, p);
                                          },
                                          child: Text(
                                            'OK',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ))
                                      // : SizedBox(width: 10),
                                      ),
                                  SizedBox(width: 10),
                                  Align(
                                    alignment: Alignment.bottomRight,
                                    child: ElevatedButton(
                                        style: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                          (Set<MaterialState> states) {
                                            if (states.contains(MaterialState.pressed))
                                              return CommonUtils.createMaterialColor(Colors.redAccent);
                                            return CommonUtils.createMaterialColor(Color(0XFFb10c00)); // Use the component's default.
                                          },
                                        )),
                                        onPressed: () {
                                          Navigator.pop(context);
                                        },
                                        child: Text(
                                          'Cancel',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        )),
                                  )
                                ],
                              ),
                            )
                          ]);
                        }
                      });
                    }),
                  ]),
                ),
              )),
            ),
          );
        });
  }
}
