import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../common/component_utils.dart';
import '../../../models/spacemgmt/property/propertyview_data.dart';
import '../../../providers/spacemanagement/property/propertysearch_controller.dart';

class PropertySearch extends StatelessWidget {
  String? source;
  PropertySearch({Key? key, this.source}) : super(key: key);
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  PropertySearchController propertySearchState = Get.put(PropertySearchController());

  PropertySearchController propertyCtrlr = Get.find<PropertySearchController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshBuildings() async {
    await propertyCtrlr.getPropertySearchData(searchText: propertyCtrlr.searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_PROPERTIES')!.value!, style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            // Get.off(() => HomeScreen());
            Get.back();
          },
        ),
      ),
      body: Column(
        children: <Widget>[
          TaSearchInputText(
              makeSearch: (searchtext) async {
                await propertyCtrlr.getPropertySearchData();
              },
              searchController: propertyCtrlr.searchController,
              hintSearch: 'Search '),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshBuildings,
              child: GetX<PropertySearchController>(
                initState: (state) async {
                  propertyCtrlr.isLoading(true);
                  await talabel.getlabels('TMCMOBILE_PROPERTYSEARCH', 'Property_search', filtertype: 'tab');
                  propertyCtrlr.getPropertySearchData(action: 'onload');
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.propertylist.isEmpty
                          ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.propertylist[index]);
                              },
                              itemCount: _.propertylist.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, PropertyViewData p) {
    final primary = Color(0xff696b9e);
    final secondary = Color(0xfff29a94);

    return GestureDetector(
      onTap: () async {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    p.storeName ?? '',
                    style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  const SizedBox(
                    height: 6,
                  ),
                  if (talabel.get('TMCMOBILE_PROPERTYSEARCH_STOREBNUM') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_PROPERTYSEARCH_STOREBNUM')?.value ?? 'Store Number:', p.storeNumber?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_PROPERTYSEARCH_STATUS') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_PROPERTYSEARCH_STATUS')?.value ?? 'Status:', p.status?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_PROPERTYSEARCH_SHOPPINGCENTER') != null)
                    ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROPERTYSEARCH_SHOPPINGCENTER')?.value ?? 'Shopping Center:',
                        p.shoppingCenter?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_PROPERTYSEARCH_LANDLORD') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_PROPERTYSEARCH_LANDLORD')?.value ?? 'Landlord:', p.landlord?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_PROPERTYSEARCH_LOCTYPE') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_PROPERTYSEARCH_LOCTYPE')?.value ?? 'Location Type:', p.locTypeCodeDesc?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_PROPERTYSEARCH_OWNERSHIPTYPE') != null)
                    ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROPERTYSEARCH_OWNERSHIPTYPE')?.value ?? 'Ownership Type:',
                        p.ownershipTypeDesc?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_PROPERTYSEARCH_FACILITYTYPE') != null)
                    ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROPERTYSEARCH_FACILITYTYPE')?.value ?? 'Facility Type:',
                        p.facilityTypeDesc?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  const SizedBox(
                    height: 6,
                  ),
                  Row(
                    children: <Widget>[
                      Icon(
                        Icons.location_on,
                        color: secondary,
                        size: 15,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Expanded(
                        child: Text('${p.address ?? ''}' + ' ' + '${p.city ?? ''}' + ' ' + '${p.state ?? ' '}',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: primary,
                              fontSize: 12,
                              letterSpacing: .1,
                            )),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
