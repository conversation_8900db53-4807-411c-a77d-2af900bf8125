import 'dart:async';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import 'login_screen.dart';

class TimerExpired extends StatefulWidget {
  static const routName = '/timerexpired';
  TimerExpired();
  @override
  _TimerExpiredState createState() => _TimerExpiredState();
}

class _TimerExpiredState extends State<TimerExpired> {
  final _key = UniqueKey();
  Timer? _timer;

  startTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }

    DateTime _expiryDate = DateTime.now().add(
      Duration(seconds: 120),
    );

    final timeToExpiry = _expiryDate.difference(DateTime.now()).inSeconds;
    SharedPrefUtils.saveStr("ExpiryDate", _expiryDate.toString());

    debugPrint(' timeto Expiry $timeToExpiry');

    return Timer(Duration(seconds: timeToExpiry), reroute);
  }

  @override
  void initState() {
    debugPrint("Inside Start Timer Init");
    super.initState();
    _timer = startTimer();
  }

  reroute() {
    Navigator.of(context).pushNamed(LoginPage.routName);
    SharedPrefUtils.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
