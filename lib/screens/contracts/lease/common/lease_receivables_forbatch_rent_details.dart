import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../common/component_utils.dart';
import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/component_widgets/tatable.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/contracts/lease/LeaseBatchRentPaysummeryData.dart';
import '../../../../models/contracts/lease/leasereceivablesforbatchrentdata.dart';
import '../../../../providers/contracts/lease/lease_batch_rentpay_summery_controller.dart';
import '../../../../providers/contracts/lease/lease_receivables_forbatch_rent_controller.dart';
import '../../../../providers/ta_admin/label_controller.dart';
import '../../../../utils/device_util.dart';

class LeaseReceivablesforBatchRentDetails extends StatelessWidget {
  int? batchid;
  LeaseReceivablesforBatchRentDetails({Key? key, this.batchid}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  LeaseReceivablesforBatchRentController lbprsCtrl = Get.find<LeaseReceivablesforBatchRentController>();

  Future _refreshStatusReports() async {
    await lbprsCtrl.loadReceivablesforBatch(batchid);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshStatusReports,
            child: GetX<LeaseReceivablesforBatchRentController>(
              initState: (state) {
                lbprsCtrl.loadReceivablesforBatch(batchid);
              },
              builder: (_) {
                return _.isLoading.isTrue
                    ? ProgressIndicatorCust()
                    : _.lbrpsList.length < 1
                        ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return listitem(context, _.lbrpsList[index]);
                            },
                            itemCount: _.lbrpsList.length,
                          );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget listitem(BuildContext context, LeaseReceivablesforBatchRentData lp) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return SingleChildScrollView(
      child: GestureDetector(
        // onTap: () {

        // },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
          padding: EdgeInsets.only(right: 10, left: 10, top: 5),
          child: Column(
            children: [
              //ComponentUtils.listVertRow('Lease Id', lp.leaseId.toString()),
              ComponentUtils.listVertRow('Lease ', lp.leaseNumber.toString() ?? ''),
              ComponentUtils.listVertRow('Effective Date', lp.effectiveDate?.toString() ?? ''),
              ComponentUtils.listVertRow('Applies From', lp.appliesFromDate?.toString() ?? ''),
              ComponentUtils.listVertRow('Applies To', lp.appliesToDate?.toString() ?? ''),
              ComponentUtils.listVertRow('Payment Category', lp.paymentCategory?.toString() ?? ''),
              ComponentUtils.listVertRow('Net Amount', lp.netAmount?.toString() ?? '0.0', dtype: 'num'),
              ComponentUtils.listVertRow('Previous Net Amount', lp.prevNet?.toString() ?? '0.0', dtype: 'num'),
              ComponentUtils.listVertRow('Tax Applies', lp.taxApplies ?? ''),
            ],
          ),
        ),
      ),
    );
  }
}
