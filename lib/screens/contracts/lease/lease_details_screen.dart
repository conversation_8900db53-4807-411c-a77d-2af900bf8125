import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/location/locationentity/locationentity.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/common/documents_controller.dart';
import 'package:tangoworkplace/providers/common/entitycomments_controller.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/documents/documents.dart';
import 'package:tangoworkplace/screens/common/comments/entity_comments.dart';
import 'package:tangoworkplace/screens/common/photos/photos.dart';
import 'package:tangoworkplace/screens/locationmangement/locationentity/tabs/location_general.dart';
import '../../../common/component_utils.dart';
import '../../../providers/common/entitydemographics_controller.dart';
import '../../../providers/common/photosgrid_controller.dart';
import '../../../providers/contracts/lease/leasedetails_controller.dart';
import '../../../providers/locationmanagement/locationentity/locationgeneral_controller.dart';
import '../../common/demographic/entitydemographics.dart';
import '../../common/photos/photosgrid.dart';

class LeaseDetails extends StatelessWidget {
  LocationEntity? locEntity;
  final nav_from;
  int? entityid;
  String? entityname;
  LeaseDetails({this.locEntity, this.nav_from, this.entityid, this.entityname});

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final locEntitytabs = <Tab>[];
  Applabel? label;
  LabelController talabel = Get.find<LabelController>();

  LeaseDetailsController detCtrl = Get.put(LeaseDetailsController());

  var generaltab;
  var demographicstab;
  var photostab;
  var docstab;
  var commentstab;

  List<Tab> tabsdata = [];

  String? _setTablabels(Applabel? label) {
    if (label != null) {
      locEntitytabs.add(Tab(
        text: label.value,
      ));
      return label.value;
    }
  }

  Future _getlabels(LabelController labCtrl) async {
    try {
      await labCtrl.getlabels('TMCMOBILE_LOCATIONS', 'locations_details', filtertype: 'page');

      generaltab = _setTablabels(labCtrl.get('TMCMOBILE_LOCATIONS_GENERAL'));
      //demographicstab = _setTablabels(labCtrl.get('TMCMOBILE_LOCATIONS_DEMOGRAPHICS'));
      //photostab = _setTablabels(labCtrl.get('TMCMOBILE_LOCATIONS_PHOTOS'));
      // docstab = _setTablabels(labCtrl.get('TMCMOBILE_LOCATIONS_DOCUMENTS'));
      //commentstab = _setTablabels(labCtrl.get('TMCMOBILE_LOCATIONS_COMMENTS'));
    } catch (e) {
      debugPrint('$e');
    }
    labCtrl.setdataloading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    //debugPrint('sitename-------${entityname}');
    talabel.setdataloading.value = true;

    LocationGeneralController locationGeneralController = Get.put(LocationGeneralController());
    EntityCommentsController entityCommentsController = Get.put(EntityCommentsController());
    PhotosGridController photosController = Get.put(PhotosGridController());
    DocumentController documentController = Get.put(DocumentController());
    EntityDemographicsController demographicController = Get.put(EntityDemographicsController());

    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(entityname ?? '', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<LabelController>(initState: (state) {
        Future.delayed(Duration.zero, () async {
          try {
            detCtrl.isloading.value = true;
            await _getlabels(talabel);
            if (nav_from == 'locate') {
              await detCtrl.getlocEntityRecord(entityid?.toString());

              locEntity = detCtrl.locEntityrec.value;
            }
          } finally {
            detCtrl.isloading.value = false;
          }
        });
      }, builder: (_) {
        return (talabel.setdataloading.value || detCtrl.isloading.value)
            ? const ProgressIndicatorCust()
            : DefaultTabController(
                initialIndex: 0,
                length: (locEntitytabs != null && locEntitytabs.isNotEmpty) ? locEntitytabs.length : 1,
                child: Column(children: <Widget>[
                  Material(
                    elevation: 5,
                    color: Colors.white,
                    child: TabBar(
                      //controller: _tabController,
                      isScrollable: locEntitytabs.length > 3,
                      labelColor: ComponentUtils.tablabelcolor,
                      unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                      indicatorColor: ComponentUtils.tabindicatorColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: TextStyle(
                          fontWeight: FontWeight.bold, fontStyle: FontStyle.normal, fontSize: 14, color: ComponentUtils.tablabelcolor),
                      tabs: (locEntitytabs != null && locEntitytabs.isNotEmpty)
                          ? locEntitytabs
                          : Tab(
                              text: 'General',
                            ) as List<Widget>,
                    ),
                  ),
                  Expanded(
                    child: (locEntitytabs == null || locEntitytabs.isEmpty)
                        ? TabBarView(children: [
                            LocationGeneral(
                              loc: locEntity,
                            )
                          ])
                        : TabBarView(
                            children: [
                              if (generaltab != null)
                                LocationGeneral(
                                  loc: locEntity,
                                ),
                              if (demographicstab != null)
                                EntityDemographics(
                                  entityType: 'STORE',
                                  entityId: locEntity!.storeId,
                                ),
                              if (photostab != null)
                                PhotosGrid(
                                  //Photos(
                                  entityType: 'STORE',
                                  entityId: locEntity!.storeId,
                                  //photoMode: 'v',
                                  mode: 'edit',
                                ),
                              if (docstab != null)
                                Documents(
                                  rootfolderid: locEntity!.rootFolderId,
                                  entityType: 'STORE',
                                  entityId: locEntity!.storeId,
                                ),
                              if (commentstab != null)
                                EntityComments(
                                  entityType: 'STORE',
                                  entityId: locEntity!.storeId,
                                ),
                            ],
                          ),
                  ),
                ]),
              );
      }),
    );
  }
}
