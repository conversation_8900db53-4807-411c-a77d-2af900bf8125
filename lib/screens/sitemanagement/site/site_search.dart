import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/sitemanagement/site/site_details.dart';

import '../../../common/component_utils.dart';
import '../../../models/sitemanagement/site/siteview.dart';
import '../../../providers/sitemanagement/site/site_controller.dart';
import '../../common/utils/entityerrorpg.dart';
import 'search/site_filter_widget.dart';

class SiteSearch extends StatelessWidget {
  String? source;
  SiteSearch({Key? key, this.source}) : super(key: key);
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  SiteSearchController siteSearchState = Get.put(SiteSearchController());

  SiteSearchController siteSearchCtrlr = Get.find<SiteSearchController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshSites() async {
    await siteSearchCtrlr.getSiteSearchData(searchText: siteSearchCtrlr.searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_SITES')!.value!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        actions: [
          IconButton(
              onPressed: () async {
                Get.to(() => SiteFilterWidget());
                siteSearchCtrlr.filterwidgetloading.value = true;
                await talabel.getlabels('TMCMOBILE_SITESEARCH', 'Site_search', filtertype: 'tab');
                Future.delayed(const Duration(seconds: 1), () async {
                  siteSearchCtrlr.filterwidgetloading.value = false;
                });
              },
              icon: Icon(
                Icons.search,
                color: ComponentUtils.primecolor,
              )),
          // Obx(
          //   () => talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION') != null
          //       ? IconButton(
          //           onPressed: () {
          //             navigationBottomSheet(context);
          //           },
          //           icon: Icon(
          //             Icons.apps,
          //             color: ComponentUtils.primecolor,
          //           ))
          //       : Container(),
          // ),
        ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            // Get.off(() => HomeScreen());
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          filterChipContainer(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshSites,
              child: GetX<SiteSearchController>(
                initState: (state) async {
                  siteSearchCtrlr.isLoading(true);
                  await talabel.getlabels('TMCMOBILE_SITESEARCH', 'Site_search', filtertype: 'tab');
                  siteSearchCtrlr.getSiteSearchData(action: 'onload');
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.sitelist.isEmpty
                          ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.sitelist[index]);
                              },
                              itemCount: _.sitelist.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterChipContainer() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.only(left: 12, right: 10, top: 5, bottom: 2),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Wrap(
                alignment: WrapAlignment.start,
                spacing: 6.0,
                runSpacing: 6.0,
                children: List<Widget>.generate(siteSearchCtrlr.filterList.length, (int index) {
                  return siteSearchCtrlr.filterList[index];
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, SiteView s) {
    final primary = Color(0xff696b9e);
    final secondary = Color(0xfff29a94);

    return GestureDetector(
        onTap: () async {
          String mode =
              await siteSearchCtrlr.getUserEntityEditAccess(entityid: s.siteId.toString(), entitytype: 'SITE', roFlag: s.readOnlyFlag);

          if (mode == 'error') {
            Get.to(
              () => EntityErrorPg(
                entitytype: 'Site',
                entityid: s.siteId,
                entityname: s.siteName,
              ),
            );
          } else {
            Get.to(() => SiteDetails(
                  site: s,
                  nav_from: 'search',
                  siteid: s.siteId,
                  sitename: s.siteName,
                  tabroles: siteSearchCtrlr.tabroles.value,
                  parent_mode: mode,
                ));
          }
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${s?.siteName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.shield,
                            color: secondary,
                            size: 15,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Text(s.siteNumber?.toString() ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        // Row(
                        //   children: <Widget>[
                        //     Icon(
                        //       Icons.bolt,
                        //       color: secondary,
                        //       size: 15,
                        //     ),
                        //     SizedBox(
                        //       width: 5,
                        //     ),
                        Text(s.statusDesc ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        //   ],
                        // ),
                      ],
                    ),
                    // SizedBox(
                    //   height: 6,
                    // ),
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   children: <Widget>[
                    //     Row(children: [
                    //       Icon(
                    //         Icons.assistant_photo,
                    //         color: secondary,
                    //         size: 15,
                    //       ),
                    //       SizedBox(
                    //         width: 5,
                    //       ),
                    //       Text(s.entityType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                    //     ]),
                    //     Text(p.ownershipType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                    //   ],
                    // ),
                    SizedBox(
                      height: 6,
                    ),
                    Row(
                      children: <Widget>[
                        Icon(
                          Icons.location_on,
                          color: secondary,
                          size: 15,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Expanded(
                          child: Text('${s.address ?? ''}' + ' ' + '${s.city ?? ''}' + ' ' + '${s.state ?? ' '}',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: primary,
                                fontSize: 12,
                                letterSpacing: .1,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
