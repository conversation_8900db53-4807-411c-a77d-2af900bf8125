import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/sitemanagement/target/targetbvo.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/widgets/component_widgets/taforminputtext.dart';
import '../../../../models/sitemanagement/target/targetview.dart';
import '../../../../providers/sitemanagement/target/target_attributes_controller.dart';
import '../../../../providers/sitemanagement/target/target_general_controller.dart';

class TargetAttributes extends GetView<TargetAttributesController> {
  Targetbvo target;
  String? mode;

  TargetAttributes({Key? key, required this.target, this.mode}) : super(key: key);

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  TargetAttributesController attrCtrl = Get.find<TargetAttributesController>();
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    attrCtrl.labelsloading.value = true;

    return Stack(alignment: AlignmentDirectional.bottomCenter, children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            try {
              attrCtrl.isloading.value = true;
              Future.delayed(Duration.zero, () async {
                await attrCtrl.getlabels(talabel);
                attrCtrl.tragetrec.value = target;
              });
            } finally {
              attrCtrl.isloading.value = false;
            }
          },
          builder: (_) {
            return (attrCtrl.labelsloading.value)
                ? const ProgressIndicatorCust()
                : Form(
                    key: _formKey,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    child: attributesTab(target),
                  );
          },
        ),
      ),
      if (mode == 'edit') bottombuttonbar(_formKey),
    ]);
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            const SizedBox(
              height: 5.0,
              width: 5.0,
            ),
            if (mode == 'edit')
              TaButton(
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                type: 'elevate',
                onPressed: () {
                  attrCtrl.onSaveAttributes(target);
                },
              ),
            const SizedBox(
              width: 5.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget attributesTab(Targetbvo? t) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST')!.ro : true,
              value: attrCtrl.tragetrec.value.salesForecast?.toString() ?? '',
              onChanged: (val) {
                attrCtrl.tragetrec.value.salesForecast = double.tryParse(val);
              },
              onSaved: (val) {
                attrCtrl.tragetrec.value.salesForecast = double.tryParse(val);
              },
            ),
          if (talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST2') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST2')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST2')!.ro : true,
              value: attrCtrl.tragetrec.value.salesForecast2?.toString() ?? '',
              onChanged: (val) {
                attrCtrl.tragetrec.value.salesForecast2 = double.tryParse(val);
              },
              onSaved: (val) {
                attrCtrl.tragetrec.value.salesForecast2 = double.tryParse(val);
              },
            ),
          if (talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST3') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST3')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_TARGET_ATTRIBUTES_SALESFORECAST3')!.ro : true,
              value: attrCtrl.tragetrec.value.salesForecast3?.toString() ?? '',
              onChanged: (val) {
                attrCtrl.tragetrec.value.salesForecast3 = double.tryParse(val);
              },
              onSaved: (val) {
                attrCtrl.tragetrec.value.salesForecast3 = double.tryParse(val);
              },
            ),
        ],
      ),
    );
  }
}
