import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../../common/widgets/component_widgets/taforminputtext.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/lookup_values.dart';
import '../../../../models/sitemanagement/site/siteview.dart';
import '../../../../models/sitemanagement/target/targetbvo.dart';
import '../../../../models/sitemanagement/target/targetview.dart';
import '../../../../providers/sitemanagement/site/site_general_controller.dart';
import '../../../../providers/sitemanagement/target/target_fv_controller.dart';

class TargetFieldValidation extends StatelessWidget {
  final Targetbvo? trgt;
  final mode;
  TargetFieldValidation({Key? key, this.trgt, this.mode}) : super(key: key);

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  TargetFvController fvCtrl = Get.find<TargetFvController>();

  @override
  Widget build(BuildContext context) {
    fvCtrl.labelsloading.value = true;

    return Stack(alignment: AlignmentDirectional.bottomCenter, children: <Widget>[
      Positioned.fill(
        bottom: 50,
        child: GetX<TargetFvController>(
          initState: (state) async {
            fvCtrl.isDataLoading.value = true;
            Future.delayed(Duration.zero, () async {
              await fvCtrl.getlabels(talabel);
              await fvCtrl.getFieldValidData(trgt!.targetId);
            });
          },
          builder: (_) {
            return (fvCtrl.labelsloading.value || fvCtrl.isDataLoading.value) ? ProgressIndicatorCust() : fvform();
          },
        ),
      ),
      if (mode == 'edit')
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              children: [
                const SizedBox(
                  width: 15,
                ),
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                  onPressed: () async {
                    debugPrint('Save----------');
                    await fvCtrl.onSaveFieldValidation();
                  },
                ),
                const SizedBox(
                  width: 20,
                ),
              ],
            ),
          ],
        ),
    ]);
  }

  Widget fvform() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPHASING') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPHASING')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPHASING')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPHASING')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.devPhasing = newValue;
                    },
              value: (fvCtrl.fvdata.value.devPhasing == '' || fvCtrl.fvdata.value.devPhasing == 'null')
                  ? null
                  : fvCtrl.fvdata.value.devPhasing,
              listflag: (fvCtrl.devphasinglov.value.isNotEmpty && fvCtrl.devphasinglov.value != null),
              items: fvCtrl.devphasinglov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPRIORITY') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPRIORITY')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPRIORITY')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVPRIORITY')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.devPriority = newValue;
                    },
              value: (fvCtrl.fvdata.value.devPriority == '' || fvCtrl.fvdata.value.devPriority == 'null')
                  ? null
                  : fvCtrl.fvdata.value.devPriority,
              listflag: (fvCtrl.devprioritylov.value.isNotEmpty && fvCtrl.devprioritylov.value != null),
              items: fvCtrl.devprioritylov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_RECOSTINTA') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_RECOSTINTA')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_RECOSTINTA')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_RECOSTINTA')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.reCostInTa = newValue;
                    },
              value: (fvCtrl.fvdata.value.reCostInTa == '' || fvCtrl.fvdata.value.reCostInTa == 'null')
                  ? null
                  : fvCtrl.fvdata.value.reCostInTa,
              listflag: (fvCtrl.recostintalov.value.isNotEmpty && fvCtrl.recostintalov.value != null),
              items: fvCtrl.recostintalov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DIFFICULTYENTRY') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DIFFICULTYENTRY')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DIFFICULTYENTRY')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DIFFICULTYENTRY')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.difficultyEntry = newValue;
                    },
              value: (fvCtrl.fvdata.value.difficultyEntry == '' || fvCtrl.fvdata.value.difficultyEntry == 'null')
                  ? null
                  : fvCtrl.fvdata.value.difficultyEntry,
              listflag: (fvCtrl.devprioritylov.value.isNotEmpty && fvCtrl.difficultyentrylov.value != null),
              items: fvCtrl.difficultyentrylov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_ABILITYIMAGE') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_ABILITYIMAGE')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_ABILITYIMAGE')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_ABILITYIMAGE')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.abilityImage = newValue;
                    },
              value: (fvCtrl.fvdata.value.abilityImage == '' || fvCtrl.fvdata.value.abilityImage == 'null')
                  ? null
                  : fvCtrl.fvdata.value.abilityImage,
              listflag: (fvCtrl.abilityimagelov.value.isNotEmpty && fvCtrl.abilityimagelov.value != null),
              items: fvCtrl.abilityimagelov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_TRAFFICRATING') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_TRAFFICRATING')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_TRAFFICRATING')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_TRAFFICRATING')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.trafficRating = newValue;
                    },
              value: (fvCtrl.fvdata.value.trafficRating == '' || fvCtrl.fvdata.value.trafficRating == 'null')
                  ? null
                  : fvCtrl.fvdata.value.trafficRating,
              listflag: (fvCtrl.trafficratinglov.value.isNotEmpty && fvCtrl.trafficratinglov.value != null),
              items: fvCtrl.trafficratinglov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVTARGETYEAR') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVTARGETYEAR')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVTARGETYEAR')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_DEVTARGETYEAR')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.devTargetYear = newValue;
                    },
              value: (fvCtrl.fvdata.value.devTargetYear == '' || fvCtrl.fvdata.value.devTargetYear == 'null')
                  ? null
                  : fvCtrl.fvdata.value.devTargetYear,
              listflag: (fvCtrl.devtargetyearlov.value.isNotEmpty && fvCtrl.devtargetyearlov.value != null),
              items: fvCtrl.devtargetyearlov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_CEXTLOV21') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_CEXTLOV21')!.value,
              emptttext: 'Select',
              readonly: mode == 'edit' ? (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_CEXTLOV21')!.ro) : true,
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_CEXTLOV21')!.ro!)
                  ? null
                  : (newValue) async {
                      debugPrint('newValue          ' + newValue);
                      fvCtrl.fvdata.value.cextLov21 = newValue;
                    },
              value:
                  (fvCtrl.fvdata.value.cextLov21 == '' || fvCtrl.fvdata.value.cextLov21 == 'null') ? null : fvCtrl.fvdata.value.cextLov21,
              listflag: (fvCtrl.cextlo21lov.value.isNotEmpty && fvCtrl.cextlo21lov.value != null),
              items: fvCtrl.cextlo21lov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  value: l.lookupCode,
                );
              }).toList(),
              // validate: (value) {
              //   if (value == null || value.isEmpty) {
              //     String vmsg = talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE').value + ' is Required';
              //     return vmsg;
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_COMMENTS') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_COMMENTS')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_TARGET_FIELDVALIDATION_COMMENTS')!.ro : true,
              value: fvCtrl.fvdata.value.comments ?? '',
              maxLines: 5,
              minLines: 1,
              keyboard: TextInputType.multiline,
              textInputAct: TextInputAction.newline,
              onChanged: (val) {
                fvCtrl.fvdata.value.comments = val != null ? val : '';
              },
              onSaved: (val) {
                fvCtrl.fvdata.value.comments = val;
              },
            ),
        ],
      ),
    );
  }
}
