import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/sitemanagement/target/targetview.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/sitemanagement/target/target_details.dart';

import '../../../common/component_utils.dart';
import '../../../providers/sitemanagement/target/target_controller.dart';
import 'search/target_filter_widget.dart';

class TargetSearch extends StatelessWidget {
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  TargetSearchController targetSearchState = Get.put(TargetSearchController());

  TargetSearchController targetSearchCtrlr = Get.find<TargetSearchController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshTargets() async {
    targetSearchCtrlr.getTargetSearchData(searchText: targetSearchCtrlr.searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_TARGETS')!.value!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        actions: [
          IconButton(
              onPressed: () async {
                Get.to(() => TargetFilterWidget());
                targetSearchCtrlr.filterwidgetloading.value = true;
                await talabel.getlabels('TMCMOBILE_TARGETSEARCH', 'Target_search', filtertype: 'tab');
                Future.delayed(const Duration(seconds: 1), () async {
                  targetSearchCtrlr.filterwidgetloading.value = false;
                });
              },
              icon: Icon(
                Icons.search,
                color: ComponentUtils.primecolor,
              )),
        ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          filterChipContainer(),

          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshTargets,
              child: GetX<TargetSearchController>(
                initState: (state) async {
                  await targetSearchCtrlr.getTargetSearchData(action: 'onload');
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.targetlist.length < 1
                          ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.targetlist[index]);
                              },
                              itemCount: _.targetlist.length,
                            );
                },
              ),
            ),
          ),

          // Expanded(
          //   child: GetBuilder<ProjectSearchController>(builder: (ctrlr) {
          //     return ctrlr.isLoading.value
          //         ? ProgressIndicatorCust()
          //         : (ctrlr.projects != null || ctrlr.projects.isNotEmpty)
          //             ? ListView.builder(
          //                 itemCount: ctrlr.projects.length,
          //                 itemBuilder: (context, index) {
          //                   return listitem(context, ctrlr.projects[index]);
          //                 })
          //             : Center(
          //                 child: Text('No Projects Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
          //               );
          //   }),
          // ),
        ],
      ),
    );
  }

  Widget filterChipContainer() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.only(left: 12, right: 10, top: 5, bottom: 2),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Wrap(
                alignment: WrapAlignment.start,
                spacing: 6.0,
                runSpacing: 6.0,
                children: List<Widget>.generate(targetSearchCtrlr.filterList.length, (int index) {
                  return targetSearchCtrlr.filterList[index];
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, TargetView t) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.to(
            () => TargetDetails(
              targetid: t.targetId,
              nav_from: 'search',
              targetname: t.targetName,
            ),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${t?.targetName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.shield,
                            color: secondary,
                            size: 15,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Text(t.targetNumber?.toString() ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        // Row(
                        //   children: <Widget>[
                        //     Icon(
                        //       Icons.bolt,
                        //       color: secondary,
                        //       size: 15,
                        //     ),
                        //     SizedBox(
                        //       width: 5,
                        //     ),
                        Text(t.statusDesc ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        //   ],
                        // ),
                      ],
                    ),
                    // SizedBox(
                    //   height: 6,
                    // ),
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   children: <Widget>[
                    //     Row(children: [
                    //       Icon(
                    //         Icons.assistant_photo,
                    //         color: secondary,
                    //         size: 15,
                    //       ),
                    //       SizedBox(
                    //         width: 5,
                    //       ),
                    //       Text(t.entityType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                    //     ]),
                    //     Text(t.ownershipType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                    //   ],
                    // ),
                    SizedBox(
                      height: 6,
                    ),
                    Row(
                      children: <Widget>[
                        Icon(
                          Icons.location_on,
                          color: secondary,
                          size: 15,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Expanded(
                          child: Text('${t.address ?? ''}' + ' ' + '${t.city ?? ''}' + ' ' + '${t.state ?? ' '}',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: primary,
                                fontSize: 12,
                                letterSpacing: .1,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
