import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/common/documents_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/demographic/entitydemographics.dart';
import 'package:tangoworkplace/screens/common/documents/documents.dart';
import 'package:tangoworkplace/screens/sitemanagement/target/tabs/target_attributes.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/sitemanagement/target/targetbvo.dart';
import '../../../providers/common/entitydemographics_controller.dart';
import '../../../providers/common/photosgrid_controller.dart';
import '../../../providers/sitemanagement/site/entityassociatedsites_controller.dart';
import '../../../providers/sitemanagement/target/target_attributes_controller.dart';
import '../../../providers/sitemanagement/target/target_fv_controller.dart';
import '../../../providers/sitemanagement/target/target_general_controller.dart';
import '../../../providers/sitemanagement/target/targetdetails_controller.dart';
import '../../common/associated_sites/entity_associatedsites.dart';
import '../../common/photos/photosgrid.dart';
import 'tabs/target_fieldvalidation.dart';
import 'tabs/target_general.dart';

class TargetDetails extends StatelessWidget {
  Targetbvo? trgt;
  int? targetid;
  final nav_from;
  String? targetname;
  TargetDetails({this.trgt, this.targetid, this.nav_from, this.targetname});

  TargetDetailsController detCtrl = Get.put(TargetDetailsController());

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey _targettabControllerKey = GlobalKey();
  final targettabs = <Tab>[];
  Applabel? label;
  LabelController talabel = Get.find<LabelController>();

  var generaltab;
  var demographicstab;
  var fieldvalidtab;
  //var photostab;
  var docstab;
  var asssocsitestab;
  var attributestab;

  List<Tab> tabsdata = [];

  String? _setTablabels(Applabel? label) {
    if (label != null) {
      targettabs.add(Tab(
        text: label.value,
      ));
      return label.value;
    }
  }

  Future _getlabels(LabelController labCtrl) async {
    try {
      // labCtrl.setdataloading.value = true;
      await labCtrl.getlabels('TMCMOBILE_TARGET', 'target_details', filtertype: 'page');
      if (detCtrl.tabroles.value!['general'] != 'na') generaltab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_GENERAL'));
      if (detCtrl.tabroles.value!['attributes'] != 'na') attributestab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_ATTRIBUTES'));
      if (detCtrl.tabroles.value!['demog'] != 'na') demographicstab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_DEMOGRAPHICS'));
      if (detCtrl.tabroles.value!['fv'] != 'na') fieldvalidtab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_FIELDVALIDATION'));

      // photostab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_PHOTOS'));
      if (detCtrl.tabroles.value!['document'] != 'na') docstab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_DOCUMENTS'));
      if (detCtrl.tabroles.value!['as'] != 'na') asssocsitestab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_ASSOCITEDSITES'));
    } catch (e) {
      debugPrint('$e');
    }
    labCtrl.setdataloading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    Future.delayed(Duration.zero, () {
      debugPrint('targetname-------${targetname}');
      talabel.setdataloading.value = true;

      TargetGeneralController targetGeneralController = Get.put(TargetGeneralController());
      TargetAttributesController targetAttributesController = Get.put(TargetAttributesController());
      //EntityCommentsController entityCommentsController = Get.put(EntityCommentsController());
      PhotosGridController photoController = Get.put(PhotosGridController());
      DocumentController documentController = Get.put(DocumentController());
      EntityAssociatedSitesController entityAssociatedSitesController = Get.put(EntityAssociatedSitesController());
      TargetFvController targetFvController = Get.put(TargetFvController());
      EntityDemographicsController demographicController = Get.put(EntityDemographicsController());
    });
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(targetname!, style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<LabelController>(initState: (state) {
        detCtrl.isloading.value = true;
        Future.delayed(const Duration(seconds: 1), () async {
          try {
            await detCtrl.getUserEntityEditAccess(entityid: targetid.toString(), entitytype: 'TARGET', roFlag: 'N');

            await _getlabels(talabel);
            await detCtrl.getTargetRecord(targetid?.toString());
            trgt = detCtrl.targetrec.value;
          } finally {
            detCtrl.isloading.value = false;
          }
        });
      }, builder: (_) {
        return (talabel.setdataloading.value || detCtrl.isloading.value)
            ? const ProgressIndicatorCust()
            : DefaultTabController(
                key: _targettabControllerKey,
                initialIndex: 0,
                length: (targettabs != null && targettabs.isNotEmpty) ? targettabs.length : 1,
                child: Column(children: <Widget>[
                  Material(
                    elevation: 5,
                    color: Colors.white,
                    child: TabBar(
                      //controller: _tabController,
                      isScrollable: true,
                      labelColor: ComponentUtils.tablabelcolor,
                      unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                      indicatorColor: ComponentUtils.tabindicatorColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: TextStyle(
                          fontWeight: FontWeight.bold, fontStyle: FontStyle.normal, fontSize: 14, color: ComponentUtils.tablabelcolor),
                      tabs: (targettabs != null && targettabs.isNotEmpty)
                          ? targettabs
                          : [
                              const Tab(
                                text: 'General',
                              )
                            ],
                    ),
                  ),
                  Expanded(
                    child: (targettabs == null || targettabs.isEmpty)
                        ? TabBarView(children: [
                            TargetGeneral(
                              target: trgt,
                            )
                          ])
                        : TabBarView(
                            children: [
                              if (generaltab != null && (detCtrl.tabroles.value!['general'] != 'na'))
                                TargetGeneral(
                                  target: trgt,
                                  mode: (detCtrl.mode.value == 'edit' && detCtrl.tabroles.value['general'] == 'edit') ? 'edit' : 'view',
                                ),
                              if (attributestab != null && (detCtrl.tabroles.value!['attributes'] != 'na'))
                                TargetAttributes(
                                  mode: (detCtrl.mode.value == 'edit' && detCtrl.tabroles.value['attributes'] == 'edit') ? 'edit' : 'view',
                                  target: trgt ?? Targetbvo(),
                                ),
                              if (demographicstab != null && (detCtrl.tabroles.value!['demog'] != 'na'))
                                EntityDemographics(
                                    entityType: 'TARGET',
                                    entityId: targetid,
                                    mode: (detCtrl.mode.value == 'edit' && detCtrl.tabroles.value['demog'] == 'edit') ? 'edit' : 'view'),
                              if (fieldvalidtab != null && (detCtrl.tabroles.value!['fv'] != 'na'))
                                TargetFieldValidation(
                                    trgt: trgt,
                                    mode: (detCtrl.mode.value == 'edit' && detCtrl.tabroles.value['fv'] == 'edit') ? 'edit' : 'view'),
                              // if (photostab != null && (detCtrl.tabroles.value!['general'] != 'na'))
                              //   PhotosGrid(
                              //     entityType: 'TARGET',
                              //     entityId: targetid,
                              //     mode: 'edit',
                              //   ),
                              if (docstab != null && (detCtrl.tabroles.value!['document'] != 'na'))
                                Documents(
                                    rootfolderid: trgt?.rootFolderId,
                                    entityType: 'TARGET',
                                    entityId: targetid,
                                    mode: (detCtrl.mode.value == 'edit' && detCtrl.tabroles.value['document'] == 'edit') ? 'edit' : 'view'),
                              if (asssocsitestab != null && (detCtrl.tabroles.value!['as'] != 'na'))
                                EntityAssociatedSites(
                                    entityType: 'TARGET',
                                    entityId: targetid,
                                    mode: (detCtrl.mode.value == 'edit' && detCtrl.tabroles.value['as'] == 'edit') ? 'edit' : 'view'),
                            ],
                          ),
                  ),
                ]),
              );
      }),
    );
  }
}
