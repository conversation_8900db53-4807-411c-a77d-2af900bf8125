import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:local_auth/local_auth.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/login_session.dart';
import 'package:tangoworkplace/common/widgets/sso_web_view.dart';
import 'package:tangoworkplace/providers/ta_admin/home_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/user_secure_storage.dart';
import '../providers/common/utils/mfalogin_controller.dart';
import '../screens/settings_screen.dart';
import '../utils/preferences_utils.dart';
import 'package:universal_html/html.dart' as shtml;

class LoginPage extends StatefulWidget {
  final String showPassword = "N";
  static const routName = '/login';
  @override
  State<StatefulWidget> createState() => new _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  String? showPassword;
  bool checkuser = true;
  String userError = '';
  final GlobalKey<FormState> _loginformKey = new GlobalKey<FormState>();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final userController = TextEditingController();
  final pwdController = TextEditingController();
  bool? rememberme = false;
  bool isLoading = false;
  String otpval = 'email';
  int? mfaexpiry = 2;
  bool showOtpTile = false;
  //bool isWeb = GetPlatform.isWeb;

  final _userFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final LocalAuthentication auth = LocalAuthentication();

  Future<bool> checkBiometrics() async {
    final bool canAuthenticate = await auth.canCheckBiometrics;
    final bool isDeviceSupported = await auth.isDeviceSupported();
    return canAuthenticate && isDeviceSupported;
  }

  Future<bool> authenticate() async {
    try {
      return await auth.authenticate(
        localizedReason: 'Authenticate to access your account',
        options: const AuthenticationOptions(
          biometricOnly: true, // Force biometric (no device PIN)
          useErrorDialogs: true, // OS handles error dialogs
          stickyAuth: true, // Keep auth state
        ),
      );
    } catch (e) {
      debugPrint('Error: $e');
      return false;
    }
  }

  void submitform(String username, String pwd, {String? otptype, int? mfaexpiry}) async {
    if (this._loginformKey.currentState!.validate()) {
      _loginformKey.currentState!.save();
      setState(() {
        this.isLoading = true;
      });
      await directlogin(username, pwd, _scaffoldKey.currentContext, otptype: otptype, mfaexpiry: mfaexpiry).then((_) {
        setState(() {
          this.isLoading = false;
        });
      });
    }
  }

  Future<void> rememberUserName(bool remember, String username) async {
    if (remember) {
      await UserContext.setUsername(username);
    } else {
      await UserContext.setUsername(null);
    }
  }

  Future<void> setwebhostname() async {
    if (GetPlatform.isWeb) {
      final hostname = shtml.window.location.hostname;
      debugPrint(' hostname>>>>>${hostname}');
      List hostlist = CommonUtils.hostlist;
      hostlist.forEach((element) {
        if (hostname!.toUpperCase().contains(element)) {
          SharedPrefUtils.saveStr(ConstHelper.hostNameVar, element?.toLowerCase());
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    SharedPrefUtils.clear();
    setwebhostname();
    bool labelCtrl = Get.isRegistered<LabelController>();
    debugPrint('labelCtrl------------ $labelCtrl');
    if (labelCtrl) Get.delete<LabelController>();
    bool homeCtrl = Get.isRegistered<HomeController>();
    debugPrint('homeCtrl------------ $homeCtrl');
    if (homeCtrl) Get.delete<HomeController>();
    bool mfaCtrl = Get.isRegistered<MfaLoginController>();
    debugPrint('mfaCtrl------------ $mfaCtrl');
    if (mfaCtrl) Get.delete<MfaLoginController>();
    init();
  }

  Future init() async {
    final name = await UserContext.getUsername() ?? '';

    setState(() {
      this.userController.text = name;
      if (name != null && name.isNotEmpty) rememberme = true;
    });
  }

  Future<void> resetform() async {
    //this.userController.clear();
    this.pwdController.clear();
    setState(() {
      this.showPassword = 'N';
    });
  }

  void validateuser(String username) {
    if (this._loginformKey.currentState!.validate()) {
      setState(() {
        this.isLoading = true;
      });
      _loginformKey.currentState!.save();
      rememberUserName(rememberme!, username);
      String validateurl = ApiService.getServerurl() + validateuserurl + "/" + username;
      var uri = Uri.parse(validateurl);
      http.get(uri).then((response) async {
        Map<String, dynamic>? c = new Map();
        c = jsonDecode(response.body) as Map<String, dynamic>?;
        debugPrint('$c');

        if (c != null) {
          if (c['status'] != 0) {
            setState(() {
              this.isLoading = false;
              this.checkuser = false;
              this.userError = 'Login was unsuccessful';
            });
          } else {
            String? ssoenabled = c['ssoEnabled'];
            String? ssoLoginUrl = c['ssoLoginUrl'];
            String? ssoLogoutUrl = c['ssoLogoutUrl'];
            String? aqBaseUrl = c['aqBaseUrl'];
            String ssoCheck = "no";
            var browserflag = c['externalBrowser'] ?? false;
            SharedPrefUtils.saveStr("SSOEnabled", ssoenabled ?? '');
            SharedPrefUtils.saveStr("SSOLogoutURL", ssoLogoutUrl ?? '');
            SharedPrefUtils.saveStr(ConstHelper.aqbaseurl, aqBaseUrl ?? '');

            if ("Y" == ssoenabled) {
              ssoCheck = "sso";
              if (ssoLoginUrl != null && ssoLoginUrl.isNotEmpty) ssoCheck = "ssourl";
            }

            debugPrint(ssoLoginUrl);

            if (ssoCheck == 'ssourl') {
              debugPrint("Inside ssoenabled");
              setState(() {
                this.checkuser = true;
                this.isLoading = false;
              });
              if (browserflag != null && browserflag) {
                var seid = await browserlogin(ssoLoginUrl!);
                if (seid != null && seid != 'error') {
                  seid = 'JSESSIONID=' + seid;
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoginSessionImpl(username, seid: seid),
                    ),
                  );
                } else {
                  setState(() {
                    this.checkuser = false;
                    this.userError = 'Unable to Login. Please contact Support';
                  });
                }
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SSOWebView(ssoLoginUrl, 'login', username),
                  ),
                );
              }
            } else if (ssoCheck == 'sso') {
              setState(() {
                this.checkuser = false;
                this.isLoading = false;
                this.userError = 'Unable to Login.Please contact Support';
              });
            } else {
              setState(() {
                this.checkuser = true;
                this.isLoading = false;
                this.showPassword = 'Y';
                this.showOtpTile = c!['mfaenabled'] == 'Y';
                if (c['mfaexpiry'] != null && c['mfaexpiry'] > 2)
                  this.mfaexpiry = c['mfaexpiry'] as int?;
                else
                  this.mfaexpiry = 2;
              });
            }
          }
        }
      }).catchError((err) {
        debugPrint('error----$err');
        setState(() {
          this.isLoading = false;
          this.checkuser = false;
          this.userError = 'Connection Unreachable';
        });
      });
    } else {
      setState(() {
        this.isLoading = false;
        this.checkuser = true;
      });
    }
  }

  Future<String> browserlogin(String _url) async {
    var _status = 'error';
    final callbackUrlScheme = 'tangoauth';

    debugPrint('browserloginurl--------' + _url);
    try {
      var result = await FlutterWebAuth.authenticate(url: _url, callbackUrlScheme: callbackUrlScheme);
      debugPrint('result>>>>    $result');

      if (result != null && result.contains('sessionId')) {
        var sid = result.split('sessionId=');
        _status = sid[1];
      }
    } on PlatformException catch (e) {
      debugPrint('Exception------' + e.message!);
    }
    debugPrint('_status     $_status');
    return _status;
  }

  @override
  void dispose() {
    userController.dispose();
    pwdController.dispose();
    _userFocusNode.dispose();
    _passwordFocusNode.dispose();

    super.dispose();
  }

  Widget loadCircle() {
    return Visibility(
      visible: this.isLoading,
      child: Container(
        height: 25.0,
        width: 50.0,
        padding: EdgeInsets.only(right: 25.0),
        child: ProgressIndicatorCust(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isKeyBoard = MediaQuery.of(context).viewInsets.bottom != 0;
    final isTablet = CommonUtils.isTablet(context);
    final deviceHeight = Get.height;
    //debugPrint('deviceHeight-----$deviceHeight');
    return //SafeArea(child:
        Scaffold(
      key: _scaffoldKey,
      bottomNavigationBar: homebottombar(),
      resizeToAvoidBottomInset: false,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      floatingActionButton: !GetPlatform.isWeb
          ? FloatingActionButton(
              mini: true,
              backgroundColor: Colors.white,
              foregroundColor: const Color(0XFFb10c00),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SettingsForm(),
                  ),
                );
              },
              child: Icon(Icons.settings_outlined),
            )
          : Container(),
      body: OrientationBuilder(
        builder: (context, orientation) {
          return isTablet && orientation.name == Orientation.landscape.name
              ? Padding(
                  padding: const EdgeInsets.fromLTRB(20.0, 30.0, 20.0, 20.0),
                  child: Container(
                    child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Column(
                            children: [
                              if (!isKeyBoard)
                                const SizedBox(
                                  height: 100.0,
                                ),
                              if (isKeyBoard)
                                Container(
                                  padding: EdgeInsets.only(top: 50),
                                  //height: 500,
                                  child: const Image(
                                    image: AssetImage('lib/icons/talogo_new.png'),
                                  ),
                                ),
                              const SizedBox(
                                height: 50,
                              ),
                              Container(
                                //color: Colors.amberAccent,
                                height: (Get.height / 2) - 50,
                                padding: EdgeInsets.only(bottom: 20),
                                child: const Image(
                                  image: AssetImage('lib/icons/ta-platform.png'),
                                ),
                              ),
                            ],
                          ),
                          Container(
                            padding: EdgeInsets.only(left: 50, top: 30),
                            child: Column(
                              children: [
                                if (!isKeyBoard)
                                  Container(
                                    padding: EdgeInsets.only(top: 50),
                                    //height: 500,
                                    child: const Image(
                                      image: AssetImage('lib/icons/talogo_new.png'),
                                    ),
                                  ),
                                const SizedBox(
                                  height: 50,
                                ),
                                Container(
                                  constraints: BoxConstraints(maxWidth: 500),
                                  padding: EdgeInsets.all(20.0),
                                  // height: 200,
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(10.0),
                                      boxShadow: [BoxShadow(color: Color.fromARGB(225, 95, 27, 1), blurRadius: 15, offset: Offset(0, 10))]),
                                  child: Form(
                                    key: this._loginformKey,
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                      children: <Widget>[
                                        LoginTextFormFIeld(
                                          userController: userController,
                                          label: 'User Name',
                                          hintText: 'User Name',
                                          obscureText: false,
                                          editable: this.showPassword != 'Y',
                                          focusNode: _userFocusNode,
                                          iconData: Icons.person,
                                        ),
                                        Visibility(
                                          visible: !this.checkuser,
                                          child: Container(
                                            // width: 30,
                                            margin: const EdgeInsets.only(top: 9.0),
                                            alignment: Alignment.bottomLeft,
                                            child: Text(this.userError,
                                                style: TextStyle(
                                                  fontSize: 12.0,
                                                  color: Theme.of(context).primaryColor,
                                                )),
                                          ),
                                        ),
                                        // SizedBox(height: 15),
                                        Visibility(
                                          visible: this.showPassword != 'Y',
                                          child: Container(
                                              padding: const EdgeInsets.only(top: 15.0),
                                              margin: const EdgeInsets.only(right: 10.0),
                                              width: 30,
                                              alignment: Alignment.bottomLeft,
                                              child: Row(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  Row(mainAxisAlignment: MainAxisAlignment.start, children: <Widget>[
                                                    Checkbox(
                                                      value: this.rememberme,
                                                      activeColor: Theme.of(context).primaryColor,
                                                      onChanged: (bool? value) {
                                                        _userFocusNode.unfocus();
                                                        setState(() {
                                                          this.rememberme = value;
                                                        });
                                                      },
                                                    ),
                                                    Text('Remember Me')
                                                  ]),
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.end,
                                                    children: [
                                                      loadCircle(),
                                                      TextButton(
                                                        child: new Text(
                                                          'Go',
                                                          style: TextStyle(
                                                              color: CommonUtils.createMaterialColor(Color(0XFFffffff)),
                                                              fontWeight: FontWeight.bold),
                                                        ),
                                                        onPressed: () {
                                                          _userFocusNode.unfocus();
                                                          validateuser(userController.text);
                                                        },
                                                        style: TextButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
                                                      ),
                                                    ],
                                                  )
                                                ],
                                              )),
                                        ),

                                        Visibility(
                                            visible: this.showPassword == 'Y',
                                            child: LoginTextFormFIeld(
                                              userController: pwdController,
                                              label: 'Password',
                                              hintText: 'Password',
                                              obscureText: true,
                                              focusNode: _passwordFocusNode,
                                              iconData: Icons.lock_open,
                                            )),
                                        otptyperadio(),
                                        Visibility(
                                          visible: this.showPassword == 'Y',
                                          child: Container(
                                            width: 30,
                                            alignment: Alignment.bottomLeft,
                                            margin: new EdgeInsets.only(top: 20.0, right: 10.0),
                                            child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                                              loadCircle(),
                                              TextButton(
                                                child: Text(
                                                  'Cancel',
                                                  style: new TextStyle(
                                                      color: CommonUtils.createMaterialColor(Color(0XFFffffff)),
                                                      fontWeight: FontWeight.bold),
                                                ),
                                                onPressed: () {
                                                  _passwordFocusNode.unfocus();
                                                  resetform();
                                                },
                                                style: TextButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
                                              ),
                                              SizedBox(height: 0.0, width: 10.0),
                                              TextButton(
                                                child: Text(
                                                  'Sign In',
                                                  style: TextStyle(
                                                      color: CommonUtils.createMaterialColor(Color(0XFFffffff)),
                                                      fontWeight: FontWeight.bold),
                                                ),
                                                onPressed: () {
                                                  _passwordFocusNode.unfocus();
                                                  submitform(userController.text, pwdController.text,
                                                      // otptype: showOtpTile ? otpval : 'no-otp');
                                                      otptype: showOtpTile ? 'email' : 'no-otp',
                                                      mfaexpiry: mfaexpiry);
                                                },
                                                style: TextButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
                                              ),
                                            ]),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ]),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.fromLTRB(20.0, 30.0, 20.0, 20.0),
                  child: Container(
                    child: new Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        //mainAxisSize: MainAxisSize.max,
                        children: <Widget>[
                          if (isTablet)
                            SizedBox(
                              height: 1.0,
                            ),
                          new Container(
                            padding: EdgeInsets.only(top: (deviceHeight < 680 && !isTablet) ? 10 : 40, bottom: isTablet ? 0 : 10),
                            //height: 500,
                            child: new Image(
                              image: AssetImage('lib/icons/talogo_new.png'),
                            ),
                          ),
                          //if (!isKeyBoard)
                          if (!isTablet)
                            new Container(
                              height: isKeyBoard
                                  ? 60.0
                                  : (deviceHeight < 680 && !isTablet)
                                      ? 190
                                      : 230.0,
                              padding: EdgeInsets.only(bottom: 20),
                              child: new Image(
                                image: AssetImage('lib/icons/ta-platform.png'),
                              ),
                            ),
                          if (!isTablet)
                            SizedBox(
                              height: 2,
                            ),
                          new Column(
                            children: [
                              new Container(
                                constraints: BoxConstraints(maxWidth: 500),
                                padding: EdgeInsets.fromLTRB(20.0, 7.0, 20.0, 15.0),
                                // height: 200,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(10.0),
                                    boxShadow: [BoxShadow(color: Color.fromARGB(225, 95, 27, 1), blurRadius: 15, offset: Offset(0, 10))]),
                                child: new Form(
                                  key: this._loginformKey,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.stretch,
                                    children: <Widget>[
                                      // new Text('Login',
                                      //     style: new TextStyle(
                                      //       fontSize: 18.0,
                                      //       fontWeight: FontWeight.bold,
                                      //       color: Theme.of(context).primaryColor,
                                      //     )),
                                      LoginTextFormFIeld(
                                        userController: userController,
                                        label: 'User Name',
                                        hintText: 'User Name',
                                        obscureText: false,
                                        editable: this.showPassword != 'Y',
                                        focusNode: _userFocusNode,
                                        iconData: Icons.person,
                                      ),
                                      Visibility(
                                        visible: !this.checkuser,
                                        child: Container(
                                          // width: 30,
                                          margin: const EdgeInsets.only(top: 9.0),
                                          alignment: Alignment.bottomLeft,
                                          child: new Text(this.userError,
                                              style: new TextStyle(
                                                fontSize: 12.0,
                                                color: Theme.of(context).primaryColor,
                                              )),
                                        ),
                                      ),
                                      // SizedBox(height: 15),
                                      Visibility(
                                        visible: this.showPassword != 'Y',
                                        child: Container(
                                            padding: const EdgeInsets.only(top: 15.0),
                                            margin: new EdgeInsets.only(right: 10.0),
                                            width: 30,
                                            alignment: Alignment.bottomLeft,
                                            child: new Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                new Row(mainAxisAlignment: MainAxisAlignment.start, children: <Widget>[
                                                  new Checkbox(
                                                    value: this.rememberme,
                                                    activeColor: Theme.of(context).primaryColor,
                                                    onChanged: (bool? value) {
                                                      _userFocusNode.unfocus();
                                                      setState(() {
                                                        this.rememberme = value;
                                                      });
                                                    },
                                                  ),
                                                  Text('Remember Me')
                                                ]),
                                                new Row(
                                                  mainAxisAlignment: MainAxisAlignment.end,
                                                  children: [
                                                    loadCircle(),
                                                    TextButton(
                                                      child: new Text(
                                                        'Go',
                                                        style: new TextStyle(
                                                            color: CommonUtils.createMaterialColor(Color(0XFFffffff)),
                                                            fontWeight: FontWeight.bold),
                                                      ),
                                                      onPressed: () {
                                                        _userFocusNode.unfocus();
                                                        validateuser(userController.text);
                                                      },
                                                      style: TextButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
                                                    ),
                                                  ],
                                                )
                                              ],
                                            )),
                                      ),

                                      Visibility(
                                          visible: this.showPassword == 'Y',
                                          child: LoginTextFormFIeld(
                                            userController: pwdController,
                                            label: 'Password',
                                            hintText: 'Password',
                                            obscureText: true,
                                            focusNode: _passwordFocusNode,
                                            iconData: Icons.lock_open,
                                          )),

                                      // otptyperadio(),

                                      Visibility(
                                        visible: this.showPassword == 'Y',
                                        child: new Container(
                                          width: 30,
                                          alignment: Alignment.bottomLeft,
                                          // margin: new EdgeInsets.only(top: showOtpTile ? 1 : 15.0, right: 10.0),
                                          margin: new EdgeInsets.only(top: 15.0, right: 10.0),
                                          child: new Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                                            loadCircle(),
                                            new TextButton(
                                              child: new Text(
                                                'Cancel',
                                                style: new TextStyle(
                                                    color: CommonUtils.createMaterialColor(Color(0XFFffffff)), fontWeight: FontWeight.bold),
                                              ),
                                              onPressed: () {
                                                _passwordFocusNode.unfocus();
                                                resetform();
                                              },
                                              style: TextButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
                                            ),
                                            SizedBox(height: 0.0, width: 10.0),
                                            new TextButton(
                                              child: new Text(
                                                'Sign In',
                                                style: new TextStyle(
                                                    color: CommonUtils.createMaterialColor(Color(0XFFffffff)), fontWeight: FontWeight.bold),
                                              ),
                                              onPressed: () {
                                                _passwordFocusNode.unfocus();
                                                submitform(userController.text, pwdController.text,
                                                    // otptype: showOtpTile ? otpval : 'no-otp');
                                                    otptype: showOtpTile ? 'email' : 'no-otp',
                                                    mfaexpiry: mfaexpiry);
                                              },
                                              style: TextButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
                                            ),
                                          ]),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (isTablet)
                            SizedBox(
                              height: 30,
                            ),
                          if (isTablet)
                            new Container(
                              //color: Colors.amberAccent,
                              height: (Get.height / 2) - 30,
                              padding: EdgeInsets.only(bottom: 20),
                              child: new Image(
                                image: AssetImage('lib/icons/ta-platform.png'),
                              ),
                            ),
                        ]),
                  ),
                );
        },
      ),
    );
  }

  Widget homebottombar() {
    return BottomAppBar(
      elevation: 5,
      child: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[Text(ConstHelper.appversion, style: TextStyle(fontSize: 14))],
        ),
      ),
      //to add a space between the FAB and BottomAppBar
      //color of the BottomAppBar
      color: Colors.white,
    );
  }

  Widget otptyperadio() {
    return Visibility(
      visible: this.showPassword == 'Y' && showOtpTile,
      child: new Container(
        width: 30,
        alignment: Alignment.bottomLeft,
        //margin: new EdgeInsets.only(top: 10.0, right: 10.0),
        child: new Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          SizedBox(
            width: 10.0,
          ),
          Text(
            'OTP   :',
            style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 12),
          ),
          // SizedBox(
          //   width: 5.0,
          // ),
          Radio(
            value: 'email',
            groupValue: otpval,
            onChanged: (dynamic value) {
              setState(() {
                otpval = value.toString();
              });
            },
          ),
          // Expanded(
          //   child:
          Text(
            'Email',
            style: TextStyle(fontSize: 12),
          ),
          // ),
          Radio(
            value: 'phone',
            groupValue: otpval,
            onChanged: (dynamic value) {
              setState(() {
                otpval = value.toString();
              });
            },
          ),
          // Expanded(
          // child:
          Text(
            'Phone',
            style: TextStyle(fontSize: 12),
          ),
          // ),
        ]),
      ),
    );
  }
}

class LoginTextFormFIeld extends StatelessWidget {
  const LoginTextFormFIeld({
    Key? key,
    required this.userController,
    required this.label,
    required this.hintText,
    required this.obscureText,
    this.editable,
    this.focusNode,
    this.iconData,
  }) : super(key: key);

  final TextEditingController userController;
  final String label;
  final String hintText;
  final bool obscureText;
  final bool? editable;
  final FocusNode? focusNode;
  final IconData? iconData;

  @override
  Widget build(BuildContext context) {
    return new TextFormField(
        style: TextStyle(color: Colors.black),
        focusNode: focusNode,
        cursorColor: Theme.of(context).primaryColor,
        controller: userController,
        obscureText: obscureText,
        showCursor: true,
        enabled: editable == null ? true : editable,
        decoration: new InputDecoration(
            contentPadding: EdgeInsets.all(14.0),
            border: InputBorder.none,
            prefixIcon: IconData != null
                ? new Icon(
                    iconData,
                    color: Theme.of(context).primaryColor,
                  )
                : null,
            hintText: hintText,
            //labelText: label,
            labelStyle: TextStyle(color: Colors.black),
            hintStyle: TextStyle(color: Colors.black38, fontSize: 14.0),
            enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Theme.of(context).primaryColor)),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Theme.of(context).primaryColor),
            ),
            errorStyle: TextStyle(color: Theme.of(context).primaryColor),
            errorBorder: UnderlineInputBorder(borderSide: BorderSide(color: Theme.of(context).primaryColor))),
        validator: (value) {
          if (value!.isEmpty) {
            return 'Please enter $label';
          }
          return null;
        });
  }
}
