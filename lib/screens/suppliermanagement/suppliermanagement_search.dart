import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import '../../../common/component_utils.dart';
import '../../models/suppliermanagement/supplier_search.dart';
import '../../providers/suppliermangement/suppliermangementsearch_controller.dart';
import 'supplier_details_screen.dart';

class SupplierManagementSearch extends StatelessWidget {
  String? source;
  SupplierManagementSearch({Key? key, this.source}) : super(key: key);
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  SupplierMangementSearchController supplierMgmtState = Get.put(SupplierMangementSearchController());

  SupplierMangementSearchController supplierMgmtCtrlr = Get.find<SupplierMangementSearchController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshSites() async {
    await supplierMgmtCtrlr.getSupplierData(searchText: supplierMgmtCtrlr.searchCtrl.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_SUPPLIERMGMT')!.value!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        actions: [
          if (source != null)
            IconButton(
                onPressed: () {
                  Get.off(() => HomeScreen());
                },
                icon: Icon(
                  Icons.home,
                  color: ComponentUtils.primecolor,
                )),
        ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            // Get.off(() => HomeScreen());
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          TaSearchInputText(
              makeSearch: (searchtext) {
                supplierMgmtCtrlr.getSupplierData(searchText: searchtext.toString());
              },
              searchController: supplierMgmtCtrlr.searchCtrl,
              hintSearch: 'Search '),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshSites,
              child: GetX<SupplierMangementSearchController>(
                initState: (state) {
                  supplierMgmtCtrlr.getSupplierData();
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.suppliers.isEmpty
                          ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.suppliers[index]);
                              },
                              itemCount: _.suppliers.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, SupplierSearch s) {
    final primary = Color(0xff696b9e);
    final secondary = Color(0xfff29a94);

    return GestureDetector(
        onTap: () {
          debugPrint('supplier id >>>>>>>>${s.supplierId}--${s.supplierName}');
          Get.to(() => SupplierDetails(
                parentMode: 'edit',
                supplierid: s.supplierId,
                supplierName: s.supplierName,
                navFrom: 'search',
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      s?.supplierName ?? '',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.shield,
                            color: secondary,
                            size: 15,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Text(s.supplierNumber?.toString() ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        // Row(
                        //   children: <Widget>[
                        //     Icon(
                        //       Icons.bolt,
                        //       color: secondary,
                        //       size: 15,
                        //     ),
                        //     SizedBox(
                        //       width: 5,
                        //     ),
                        Text(s.status ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        //   ],
                        // ),
                      ],
                    ),
                    // SizedBox(
                    //   height: 6,
                    // ),

                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      children: <Widget>[
                        Icon(
                          Icons.location_on,
                          color: secondary,
                          size: 15,
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Expanded(
                          child: Text('${s.address1 ?? ''}' + ' ' + '${s.city ?? ''}' + ' ' + '${s.state ?? ' '}',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: primary,
                                fontSize: 12,
                                letterSpacing: .1,
                              )),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.person,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(s.contactPerson ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                      ],
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.business_center,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(s.supplierType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
