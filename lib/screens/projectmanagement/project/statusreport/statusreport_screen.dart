import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/models/project/statusreport/statusreport.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/statusreport/statusreport_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/statusreport/statusreport_details_screen.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class StatusReports extends StatelessWidget {
  final entityid;
  final ProjectsView? projectview;
  String? mode;
  StatusReports({Key? key, this.entityid, this.projectview, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  StatusReportController statusrepoCtrl = Get.find<StatusReportController>();

  @override
  Widget build(BuildContext context) {
    return listStack(context);
  }

  Future _refreshStatusReports() async {
    await statusrepoCtrl.loadStatusReportsData(entityid);
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      // Row(
      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //   children: [
      //     Row(
      //       children: [
      //         SizedBox(
      //           width: 15,
      //         ),
      //         TaButton(
      //           type: 'elevate',
      //           buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_ADD')?.value ?? 'Add',
      //           onPressed: () {
      //             debugPrint('Add----------');
      //             //addPunchlistItem(context);
      //           },
      //         ),
      //       ],
      //     ),
      //   ],
      // ),
      Positioned.fill(
        //top: 45,
        child: RefreshIndicator(
          onRefresh: _refreshStatusReports,
          child: GetX<StatusReportController>(
            initState: (state) {
              statusrepoCtrl.loadStatusReportsData(entityid);
              Future.delayed(Duration(seconds: 0), () async {
                await statusrepoCtrl.getlabels(talabel);
                statusrepoCtrl.labelsloading.value = false;
              });
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? ProgressIndicatorCust()
                  : _.statusreportsdata.length < 1
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.statusreportsdata[index]);
                          },
                          itemCount: _.statusreportsdata.length,
                        );
            },
          ),
        ),
      ),
      if (mode == 'edit')
        Positioned(
          bottom: 30,
          right: 30,
          child: FloatingActionButton(
            elevation: 5.0,
            child: const Icon(
              Icons.add,
              size: 30,
            ),
            onPressed: () {
              debugPrint('add button----------------------');
              statusrepoCtrl.statusreport.value = StatusReport(projectId: entityid);
              statusrepoCtrl.sr_mode.value = 'create';
              Get.to(() => StatusReportDetails(
                    projectid: entityid,
                    projectview: projectview,
                    mode: mode,
                  ));
            },
          ),
        ),
    ]);
  }

  Widget listitem(BuildContext context, StatusReport sr) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {
        statusrepoCtrl.statusreport.value = sr;
        statusrepoCtrl.sr_mode.value = 'edit';
        Get.to(() => StatusReportDetails(
              projectid: entityid,
              mode: mode,
            ));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,

                  //crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Obx(
                          () => Text(
                            talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_SEARCH_WEEKENDING')?.value ?? 'Week Ending',
                            style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 10),
                          ),
                        ),
                        Text(
                          ' : ',
                          style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 10),
                        ),
                        Text(
                          sr.weekEnding?.toString() ?? '',
                          style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    // Flexible(
                    //   child: Text(
                    //     punch.scopePunchName ?? '',
                    //     style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                    //     maxLines: 1,
                    //     overflow: TextOverflow.ellipsis,
                    //   ),
                    // ),
                  ]),
              SizedBox(
                height: 5,
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Row(
                  children: [
                    Obx(
                      () => Text(
                        talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_SEARCH_PROJECTINWEEK')?.value ?? 'Project In Week',
                        style: TextStyle(color: primary, fontSize: 10),
                      ),
                    ),
                    Text(
                      ' : ',
                      style: TextStyle(color: primary, fontSize: 10),
                    ),
                    Text(
                      sr.projectInWeek?.toString() ?? '',
                      style: TextStyle(color: primary, fontSize: 10),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                // Text(
                //   sr.projectInWeek?.toString() ?? '',
                //   style: TextStyle(color: primary, fontSize: 10),
                // ),
                Text(
                  sr.jobSuper ?? '',
                  style: TextStyle(color: primary, fontSize: 10),
                ),
              ]),
              SizedBox(
                height: 5.0,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      sr.constructionManager ?? '',
                      style: TextStyle(color: primary, fontSize: 10),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
