import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputdate.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/models/project/statusreport/statusreport.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/statusreport/statusreport_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/photos/photospg.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/statusreport/statusreportdetails_data.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../../common/component_utils.dart';
import '../../../common/photos/photosgrid.dart';
import '../../../common/utils/dms/dmsfilepg.dart';

class StatusReportDetails extends StatelessWidget {
  StatusReport? sr;
  final projectid;
  final ProjectsView? projectview;
  String? mode;

  StatusReportDetails({Key? key, this.sr, this.projectid, this.projectview, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  StatusReportController statusrepoCtrl = Get.find<StatusReportController>();
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GetX<LabelController>(
      initState: (state) {
        statusrepoCtrl.labelsloading.value = true;
        statusrepoCtrl.isdetailsloading.value = true;

        Future.delayed(Duration(seconds: 1), () async {
          sr = statusrepoCtrl.statusreport.value;

          await statusrepoCtrl.getlabels(talabel);
          await statusrepoCtrl.setCurrentRow();
          statusrepoCtrl.labelsloading.value = false;
          statusrepoCtrl.isdetailsloading.value = false;
        });
      },
      builder: (_) {
        return (statusrepoCtrl.labelsloading.value || statusrepoCtrl.isdetailsloading.value)
            ? ComponentUtils.labelLoadScaffold()
            : Scaffold(
                appBar: AppBar(
                  iconTheme: Theme.of(context).appBarTheme.iconTheme,
                  title: Text(talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS')?.value ?? '', style: ComponentUtils.appbartitlestyle),
                  backgroundColor: Colors.white,
                  elevation: 5,
                  leading: new IconButton(
                    icon: ComponentUtils.backpageIcon,
                    color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                    onPressed: () {
                      debugPrint('------back-------');
                      Get.back();
                      statusrepoCtrl.loadStatusReportsData(projectid);
                    },
                  ),
                ),
                body: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: statusReportDetForm(),
                ),
                //bottomNavigationBar: bottombuttonbar(_formKey),
                bottomSheet: bottombuttonbar(_formKey),
              );
      },
    );
  }

  Widget statusReportDetForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Obx(
        () => Column(
          children: <Widget>[
// projnifame
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJNAME') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJNAME')!.value,
                readOnly: true, //talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJNAME').ro,
                value: statusrepoCtrl.sr_mode == 'create'
                    ? (projectview?.projectName ?? '')
                    : (statusrepoCtrl.statusreport.value?.projectName ?? ''),
              ),
// storenum
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER')!.value,
                readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER').ro,
                value: statusrepoCtrl.sr_mode == 'create'
                    ? (projectview?.storeNumber ?? '')
                    : (statusrepoCtrl.statusreport.value?.storeNumber ?? ''),
              ),
// realestatemanag
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_REALESTATEMANAGER') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_REALESTATEMANAGER')!.value,
                readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_REALESTATEMANAGER').ro,
                value: statusrepoCtrl.sr_mode == 'create'
                    ? (projectview?.realEstateManager ?? '')
                    : (statusrepoCtrl.statusreport.value?.realEstateManager ?? ''),
              ),
// constructmanag
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_CONSTMANAGER') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_CONSTMANAGER')!.value,
                readOnly: true, //talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_CONSTMANAGER').ro,
                value: statusrepoCtrl.sr_mode == 'create'
                    ? (projectview?.constructionManagerDesc ?? '')
                    : (statusrepoCtrl.statusreport.value?.constructionManager ?? ''),
              ),

// weekending
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEEKENDING') != null)
              TaFormInputDate(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEEKENDING')!.value,
                controller: statusrepoCtrl.weekendingCtrl,
                onChanged: (dateval) {
                  debugPrint('weekending--------- $dateval');
                  statusrepoCtrl.weekendingCtrl.text = dateval.toString();
                  statusrepoCtrl.statusreport.value.weekEnding = dateval.toString();
                },
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEEKENDING')!.ro : true,
                onSaved: (val) {
                  statusrepoCtrl.weekendingCtrl.text = val;
                  statusrepoCtrl.statusreport.value.weekEnding = val;
                },
              ),

// generalcontract
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_GENERALCONTRACTOR') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_GENERALCONTRACTOR')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_GENERALCONTRACTOR')!.ro : true,
                value: statusrepoCtrl.statusreport.value?.generalContractor ?? '',
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.generalContractor = val;
                },
              ),
// jonsuper
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_JOBSUPER') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_JOBSUPER')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_JOBSUPER')!.ro : true,
                value: statusrepoCtrl.statusreport.value?.jobSuper ?? '',
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.jobSuper = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.jobSuper = val;
                },
              ),
// phonenum
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PHONENUMBER') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PHONENUMBER')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PHONENUMBER')!.ro : true,
                value: statusrepoCtrl.statusreport.value.phoneNumber ?? '',
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.phoneNumber = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.phoneNumber = val;
                },
                keyboard: TextInputType.number,
              ),
// projectinweek
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJECTINWEEK') != null)
              TaFormInputText(
                keyboard: TextInputType.number,
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJECTINWEEK')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PROJECTINWEEK')!.ro : true,
                value: statusrepoCtrl.statusreport.value.projectInWeek?.toString() ?? '',
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.projectInWeek = (val != null && val != '') ? int.parse(val) : null;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.projectInWeek = (val != null && val != '') ? int.parse(val) : null;
                },
              ),

// financehealth
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH') != null)
              TaFormDropdown(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.value,
                emptttext: 'Select',
                onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.ro!)
                    ? null
                    : (newValue) {
                        debugPrint('newValue          ' + newValue);
                        statusrepoCtrl.statusreport.value.financialHealth = newValue;
                      },
                value:
                    (statusrepoCtrl.statusreport.value.financialHealth == '' || statusrepoCtrl.statusreport.value.financialHealth == 'null')
                        ? null
                        : statusrepoCtrl.statusreport.value.financialHealth,
                listflag: (statusrepoCtrl.financeHealthlov.value.isNotEmpty && statusrepoCtrl.financeHealthlov.value != null),
                items: statusrepoCtrl.financeHealthlov.value.map((LookupValues l) {
                  return DropdownMenuItem(
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                    value: l.lookupCode,
                  );
                }).toList(),
              ),

// schedulehealth
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_SCHEDULEHEALTH') != null)
              TaFormDropdown(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_SCHEDULEHEALTH')!.value,
                emptttext: 'Select',
                onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_SCHEDULEHEALTH')!.ro!)
                    ? null
                    : (newValue) {
                        debugPrint('newValue          ' + newValue);
                        statusrepoCtrl.statusreport.value.scheduleHealth = newValue;
                      },
                value:
                    (statusrepoCtrl.statusreport.value.scheduleHealth == '' || statusrepoCtrl.statusreport.value.scheduleHealth == 'null')
                        ? null
                        : statusrepoCtrl.statusreport.value.scheduleHealth,
                listflag: (statusrepoCtrl.scheduleHealthlov.value.isNotEmpty && statusrepoCtrl.scheduleHealthlov.value != null),
                items: statusrepoCtrl.scheduleHealthlov.value.map((LookupValues l) {
                  return DropdownMenuItem(
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                    value: l.lookupCode,
                  );
                }).toList(),
              ),
// riskhealth
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_RISKHEALTH') != null)
              TaFormDropdown(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_RISKHEALTH')!.value,
                emptttext: 'Select',
                onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_RISKHEALTH')!.ro!)
                    ? null
                    : (newValue) {
                        debugPrint('newValue          ' + newValue);
                        statusrepoCtrl.statusreport.value.riskHealth = newValue;
                      },
                value: (statusrepoCtrl.statusreport.value.riskHealth == '' || statusrepoCtrl.statusreport.value.riskHealth == 'null')
                    ? null
                    : statusrepoCtrl.statusreport.value.riskHealth,
                listflag: (statusrepoCtrl.riskHealthlov.value.isNotEmpty && statusrepoCtrl.riskHealthlov.value != null),
                items: statusrepoCtrl.riskHealthlov.value.map((LookupValues l) {
                  return DropdownMenuItem(
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                    value: l.lookupCode,
                  );
                }).toList(),
              ),
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET') != null && statusrepoCtrl.sr_mode.value != 'create')
              reportdetails(),
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PHOTOS') != null && statusrepoCtrl.sr_mode.value != 'create')
              photos(talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PHOTOS')?.value ?? 'photos',
                  talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PHOTOS')?.ro ?? false),
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DOCUMENTS') != null && statusrepoCtrl.sr_mode.value != 'create')
              documents(talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DOCUMENTS')?.value ?? 'documents',
                  talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DOCUMENTS')?.ro ?? false),
// weatherthisweek
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEATHERTHISWEEK') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEATHERTHISWEEK')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEATHERTHISWEEK')!.ro : true,
                value: statusrepoCtrl.statusreport.value.weatherThisWeek ?? '',
                maxLines: 5,
                minLines: 1,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.weatherThisWeek = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.weatherThisWeek = val;
                },
              ),
// weathernextweek
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEATHERNEXTWEEK') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEATHERNEXTWEEK')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEATHERNEXTWEEK')!.ro : true,
                value: statusrepoCtrl.statusreport.value.weatherNextWeek ?? '',
                maxLines: 5,
                minLines: 1,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.weatherNextWeek = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.weatherNextWeek = val;
                },
              ),
// workschedulethisweek
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKSCHEDULETHISWEEK') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKSCHEDULETHISWEEK')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKSCHEDULETHISWEEK')!.ro : true,
                value: statusrepoCtrl.statusreport.value.workScheduleThisWeek ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.workScheduleThisWeek = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.workScheduleThisWeek = val;
                },
              ),
// worknotcomplted
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKNOTCOMPLETED') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKNOTCOMPLETED')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKNOTCOMPLETED')!.ro : true,
                value: statusrepoCtrl.statusreport.value.workNotCompleted ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.workNotCompleted = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.workNotCompleted = val;
                },
              ),
// reasonnotcompleted
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_REASONNOTCOMPLETED') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_REASONNOTCOMPLETED')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_REASONNOTCOMPLETED')!.ro : true,
                value: statusrepoCtrl.statusreport.value.reasonNotCompleted ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.reasonNotCompleted = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.reasonNotCompleted = val;
                },
              ),
// workschedulenexweek
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKSCHEDULENEXTWEEK') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKSCHEDULENEXTWEEK')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WORKSCHEDULENEXTWEEK')!.ro : true,
                value: statusrepoCtrl.statusreport.value.workScheduleNextWeek ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.workScheduleNextWeek = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.workScheduleNextWeek = val;
                },
              ),
// generalcommen
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_GENERALCOMMENTS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_GENERALCOMMENTS')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_GENERALCOMMENTS')!.ro : true,
                value: statusrepoCtrl.statusreport.value.generalComments ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.generalComments = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.generalComments = val;
                },
              ),
// schedulecommen
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_SCHEDULECOMMENTS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_SCHEDULECOMMENTS')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_SCHEDULECOMMENTS')!.ro : true,
                value: statusrepoCtrl.statusreport.value.scheduleComments ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.scheduleComments = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.scheduleComments = val;
                },
              ),
// budgetcomm
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_BUDGETCOMMENTS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_BUDGETCOMMENTS')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_BUDGETCOMMENTS')!.ro : true,
                value: statusrepoCtrl.statusreport.value.budgetComments ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.budgetComments = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.budgetComments = val;
                },
              ),
// riskcomm
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_RISKCOMMENTS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_RISKCOMMENTS')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_RISKCOMMENTS')!.ro : true,
                value: statusrepoCtrl.statusreport.value.riskComments ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.riskComments = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.riskComments = val;
                },
              ),
// actionitems
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_ACTIONITEMS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_ACTIONITEMS')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_ACTIONITEMS')!.ro : true,
                value: statusrepoCtrl.statusreport.value.actionItems ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.actionItems = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.actionItems = val;
                },
              ),
// perreportcomm
            if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PERREPORTCOMMENTS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PERREPORTCOMMENTS')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_PERREPORTCOMMENTS')!.ro : true,
                value: statusrepoCtrl.statusreport.value.perReportComments ?? '',
                maxLines: 5,
                minLines: 5,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  statusrepoCtrl.statusreport.value.perReportComments = val;
                },
                onSaved: (val) {
                  statusrepoCtrl.statusreport.value.perReportComments = val;
                },
              ),
            const SizedBox(
              height: 50,
            )
          ],
        ),
      ),
    );
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    bool isKeyPad = MediaQuery.of(Get.context!).viewInsets.bottom != 0;
    return BottomAppBar(
      color: Colors.white,
      child: Obx(
        () => Container(
          margin: const EdgeInsets.only(left: 12.0, right: 20.0, bottom: 10.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  Get.back();
                  statusrepoCtrl.loadStatusReportsData(projectid);
                },
              ),
              if (mode == 'edit')
                const SizedBox(
                  height: 5.0,
                  width: 5.0,
                ),
              if (mode == 'edit')
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                  onPressed: () async {
                    final isValid = fkey.currentState!.validate();
                    if (!isValid) {
                      return;
                    }

                    _formKey.currentState!.save();
                    debugPrint('------------------saved--------------------');
                    await statusrepoCtrl.onSaveStatusReport();
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget reportdetails() {
    var txt = talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET')?.value ?? 'Details';
    return Container(
      margin: EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(txt),
        IconButton(
            onPressed: () {
              Get.to(() => StatusReportDetailsdata(
                    appbartitle: txt,
                    projectid: statusrepoCtrl.statusreport.value.projectId,
                    statusid: statusrepoCtrl.statusreport.value.statusId,
                    mode: mode,
                  ));
            },
            icon: Icon(
              Icons.calendar_view_day,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget photos(String labelStr, bool edit) {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => PhotosPG(
                    entityId: statusrepoCtrl.statusreport.value.statusId,
                    entityType: 'STATUS_REPORT',
                    mode: mode == 'edit' ? (edit ? 'edit' : 'view') : 'view',
                  ));
            },
            icon: Icon(
              Icons.photo_library,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget documents(String labelStr, bool edit) {
    return Container(
      padding: EdgeInsets.only(bottom: 10),
      margin: EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => DmsFilePG(
                    entityId: statusrepoCtrl.statusreport.value.projectId,
                    entityType: 'PROJECT',
                    subentityid: statusrepoCtrl.statusreport.value.statusId,
                    subentitytype: 'STATUS_REPORT',
                    file_mode: edit ? 'edit' : 'view',
                    mode: mode,
                  ));
            },
            icon: Icon(
              Icons.file_present,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }
}
