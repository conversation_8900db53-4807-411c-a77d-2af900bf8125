import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tatable.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/project/budget/pohdr.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/po_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class Podata extends StatelessWidget {
  final int? entityid;
  Podata({Key? key, this.entityid}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  PoController poCtrl = Get.put(PoController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO')?.value ?? '',
              style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
          actions: <Widget>[
            IconButton(
              color: ComponentUtils.primecolor,
              icon: const Icon(Icons.swap_horizontal_circle),
              onPressed: () {
                poCtrl.tblflag.toggle();
              },
            ),
          ],
          backgroundColor: Colors.white,
          elevation: 5,
          leading: new IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
            onPressed: () {
              debugPrint('------back-------');
              Get.back();
            },
          ),
        ),
        body: potable(),

        //  Container(
        //   color: Colors.white,
        //   child: GetX<PoController>(
        //     initState: (state) {
        //       poCtrl.loadPOheaderData(projectid: entityid);
        //     },
        //     builder: (ctrl) {
        //       return ctrl.ishdrloading.isTrue
        //           ? ProgressIndicatorCust()
        //           : ctrl.podata.length < 1
        //               ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context))))
        //               : _getBodyWidget();
        //     },
        //   ),
        // ),
      ),
    );
  }

  Future _refreshPoData() async {
    poCtrl.loadPOheaderData(projectid: entityid);
  }

  Widget potable() {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: RefreshIndicator(
          onRefresh: _refreshPoData,
          child: GetX<PoController>(
            initState: (state) {
              poCtrl.loadPOheaderData(projectid: entityid);
            },
            builder: (ctrl) {
              return ctrl.ishdrloading.isTrue
                  ? ProgressIndicatorCust()
                  : (ctrl.podata.isEmpty || ctrl.podata.value == null)
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                      : (poCtrl.tblflag.value
                          ? TaTable(
                              columns: [
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_PONUM') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_PONUM')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_STATUS') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_STATUS')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_ISSUEDATE') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_ISSUEDATE')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_TOTAMT') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_TOTAMT')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_SUPPLIER') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_SUPPLIER')!.value,
                              ],
                              rows: getRows(poCtrl.podata),
                            )
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, ctrl.podata[index]);
                              },
                              itemCount: ctrl.podata.length,
                            ));
            },
          ),
        ),
      ),
    ]);
  }

  List<DataRow> getRows(List<PoHdr> podata) => podata.map((PoHdr po) {
        final cells = [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID') != null) po.poId.toString() ?? '0',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_PONUM') != null) po.poNumber ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_STATUS') != null) po.statusDesc ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_ISSUEDATE') != null) po.issueDate ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_TOTAMT') != null)
            ComponentUtils.convertNumberFormat(po.totalAmount?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_SUPPLIER') != null) po.supplierName ?? ''
        ];
        List<DataCell> getCells(List<dynamic> cells) => cells.map((data) => DataCell(Text('$data'))).toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  Widget listitem(BuildContext context, PoHdr po) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      padding: EdgeInsets.only(right: 10, left: 10, top: 5),
      child: Column(
        children: [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_PONUM') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_PONUM')!.value!, po.poNumber ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_STATUS') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_STATUS')!.value!, po.statusDesc ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_ISSUEDATE') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_ISSUEDATE')!.value!, po.issueDate ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_TOTAMT') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_TOTAMT')!.value!, po.totalAmount?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_SUPPLIER') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_SUPPLIER')!.value!, po.supplierName ?? ''),
        ],
      ),
    );
  }
}
