import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tatable.dart';
import 'package:tangoworkplace/models/common/budget/budgetRecord.dart';
import 'package:tangoworkplace/models/project/budget/budget.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/budget_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/component_utils.dart';

class BudgetData extends GetView<BudgetController> {
  final String? entityType;
  final int? entityId;
  final String? source;
  final String? hdrTxt;
  BudgetData({Key? key, this.entityType, this.entityId, this.source, this.hdrTxt}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  BudgetController budgetCntrl = Get.find<BudgetController>();

  Future _refreshBudgetData() async {
   // budgetCntrl.loadEntityBudget(entitytype: entityType, entityid: entityId);
    await budgetCntrl.fetchBudgetRecords(entitytype: entityType, entityid: entityId);
  }

  void _scrollToTop() {
   budgetCntrl.scrollController.animateTo(
      0.0, // Scroll to the top (position 0.0)
      duration: const Duration(milliseconds: 300), // Animation duration
      curve: Curves.easeInOut, // Animation curve
    );
  }

  @override
  Widget build(BuildContext context) {
  double? previousScrollPosition;
    bool isUserScrolling = false;

    // Clear any existing listeners to avoid duplicates
    budgetCntrl.scrollController.removeListener(() {});
    
    // Add refined scroll listener
    budgetCntrl.scrollController.addListener(() {
      // Detect if the user is actively scrolling
      if (budgetCntrl.scrollController.position.isScrollingNotifier.value) {
        isUserScrolling = true;
      }

      // Only trigger load more if user has scrolled and is near the bottom
      if (isUserScrolling &&
          budgetCntrl.scrollController.position.pixels >=
              budgetCntrl.scrollController.position.maxScrollExtent - 200 &&
          !budgetCntrl.isLoadingMore.value &&
          budgetCntrl.canLoadMore &&
          budgetCntrl.scrollController.position.maxScrollExtent > 0) {
        previousScrollPosition = budgetCntrl.scrollController.position.pixels;
        budgetCntrl
            .fetchBudgetRecords(
                entitytype: entityType, entityid: entityId, isLoadMore: true)
            .then((_) {
          if (previousScrollPosition != null &&
              budgetCntrl.scrollController.hasClients) {
            budgetCntrl.scrollController.jumpTo(previousScrollPosition!);
          }
        });
      }
    });
  
  // budgetCntrl.scrollController.addListener(() {
  //     if (budgetCntrl.scrollController.position.pixels >=
  //             budgetCntrl.scrollController.position.maxScrollExtent - 200 &&
  //         !budgetCntrl.isLoadingMore.value &&
  //         budgetCntrl.canLoadMore) {
  //       // Save the current scroll position before loading more
  //       previousScrollPosition = budgetCntrl.scrollController.position.pixels;
  //       // Fetch more records
  //       budgetCntrl.fetchBudgetRecords(entitytype: entityType, entityid: entityId,isLoadMore: true).then((_) {
  //         // Restore the scroll position after new records are added
  //         if (previousScrollPosition != null &&
  //             budgetCntrl.scrollController.hasClients) {
  //           budgetCntrl.scrollController.jumpTo(previousScrollPosition!);
  //         }
  //       });
  //     }
  //   });



    return Obx(
      () => budgetCntrl.labelsloading.value
          ? ComponentUtils.labelLoadScaffold()
          : Scaffold(
              appBar: AppBar(
                iconTheme: Theme.of(context).appBarTheme.iconTheme,
                title: Text(
                  hdrTxt ?? talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS')?.value ?? '',
                  style: ComponentUtils.appbartitlestyle,
                ),
                actions: <Widget>[
                  IconButton(
                    color: ComponentUtils.primecolor,
                    icon: const Icon(Icons.swap_horizontal_circle),
                    onPressed: () {
                      budgetCntrl.tblflag.toggle();
                      budgetCntrl.tblflag.value ? _scrollToTop() : null;
                    },
                  ),
                ],
                backgroundColor: Colors.white,
                elevation: 5,
                leading: new IconButton(
                  icon: ComponentUtils.backpageIcon,
                  color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                  onPressed: () {
                    debugPrint('------back-------');
                    Get.back();
                  },
                ),
              ),
              body: Column(
                children: [
                  TaSearchInputText(
                      makeSearch: (searchtext) async {
                        //await budgetCntrl.loadEntityBudget(entitytype: entityType, entityid: entityId, searchtext: searchtext.toString());
                     await budgetCntrl.fetchBudgetRecords(entitytype: entityType, entityid: entityId, searchtext: searchtext.toString());

                      },
                      searchController: budgetCntrl.bdgtSrchTxt,
                      hintSearch: 'Search '),
                  budgetDataTable()
                ],
              ),
            ),

      // },
    );
  }

  Widget budgetDataTable() {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshBudgetData,
        child: GetX<BudgetController>(
          initState: (state) async {
            await budgetCntrl.fetchBudgetRecords(entitytype: entityType, entityid: entityId, init: 'init');
          },
          builder: (ctrl) {
            return ctrl.isload.isTrue
                ? const ProgressIndicatorCust()
                : ctrl.budgetRecords.length < 1
                    ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                    : (budgetCntrl.tblflag.value
                        ? 
                        TaTable(
                          scrollController: budgetCntrl.scrollController,
                            columns: 
                             budgetCntrl.columnList.value
                            ,
                            rows:[...getRows(budgetCntrl.budgetRecords),
                            if(budgetCntrl.canLoadMore)

                             DataRow(cells: budgetCntrl.cellsList.value
                            ),  
                             ],
                          )
                        : ListView.builder(
                            controller: budgetCntrl.scrollController,
                            itemCount: ctrl.budgetRecords.length + (budgetCntrl.canLoadMore ? 1 : 0),
                            itemBuilder: (context, index) {
                              if(index == budgetCntrl.budgetRecords.length && budgetCntrl.canLoadMore){
                                return const ProgressIndicatorCust();
                                }                                                        
                              return listitem(context, ctrl.budgetRecords[index]);                            
                            },
                            
                          ));
          },
        ),
      ),
    );
  }



  List<DataColumn> getColumns(List<String> columns) => columns
      .map((String column) => DataColumn(
            label: Text(column),
            // onSort: onSort,
          ),)
      .toList();

  List<DataRow> getRows(List<BudgetRecord> budgetdata) => budgetdata.map((BudgetRecord bgt) {
        final cells = [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM') != null) bgt.lineItemSeq,
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE') != null) bgt.taskTypeDesc ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION') != null) bgt.divisionDesc ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME') != null) bgt.subTaskName ?? '',
          //bgt.taskName ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET') != null)
            ComponentUtils.convertNumberFormat(bgt.initialBudget?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET') != null)
            ComponentUtils.convertNumberFormat(bgt.approvedBudget?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST') != null)
            ComponentUtils.convertNumberFormat(bgt.estimatedCost?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT') != null)
            ComponentUtils.convertNumberFormat(bgt.commitment?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST') != null)
            ComponentUtils.convertNumberFormat(bgt.actualCost?.toString() ?? '0.0')
        ];
        List<DataCell> getCells(List<dynamic> cells) => cells.map((data) => DataCell(Text('$data'))).toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  Widget listitem(BuildContext context, BudgetRecord bgt) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      padding: EdgeInsets.only(right: 10, left: 10, top: 5),
      child: Column(
        children: [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM')!.value!, bgt.lineItemSeq.toString() ?? '0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE')!.value!, bgt.taskTypeDesc ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION')!.value!, bgt.divisionDesc ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME')!.value!, bgt.subTaskName ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET')!.value!, bgt.initialBudget?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET') != null)
            // ComponentUtils.listVertRow('Amount', bgt.initialApprovedBudget.toString ?? '0.0'),
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET')!.value!, bgt.approvedBudget?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST')!.value!, bgt.estimatedCost?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT')!.value!, bgt.commitment?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST')!.value!, bgt.actualCost?.toString() ?? '0.0',
                dtype: 'num'),
        ],
      ),
    );
  }
}
