import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/milestone.dart';
import 'package:tangoworkplace/providers/common/entitymilestones_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/budget_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/milestone/milestone_details.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/budget/budget_data.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/budget/budget_summary.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/budget/co_data.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/budget/invoice_data.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/budget/po_data.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class BudgetOverview extends GetView<EntityMilestonesController> {
  final String? entityType;
  final int? entityId;
  String? mode;
  BudgetOverview({Key? key, this.entityType, this.entityId, this.mode}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  BudgetController budgetCntrl = Get.find<BudgetController>();

  Future _refreshBudgetsnapshot() async {
    budgetCntrl.loadBudgetSnapshot(entityId);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await budgetCntrl.getlabels(talabel);
            });
          },
          builder: (_) {
            return budgetCntrl.labelsloading.value
                ? const ProgressIndicatorCust()
                : RefreshIndicator(
                    onRefresh: _refreshBudgetsnapshot,
                    child: GetX<BudgetController>(
                      initState: (state) {
                        budgetCntrl.loadBudgetSnapshot(entityId);
                      },
                      builder: (_) {
                        return _.isloading.isTrue
                            ? ProgressIndicatorCust()
                            : _.budgetsumsdata == null
                                ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                                : SingleChildScrollView(
                                    // padding: EdgeInsets.only(top: 10),
                                    child: groupdata(),
                                  );
                      },
                    ),
                  );
          },
        ),
      ),
    ]);
  }

  Widget groupdata() {
    return Column(
      children: [
        budgetSnapshotdata(),
        SizedBox(
          height: 1,
        ),
        subEntityTabs(),
      ],
    );
  }

  Widget budgetSnapshotdata() {
    var b = budgetCntrl.budgetsumsdata.value;
    return Container(
      margin: EdgeInsets.all(10.0),
      constraints: BoxConstraints(maxWidth: 500),
      padding: EdgeInsets.all(10.0),
      // height: 200,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Budget Overview',
                style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 16),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_APRVBUDGET') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_APRVBUDGET')!.value!, b!.sumApprovedBudget?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_COMMITTEDBUDGET') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_COMMITTEDBUDGET')!.value!, b!.sumComittedCost?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_ACTUALBUDGET') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_ACTUALBUDGET')!.value!, b!.sumActualCost?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_ESTIMATEDBUDGET') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_ESTIMATEDBUDGET')!.value!, b!.sumEstimatedCost?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_ACTUALRCVD') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_ACTUALRCVD')!.value!, b!.sumActualRcvd?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_EARNEDAMT') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_EARNEDAMT')!.value!, b!.sumEarnedAmount?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_AVAILBLETOSPEND') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_AVAILBLETOSPEND')!.value!, b!.sumAvailableToSpend?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_APRVCOMMITMENT') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_APRVCOMMITMENT')!.value!, b!.approvedCommitment?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INITAPRVDVSFORECAST') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INITAPRVDVSFORECAST')!.value!, b!.intitialApprovedVsForecast?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_APPRVDPO') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_APPRVDPO')!.value!, b!.sumApprovedPo?.toString() ?? '0.0',
                dtype: 'num'),
        ],
      ),
    );
  }

  Widget subEntityTabs() {
    return Container(
      margin: EdgeInsets.only(top: 5, left: 10, right: 10, bottom: 10),
      constraints: BoxConstraints(maxWidth: 500),
      padding: EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 20),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
      child: GridView.count(
        padding: EdgeInsets.only(bottom: 5.0, top: 7.0),
        crossAxisCount: 2,
        //childAspectRatio: 1.0,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 3.5,

        shrinkWrap: true,
        //padding: EdgeInsets.only(left: 10, right: 10),
        children: <Widget>[
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS') != null)
            subEntity(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS')!.value!, 'BUDGET', Icons.monetization_on),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY') != null)
            subEntity(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY')!.value!, 'SUM', Icons.summarize),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO') != null)
            subEntity(talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO')!.value!, 'PO', Icons.feed),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO') != null)
            subEntity(talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO')!.value!, 'CO', Icons.sell),
          //subEntity('EWA', 'EWA', Icons.support),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE') != null)
            subEntity(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE')!.value!, 'INV', Icons.receipt_long),
        ],
      ),
    );
  }

  Widget subEntity(String seName, String tab, IconData iconData) {
    final primary = ComponentUtils.primary;

    return GestureDetector(
      onTap: () {
        if (tab == 'BUDGET') {
          Get.to(() => BudgetData(
                entityId: entityId,
                entityType: entityType,
                source: 'PROJ-BUDGET-DET',
              ));
        } else if (tab == 'PO') {
          Get.to(() => Podata(
                entityid: entityId,
              ));
        } else if (tab == 'CO') {
          Get.to(() => Codata(
                entityid: entityId,
              ));
        } else if (tab == 'INV') {
          Get.to(() => Invoicedata(
                entityid: entityId,
              ));
        } else if (tab == 'SUM') {
          Get.to(() => BudgetSummary(
                entityid: entityId,
              ));
        }
      },
      child: Center(
        child: Container(
          width: 150,
          height: 70,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black26),
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey,
                blurRadius: 5,
                //offset: Offset(0, 10),
              ),
            ],
          ),
          child: Container(
            child: Row(
              children: [
                SizedBox(width: 5),
                Icon(
                  iconData,
                  color: ComponentUtils.primecolor,
                  size: 25,
                ),
                SizedBox(width: 10),
                Text(
                  seName,
                  style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
