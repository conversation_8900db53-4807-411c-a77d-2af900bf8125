import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tatable.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/project/budget/invhdr.dart';
import 'package:tangoworkplace/models/project/budget/pohdr.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/invoice_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/po_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class Invoicedata extends StatelessWidget {
  final int? entityid;
  Invoicedata({Key? key, this.entityid}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  InvoiceController invCtrl = Get.put(InvoiceController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE')?.value ?? '',
              style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
          actions: <Widget>[
            IconButton(
              color: ComponentUtils.primecolor,
              icon: const Icon(Icons.swap_horizontal_circle),
              onPressed: () {
                invCtrl.tblflag.toggle();
              },
            ),
          ],
          backgroundColor: Colors.white,
          elevation: 5,
          leading: IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
            onPressed: () {
              debugPrint('------back-------');
              invCtrl.invoiceSrchTxt.text = '';
              Get.back();
            },
          ),
        ),
        body: Column(
          children: [
            TaSearchInputText(
                makeSearch: (searchtext) async {
                  await invCtrl.loadINVheaderData(projectid: entityid, searchtext: searchtext.toString());
                },
                searchController: invCtrl.invoiceSrchTxt,
                hintSearch: 'Search '),
            invtable()
          ],
        ),
      ),
    );
  }

  Future _refreshPoData() async {
    invCtrl.loadINVheaderData(projectid: entityid);
  }

  Widget invtable() {
    return Expanded(
      child: RefreshIndicator(
        onRefresh: _refreshPoData,
        child: GetX<InvoiceController>(
          initState: (state) {
            invCtrl.loadINVheaderData(projectid: entityid);
          },
          builder: (ctrl) {
            return ctrl.ishdrloading.isTrue
                ? ProgressIndicatorCust()
                : ctrl.invdata.length < 1
                    ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                    : (invCtrl.tblflag.value
                        ? TaTable(
                            columns: [
                              if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVID') != null)
                                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVID')!.value,
                              if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVNUM') != null)
                                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVNUM')!.value,
                              if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_POID') != null)
                                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_POID')!.value,
                              if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_STATUS') != null)
                                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_STATUS')!.value,
                              if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_ISSUEDATE') != null)
                                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_ISSUEDATE')!.value,
                              if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_AMOUNT') != null)
                                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_AMOUNT')!.value,
                              if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_SUPPLIER') != null)
                                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_SUPPLIER')!.value,
                            ],
                            rows: getRows(invCtrl.invdata),
                          )
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return listitem(context, ctrl.invdata[index]);
                            },
                            itemCount: ctrl.invdata.length,
                          ));
          },
        ),
      ),
    );
  }

  List<DataRow> getRows(List<InvHdr> invdata) => invdata.map((InvHdr inv) {
        final cells = [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVID') != null) inv.invoiceId.toString() ?? '0',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVNUM') != null) inv.invoiceNumber ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_POID') != null) inv.poId.toString() ?? '0',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_STATUS') != null) inv.statusDesc ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_ISSUEDATE') != null) inv.invDate ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_AMOUNT') != null)
            ComponentUtils.convertNumberFormat(inv.totalAmount?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_SUPPLIER') != null) inv.supplierName ?? ''
        ];
        List<DataCell> getCells(List<dynamic> cells) => cells.map((data) => DataCell(Text('$data'))).toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  Widget listitem(BuildContext context, InvHdr inv) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      padding: EdgeInsets.only(right: 10, left: 10, top: 5),
      child: Column(
        children: [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVID') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVID')!.value!, inv.invoiceId.toString() ?? '0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVNUM') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_INVNUM')!.value!, inv.invoiceNumber ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_POID') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_POID')!.value!, inv.poId.toString() ?? '0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_STATUS') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_STATUS')!.value!, inv.statusDesc ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_ISSUEDATE') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_ISSUEDATE')!.value!, inv.invDate ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_AMOUNT') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_AMOUNT')!.value!, inv.totalAmount?.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_SUPPLIER') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_INVOICE_SUPPLIER')!.value!, inv.supplierName ?? ''),
        ],
      ),
    );
  }
}
