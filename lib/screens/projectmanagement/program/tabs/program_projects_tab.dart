import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/program/program_projects_data.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/models/project/statusreport/statusreport.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/statusreport/statusreport_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/statusreport/statusreport_details_screen.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';
import '../../../../providers/projectmanagement/program/programprojects_controller.dart';

class ProgramProjects extends StatelessWidget {
  final programId;
  ProgramProjects({Key? key, this.programId}) : super(key: key);
  LabelController talabel = Get.find<LabelController>();
  ProgramProjectsController prgProjCtrl = Get.find<ProgramProjectsController>();

  @override
  Widget build(BuildContext context) {
    return listStack(context);
  }

  Future _refreshStatusReports() async {
    await prgProjCtrl.loadProgramProjectsData(programId);
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      Positioned.fill(
        //top: 45,
        child: RefreshIndicator(
          onRefresh: _refreshStatusReports,
          child: GetX<ProgramProjectsController>(
            initState: (state) {
              prgProjCtrl.loadProgramProjectsData(programId);
              Future.delayed(Duration(seconds: 0), () async {
                await prgProjCtrl.getlabels(talabel);
                prgProjCtrl.isLoading.value = false;
              });
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? ProgressIndicatorCust()
                  : _.programProjectsList.length < 1
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.programProjectsList[index]);
                          },
                          itemCount: _.programProjectsList.length,
                        );
            },
          ),
        ),
      ),
      // if (mode == 'edit')
      // Positioned(
      //   bottom: 30,
      //   right: 30,
      //   child: FloatingActionButton(
      //     elevation: 5.0,
      //     child: const Icon(
      //       Icons.add,
      //       size: 30,
      //     ),
      //     onPressed: () {
      //       debugPrint('add button----------------------');
      //       statusrepoCtrl.statusreport.value = StatusReport(projectId: entityid);
      //       statusrepoCtrl.sr_mode.value = 'create';
      //       Get.to(() => StatusReportDetails(
      //             projectid: entityid,
      //             projectview: projectview,
      //             mode: mode,
      //           ));
      //     },
      //   ),
      // ),
    ]);
  }

  Widget listitem(BuildContext context, ProgramProjectsData pp) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      // onTap: () {

      //   Get.to(() => StatusReportDetails(
      //         projectid: entityid,
      //         mode: mode,
      //       ));
      // },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                pp.projectName ?? '',
                style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
              ),
              const SizedBox(
                height: 6,
              ),
              ComponentUtils.listVertRow('Project Id', pp.projectId?.toString() ?? ''),
              ComponentUtils.listVertRow('Status', pp?.statusDesc ?? ''),
              ComponentUtils.listVertRow('Projected Open Date', pp.projectedOpenDate?.toString() ?? ''),
              ComponentUtils.listVertRow('Communicated Open Date', pp.communicatedOpenDate?.toString() ?? ''),
            ],
          ),
        ),
      ),
    );
  }
}
