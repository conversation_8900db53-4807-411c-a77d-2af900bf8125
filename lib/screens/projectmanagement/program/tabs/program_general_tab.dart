import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../models/program/programs_search_data.dart';
import '../../../../providers/projectmanagement/program/programgeneral_controller.dart';

class ProgramGeneral extends GetView<ProgramGeneralController> {
  final ProgramsSearchData? prg;
  String? mode;
  ProgramGeneral({Key? key, this.prg, this.mode}) : super(key: key);

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  ProgramGeneralController genCtrl = Get.find<ProgramGeneralController>();

  @override
  Widget build(BuildContext context) {
    debugPrint('${prg!.toJson()}');
    genCtrl.labelsloading.value = true;

    return Stack(children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await genCtrl.getlabels(talabel);
            });
          },
          builder: (_) {
            return genCtrl.labelsloading.value ? ProgressIndicatorCust() : generalTab(prg);
          },
        ),
      ),
    ]);
  }

  Widget generalTab(ProgramsSearchData? p) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGRAMID') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGRAMID')!.value,
              value: p!.programId?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGRAMNAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGRAMNAME')!.value,
              value: p!.programName?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGDESC') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGDESC')!.value,
              value: p!.programDesc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_SPONSOR') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_SPONSOR')!.value,
              value: p!.sponsor?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_STATUS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_STATUS')!.value,
              value: p!.statusDesc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_STARTDATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_STARTDATE')!.value,
              value: p!.startDate?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ENDDATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ENDDATE')!.value,
              value: p!.endDate?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_BUDGETYEAR') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_BUDGETYEAR')!.value,
              value: p!.budgetYear?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_APPROVEDBUDGET') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_APPROVEDBUDGET')!.value,
              value: p!.approvedBudget?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_APPROVALDATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_APPROVALDATE')!.value,
              value: p!.approvalDate?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_BUDGETALLOCATED') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_BUDGETALLOCATED')!.value,
              value: p!.budgetAllocated?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_CAPITALBUDGET') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_CAPITALBUDGET')!.value,
              value: p!.capitalBudget?.toString() ?? '',
              readOnly: true,
            ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_CMPLTDBUDGET') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_CMPLTDBUDGET')!.value,
          //     value: p!.cmpltdBudget.toString()?? '',
          //     readOnly: true,
          //   ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ACTUALSPENT') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ACTUALSPENT')!.value,
              value: p!.actualSpent.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_COMMITMENTS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_COMMITMENTS')!.value,
              value: p!.commitments.toString() ?? '',
              readOnly: true,
            ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROJECTNUM') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROJECTNUM')!.value,
          //     value: p!.projectNumber?.toString(),
          //     readOnly: true,
          //   ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROJECTTYPE') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROJECTTYPE')!.value,
          //     value: p!.projectTypeDesc?.toString(),
          //     readOnly: true,
          //   ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ADDRESS') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ADDRESS')!.value,
          //     value: p!.entityAddress.toString(),
          //     readOnly: true,
          //   ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ENTITYTYPE') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_ENTITYTYPE')!.value,
          //     value: p!.entityType?.toString(),
          //     readOnly: true,
          //   ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_OWNERSHIPTYPE') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_OWNERSHIPTYPE')!.value,
          //     value: p!.ownershipType?.toString(),
          //     readOnly: true,
          //   ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_STATUS') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_STATUS')!.value,
          //     value: p!.statusDesc?.toString(),
          //     readOnly: true,
          //   ),
          // if (talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGRAM') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_PROGRAMS_GENERAL_PROGRAM')!.value,
          //     value: p!.programName?.toString(),
          //     readOnly: true,
          //   ),
        ],
      ),
    );
  }
}
