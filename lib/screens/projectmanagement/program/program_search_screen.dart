import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/status_meter_gauge.dart';

import 'package:tangoworkplace/providers/projectmanagement/project/projectsearch_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/projectmanagement/program/program_details_screen.dart';
import '../../../common/component_utils.dart';
import '../../../models/program/programs_search_data.dart';
import '../../../providers/projectmanagement/program/programsearch_controller.dart';
import '../../common/utils/entityerrorpg.dart';

class ProgramSearch extends StatelessWidget {
  String? navFrom;
  ProgramSearch({this.navFrom, super.key});
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  ProgramSearchController programSearchState = Get.put(ProgramSearchController());
  ProgramSearchController programSearchCtrlr = Get.find<ProgramSearchController>();
  ProjectSearchController projectSearchState = Get.put(ProjectSearchController());
  ProjectSearchController projectSearchCtrlr = Get.find<ProjectSearchController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshPrograms() async {
    programSearchCtrlr.fetchPrograms();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Obx(
          () => Text(talabel.get('TMCMOBILE_HOME_PROGRAMS')!.value!,
              style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
        ),
        // actions: [
        //   IconButton(
        //     color: ComponentUtils.primecolor,
        //     icon: const Icon(Icons.my_location),
        //     onPressed: () async {
        //       Position p = await _determinePosition();
        //       debugPrint('latitude     ${p.latitude}');
        //     },
        //   ),
        // ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            //Get.off(() => HomeScreen());

            Get.back();
          },
        ),
        //actions: [],
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          TaSearchInputText(
              makeSearch: (searchtext) {
                programSearchCtrlr.fetchPrograms();
              },
              searchController: programSearchCtrlr.searchCtrl,
              hintSearch: 'Search '),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshPrograms,
              child: GetX<ProgramSearchController>(
                initState: (state) async {
                  await programSearchCtrlr.fetchPrograms();
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.programs.length < 1
                          ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.programs[index]);
                              },
                              itemCount: _.programs.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, ProgramsSearchData p) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () async {
          String mode = await projectSearchCtrlr.getUserEntityEditAccess(entityid: p.programId.toString(), entitytype: 'PROGRAM');

          if (mode == 'error') {
            Get.to(
              () => EntityErrorPg(
                entitytype: 'Program',
                entityid: p.programId,
                entityname: p.programName,
              ),
            );
          } else {
            Get.to(
                () => ProgramDetails(
                      prg: p,
                      parent_mode: mode,
                      tabroles: projectSearchCtrlr.tabroles.value,
                    ),
                arguments: [
                  {"programId": p.programId},
                  {"programview": p}
                ]);
          }
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${p.programName ?? ''}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    ComponentUtils.listVertRow('Program Year', p.programYear?.toString() ?? ''),
                    ComponentUtils.listVertRow('Budget Year', p.budgetYear?.toString() ?? ''),
                    ComponentUtils.listVertRow('Status', p.statusDesc?.toString() ?? ''),
                    ComponentUtils.listVertRow('Sponsor', p.sponsor?.toString() ?? ''),
                    ComponentUtils.listVertRow('Approval Date', p.approvalDate?.toString() ?? ''),
                    ComponentUtils.listVertRow('End Date', p.endDate?.toString() ?? ''),
                    ComponentUtils.listVertRow('Capital Budget', p.capitalBudget?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('Approved Budget', p.approvedBudget?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('Committed Cost', p.commitments?.toString() ?? '0.00', dtype: 'num'),
                    ComponentUtils.listVertRow('Remaining Budget', p.remainingBudget?.toString() ?? '0.00', dtype: 'num'),
                    //ComponentUtils.listVertRow('Completed budget', p.cmpltdBudget?.toString() ?? ''),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(left: 20, right: 60),
                          child: Text(
                            '% of  Budget',
                            style: TextStyle(
                                color: primary, //fontWeight: FontWeight.bold,
                                fontSize: 12),
                          ),
                        ),
                        p.cmpltdBudget! > 0
                            ? Container(
                                height: 50,
                                child: StatusMeterGauge(
                                  size: 50.0,
                                  statusvalue: (p.cmpltdBudget)! > 0 ? (p.cmpltdBudget)! / 100 : 0,
                                ),
                              )
                            : Container(
                                height: 50,
                                child: StatusMeterGauge(
                                  size: 50.0,
                                  statusvalue: 0.0001,
                                ),
                              ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
