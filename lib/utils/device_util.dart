import 'package:flutter/material.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

class DeviceUtils {
  static double taFontSize(double multiplier, BuildContext ctx) {
    double unitHeightValue = MediaQuery.of(ctx).size.height * 0.01;

    return multiplier * unitHeightValue;
  }

  static Size screenMQ(BuildContext ctx) {
    return MediaQuery.of(ctx).size;
  }
}

class Device extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  const Device({
    Key? key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  }) : super(key: key);

  static bool isMobile(BuildContext context) => MediaQuery.of(context).size.width < 800;

  static bool isTablet(BuildContext context) => MediaQuery.of(context).size.width >= 800;
  //&& MediaQuery.of(context).size.width < 1200;

  //static bool isDesktop(BuildContext context) => MediaQuery.of(context).size.width >= 1200;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200) {
          return desktop;
        } else if (constraints.maxWidth >= 800) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}

// enum DeviceSize { mobile, tablet, desktop }

// extension LayoutUtils on BoxConstraints {
//   /// returns DeviceSize
//   DeviceSize get device {
//     if (this.maxWidth >= 720.0) {
//       return DeviceSize.desktop;
//     }
//     if (this.maxWidth >= 600.0) {
//       return DeviceSize.tablet;
//     }
//     return DeviceSize.mobile;
//   }
// }

// bool get isWeb => kIsWeb;

// bool get isMobile => !isWeb && (Platform.isIOS || Platform.isAndroid);

// bool get isDesktop => !isWeb && (Platform.isMacOS || Platform.isWindows || Platform.isLinux);

// bool get isApple => !isWeb && (Platform.isIOS || Platform.isMacOS);

// bool get isGoogle => !isWeb && (Platform.isAndroid || Platform.isFuchsia);

// bool get isAndroid => !isWeb && Platform.isAndroid;

// bool get isIos => !isWeb && Platform.isIOS;

// bool get isMacOS => !isWeb && Platform.isMacOS;

// bool get isLinux => !isWeb && Platform.isLinux;

// bool get isWindows => !isWeb && Platform.isWindows;

// String get operatingSystemName => Platform.operatingSystem;

// String get operatingSystemVersion => Platform.operatingSystemVersion;

class HexColor extends Color {
  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    if (hexColor.length == 6) {
      hexColor = "FF" + hexColor;
    }
    return int.parse(hexColor, radix: 16);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));
}

ThemeData TaLightTheme() {
  return ThemeData(
    primarySwatch: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
    primaryColor: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
    appBarTheme: AppBarTheme(
      backgroundColor: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
    ),
    scaffoldBackgroundColor: CommonUtils.createMaterialColor(Color(0XFFf2f2f2)),
  );
  // TextTheme _basicTextTheme(TextTheme base) {
  //   return base.copyWith(
  //       headline: base.headline.copyWith(
  //         fontFamily: 'Roboto',
  //         fontSize: 22.0,
  //         color: Colors.black,
  //       ),
  //       title: base.title.copyWith(
  //         fontFamily: 'Merriweather',
  //         fontSize: 15.0,
  //         color: Colors.green
  //       ),
  //       display1: base.headline.copyWith(
  //         fontFamily: 'Roboto',
  //         fontSize: 24.0,
  //         color: Colors.white,
  //       ),
  //       display2: base.headline.copyWith(
  //         fontFamily: 'Merriweather',
  //         fontSize: 22.0,
  //         color: Colors.grey,
  //       ),
  //       caption: base.caption.copyWith(
  //         color: Color(0xFFCCC5AF),
  //       ),
  //       body1: base.body1.copyWith(color: Color(0xFF807A6B)));
  // }

  // CheckboxTheme _tacheckBox(CheckboxTheme base){
  //   return base.c;
  // }

  // final ThemeData base = ThemeData.light();

  // return base.copyWith(
  //   // textTheme: _basicTextTheme(base.textTheme),
  //   //textTheme: Typography().white,

  //   primaryColor: HexColor('#B10C00'),
  //   appBarTheme: AppBarTheme(
  //     backgroundColor: HexColor('#B10C00'),
  //   ),

  //   //primaryColor: Color(0xff4829b2),
  //   indicatorColor: HexColor('#B10C00'),
  //   scaffoldBackgroundColor: HexColor('#f2f2f2'),

  //   //accentColor: Color(0xFFFFF8E1),
  //   // iconTheme: IconThemeData(
  //   //   color: Colors.white,
  //   //   size: 20.0,
  //   // ),
  //   // buttonColor: Colors.white,
  //   // backgroundColor: Colors.white,
  //   // tabBarTheme: base.tabBarTheme.copyWith(
  //   //   labelColor: Color(0xffce107c),
  //   //   unselectedLabelColor: Colors.grey,
  //   // )
  // );
}
