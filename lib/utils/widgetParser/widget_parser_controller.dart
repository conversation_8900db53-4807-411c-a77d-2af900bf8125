import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/utils/widgetParser/json_widget.dart';
import 'package:tangoworkplace/utils/widgetParser/json_widget_types.dart';

class WidgetParserController extends GetxController {
  RxMap<String, Object> jsonData = <String, Object>{}.obs;
  RxList<JsonWidget> jwidgetList = <JsonWidget>[].obs;
  var isLoading = false.obs;
  @override
  void onInit() {
    super.onInit();
    isLoading.value = true;
    getWidgetJsonData();
    isLoading.value = false;
  }

  Map<String, Object> json = {
    "widgetType": "scaffold",
    "appBar": {"widgetType": "appBar"},
    "body": {
      "widgetType": "column",
      "mainAxisAligment": "start",
      "crossAxisAligment": "start",
      "mainAxisSize": "max",
      "children": [
        {"widgetType": "textView", "text": "HELLO THIS FIRST TEXT WIDGET", "color": "0XFFb10c00"},
        {"widgetType": "textView", "text": "THIS SECOND TEXT WIDGET", "fontSize": 32},
        {"widgetType": "taInput", "text": "Enter text", "value": "Type something", "readOnly": false},
        {"widgetType": "taInput", "text": "Enter text", "value": "Type something", "readOnly": false},
        {
          "widgetType": "taDropDown",
          "text": "Select value",
          "value": 2,
          "readOnly": false,
          "dropDownValues": [
            {
              "widget": {"widgetType": "textView", "text": "HELLO 1"},
              "value": 1
            },
            {
              "widget": {"widgetType": "textView", "text": "HELLO 2", "color": "0XFFcccc00"},
              "value": 2
            },
            {
              "widget": {"widgetType": "textView", "text": "THIS IS 3", "color": "0XFFb10ff0"},
              "value": 3
            },
            {
              "widget": {"widgetType": "textView", "text": "FOUR"},
              "value": 4
            }
          ]
        },
        {"widgetType": "taCheckBox", "text": "Check me!", "value": false}
      ]
    }
  };

  final List<TextEditingController> textEditControllers = [];

  Widget parseJson() {
    JsonWidget widgetTree = JsonWidget.fromJson(json);
    return buildWidget(widgetTree);
  }

  Widget buildWidget(JsonWidget jsonWidget) {
    debugPrint('Building widget of type: ${jsonWidget.widgetType}');
    final widgetType = JsonWidgetTypeHelper.getType(jsonWidget.widgetType ?? 'text');

    return _buildTextWidget(jsonWidget);
    // switch (widgetType) {
    //   case JsonWidgetType.scaffold:
    //     return _buildScaffoldWidget(jsonWidget);
    //   case JsonWidgetType.textView:
    //     return _buildTextWidget(jsonWidget);
    //   case JsonWidgetType.row:
    //     return _buildRowWidget(jsonWidget);
    //   case JsonWidgetType.column:
    //     return _buildColumnWidget(jsonWidget);
    //   case JsonWidgetType.taInput:
    //     return _buildTaInput(jsonWidget);
    //   case JsonWidgetType.taDropDown:
    //     return _buildTaDropDown(jsonWidget);
    //   case JsonWidgetType.taCheckBox:
    //     return _buildTaCheckBox(jsonWidget);
    //   default:
    //     return _buildAppBarWidget(jsonWidget);
    // }
  }

  Widget _buildTextWidget(JsonWidget jsonWidget) {
    return Text(
      jsonWidget.text ?? '',
      style: TextStyle(
          fontSize: jsonWidget.fontSize?.toDouble(),
          color: jsonWidget.color != null ? Color(int.parse(jsonWidget.color!)) : null),
    );
  }

  Widget _buildColumnWidget(JsonWidget jsonWidget) {
    final crossAxisAligment = CrossAxisAlignment.values.firstWhereOrNull(
      (element) => element.name.toLowerCase() == jsonWidget.crossAxisAligment?.toLowerCase(),
    );
    final mainAxisAligment = MainAxisAlignment.values.firstWhereOrNull(
      (element) => element.name.toLowerCase() == jsonWidget.mainAxisAligment?.toLowerCase(),
    );
    final mainAxisSize = MainAxisSize.values.firstWhereOrNull(
      (element) => element.name.toLowerCase() == jsonWidget.mainAxisSize?.toLowerCase(),
    );
    return jsonWidget.children != null
        ? Column(
            mainAxisAlignment: mainAxisAligment ?? MainAxisAlignment.start,
            crossAxisAlignment: crossAxisAligment ?? CrossAxisAlignment.start,
            mainAxisSize: mainAxisSize ?? MainAxisSize.min,
            children: jsonWidget.children!.map((e) => buildWidget(e)).toList(),
          )
        : Container();
  }

  Widget _buildRowWidget(JsonWidget jsonWidget) {
    final crossAxisAligment = CrossAxisAlignment.values.firstWhereOrNull(
      (element) => element.name.toLowerCase() == jsonWidget.crossAxisAligment?.toLowerCase(),
    );
    final mainAxisAligment = MainAxisAlignment.values.firstWhereOrNull(
      (element) => element.name.toLowerCase() == jsonWidget.mainAxisAligment?.toLowerCase(),
    );
    final mainAxisSize = MainAxisSize.values.firstWhereOrNull(
      (element) => element.name.toLowerCase() == jsonWidget.mainAxisSize?.toLowerCase(),
    );
    return jsonWidget.children != null
        ? Row(
            mainAxisAlignment: mainAxisAligment ?? MainAxisAlignment.start,
            crossAxisAlignment: crossAxisAligment ?? CrossAxisAlignment.start,
            mainAxisSize: mainAxisSize ?? MainAxisSize.min,
            children: jsonWidget.children!.map((e) => buildWidget(e)).toList(),
          )
        : Container();
  }

  Widget _buildScaffoldWidget(JsonWidget jsonWidget) {
    return jsonWidget.body != null
        ? Scaffold(
            appBar: jsonWidget.appBar != null ? buildWidget(jsonWidget.appBar!) as AppBar? : null,
            body: buildWidget(jsonWidget.body!))
        : Container();
  }

  AppBar _buildAppBarWidget(JsonWidget jsonWidget) {
    return AppBar(
      title: Text(jsonWidget.text ?? ''),
      backgroundColor: Colors.white,
      elevation: 5,
    );
  }

  Widget _buildTaCheckBox(JsonWidget jsonWidget) {
    return TaCheckBox(
      label: jsonWidget.text,
      value: jsonWidget.value is bool ? (jsonWidget.value ?? false) : false,
    );
  }

  Widget _buildTaDropDown(JsonWidget jsonWidget) {
    return TaFormDropdown(
      listflag: true,
      emptttext: '',
      label: jsonWidget.text,
      readonly: jsonWidget.readOnly ?? false,
      value: jsonWidget.value != null ? jsonWidget.value ?? '' : null,
      items: jsonWidget.dropDownValues != null
          ? jsonWidget.dropDownValues
              ?.map((element) => DropdownMenuItem(
                    value: element.value,
                    child: buildWidget(element.widget),
                  ))
              .toList()
          : <DropdownMenuItem>[],
      onChanged: (_) {},
      onSaved: (_) {},
    );
  }

  Widget _buildTaInput(JsonWidget jsonWidget) {
    return TaInputText(
      title: jsonWidget.text,
      value: jsonWidget.value is String ? (jsonWidget.value ?? '') : '',
      readOnly: jsonWidget.readOnly ?? false,
    );
  }

  void generateValueObserver() {}

  Future<void> getWidgetJsonData() async {
    try {
      var filter;
      Map<String, dynamic> dynamicMap = {
        'a': ['site_id', 'EXACT', '196819'],
      };
      filter = CommonServices.getFilter(dynamicMap);
      var url = dynamicattributesurl + '/SITE/DYNAMIC_ATTRIBUTES';

      var resMap = await ApiService.post(url, payloadObj: filter);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        debugPrint('status>>>>>$status');
        if (status == 0) {
          var tagObjsJson = resMap['componentList'] as List?;
          if (tagObjsJson != null && tagObjsJson.isNotEmpty) {
            tagObjsJson.map((e) => buildWidget(e)).toList();
            debugPrint('Widget JSON data fetched successfully');
          } else {
            debugPrint('No records found in the response');
          }
        } else {
          debugPrint('Error fetching widget JSON data: $status');
        }
      }
    } catch (e) {
      debugPrint('Error fetching widget JSON data: $e');
    }
  }
}
