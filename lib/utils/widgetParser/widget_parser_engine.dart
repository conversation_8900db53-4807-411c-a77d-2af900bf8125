import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/utils/widgetParser/widget_parser_controller.dart';

import '../../common/progess_indicator_cust.dart';

class WidgetParserEngine extends GetView<WidgetParserController> {
  WidgetParserEngine({
    super.key,
  });

  WidgetParserController wpcState = Get.put(WidgetParserController());

  @override
  Widget build(BuildContext context) {
    controller.isLoading.value = true;
    // controller.getWidgetJsonData(); // Fetch the widget JSON data
    controller.isLoading.value = false;
    //final widget = controller.parseJson();
    return controller.isLoading.value
        ? const Center(child: ProgressIndicatorCust())
        : SingleChildScrollView(child: Container() //widget,

            );
  }
}
