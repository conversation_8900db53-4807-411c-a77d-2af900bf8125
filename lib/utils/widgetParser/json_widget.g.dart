// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'json_widget.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JsonWidget _$JsonWidgetFromJson(Map<String, dynamic> json) => JsonWidget(
      json['widgetType'] as String,
      child: json['child'] == null
          ? null
          : JsonWidget.fromJson(json['child'] as Map<String, dynamic>),
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => JsonWidget.fromJson(e as Map<String, dynamic>))
          .toList(),
      text: json['text'] as String?,
      fontSize: json['fontSize'] as int?,
      color: json['color'] as String?,
      value: json['value'],
      readOnly: json['readOnly'] as bool?,
      dropDownValues: (json['dropDownValues'] as List<dynamic>?)
          ?.map((e) => JsonDropDownWidget.fromJson(e as Map<String, dynamic>))
          .toList(),
    )
      ..mainAxisAligment = json['mainAxisAligment'] as String?
      ..mainAxisSize = json['mainAxisSize'] as String?
      ..crossAxisAligment = json['crossAxisAligment'] as String?
      ..body = json['body'] == null
          ? null
          : JsonWidget.fromJson(json['body'] as Map<String, dynamic>)
      ..appBar = json['appBar'] == null
          ? null
          : JsonWidget.fromJson(json['appBar'] as Map<String, dynamic>);

Map<String, dynamic> _$JsonWidgetToJson(JsonWidget instance) =>
    <String, dynamic>{
      'widgetType': instance.widgetType,
      'child': instance.child,
      'value': instance.value,
      'dropDownValues': instance.dropDownValues,
      'children': instance.children,
      'readOnly': instance.readOnly,
      'text': instance.text,
      'fontSize': instance.fontSize,
      'color': instance.color,
      'mainAxisAligment': instance.mainAxisAligment,
      'mainAxisSize': instance.mainAxisSize,
      'crossAxisAligment': instance.crossAxisAligment,
      'body': instance.body,
      'appBar': instance.appBar,
    };
