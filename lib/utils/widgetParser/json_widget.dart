import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/utils/widgetParser/json_drop_down_widget.dart';

part 'json_widget.g.dart';

@JsonSerializable()
class JsonWidget {
  String widgetType;
  String? val;
  String? label; // text, appBar
  JsonWidget? child;
  dynamic value; // ta_check_box, ta_drop_down (should be the same type with JsonDropDownWidget)
  List<JsonDropDownWidget>? dropDownValues; // ta_dropdown
  List<JsonWidget>? children; // column, row
  bool? readOnly;

  //FOR TEXT_VIEW
  String? text; // text, appBar
  int? fontSize;
  String? color;

  //FOR ROW AND COLUMN
  String? mainAxisAligment; // column, row
  String? mainAxisSize; //column, row
  String? crossAxisAligment; // column, row

  //FOR SCAFFOLD
  JsonWidget? body; // scaffold
  JsonWidget? appBar; //scaffold

  JsonWidget(this.widgetType,
      {this.child,
      this.val,
      this.label,
      this.children,
      this.text,
      this.fontSize,
      this.color,
      this.value,
      this.readOnly,
      this.dropDownValues});

  factory JsonWidget.fromJson(Map<String, dynamic> json) => _$JsonWidgetFromJson(json);

  Map<String, dynamic> toJson() => _$JsonWidgetToJson(this);
}
