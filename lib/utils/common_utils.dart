import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';

import 'package:tangoworkplace/common/widgets/sso_web_view.dart';
import 'package:tangoworkplace/common/widgets/web_view_screen.dart';
import 'package:tangoworkplace/models/ta_admin/user_info.dart';
import 'package:tangoworkplace/providers/ta_admin/home_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/login_screen.dart';
import '../common/progess_indicator_cust.dart';
import '../services/common_services.dart';
import 'connections.dart';
import 'constvariables.dart';
import 'preferences_utils.dart';
import 'request_api_utils.dart';

class CommonUtils {
  static List hostlist = [
    'LOCAL',
    'DEV',
    'DEV14',
    'LOCALHOST',
    'TEST',
    'AZTEST',
    'AISTEST',
    'AMZNTEST',
    'HMTEST',
    'CUSHMANTEST',
    'HMTEST',
    'USPSTEST',
    'PRODAPP',
    'AZPROD',
    'CW',
    'HM',
    'DBI',
    'AMZN',
    'STAGE',
    'AIS',
    'USPS'
  ];

  static MaterialColor createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map swatch = <int, Color>{};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    strengths.forEach((strength) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    });
    return MaterialColor(color.value, swatch as Map<int, Color>);
  }

  static void cadviewer(BuildContext context, String? buildingid, String? floorid, String spaceid, {String? action}) {
    var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
    debugPrint('sessionid------------$sessionid');
    action = action ?? 'MAC';
    var host = ApiService.getServerurl();
    var url = host + '$cadViewerurl?floorId=$floorid&scenarioId=-1&buildingId=$buildingid&action=$action';
    // &JSESSIONID=$sessionid';
    if (action == 'find') {
      url = host + '$cadViewerurl?floorId=$floorid&buildingId=$buildingid&spaceId=$spaceid';
    }
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewContainer(
          url,
          'View Floor Plan',
          host,
          source: 'CADVIEW',
        ),
      ),
    );
  }

  static void findCadviewer(BuildContext context, {String? action, String? path}) {
    var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);

    var host = ApiService.getServerurl();
    var url;

    if (action == 'find') {
      url = host + '$cadViewerurl' + path;
    }
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewContainer(
          url,
          'View Floor Plan',
          host,
          source: 'CADVIEW',
        ),
      ),
    );
  }

  static logout(BuildContext? context, {String? type, GlobalKey<NavigatorState>? navigator}) {
    debugPrint('Inside logout');
    String? ssoenabled = SharedPrefUtils.readPrefStr("SSOEnabled");
    //if (ssoenabled == "Y") {
    String? ssologouturl = SharedPrefUtils.readPrefStr("SSOLogoutURL");
    if (ssoenabled == "Y" && ssologouturl != null && ssologouturl.isNotEmpty) {
      if (type != null && type == 'auto') {
        navigator!.currentState!.pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => SSOWebView(ssologouturl, 'logout', "")),
            (Route<dynamic> route) => false);
      } else {
        //Navigator.push(context, MaterialPageRoute(builder: (context) => SSOWebView(ssologouturl, 'logout', "")));
        Navigator.pushNamedAndRemoveUntil(context!, LoginPage.routName, (Route<dynamic> route) => false);
      }
      //}
    } else {
      if (type != null && type == 'auto') {
        navigator!.currentState!
            .pushAndRemoveUntil(MaterialPageRoute(builder: (context) => LoginPage()), (Route<dynamic> route) => false);
      } else {
        ApiService.logout();
        Navigator.pushNamedAndRemoveUntil(context!, LoginPage.routName, (Route<dynamic> route) => false);
      }
    }
    SharedPrefUtils.clear();
    Get.delete<LabelController>();
    Get.delete<HomeController>();
  }

  static Future refresh(BuildContext? context) async {
    ProgressUtil.showLoaderDialog(context!);
    debugPrint('Inside refresh');
    String clientId = await SharedPrefUtils.readPrefStr(ConstHelper.clientIdvar);
    debugPrint('clientId>>$clientId');
    String url = refreshurl + '?clientId=$clientId';
    debugPrint('url>>$url');
    await ApiService.get(url);
    ProgressUtil.closeLoaderDialog(context!);
    logout(context);
  }

  static Future<UserInfo> getUserdata() async {
    var user = await jsonDecode(SharedPrefUtils.readPrefStr('UserInfo'));
    var userPreflocale = await SharedPrefUtils.readPrefStr(ConstHelper.userPreflocale);
    UserInfo u = UserInfo(
      active: user['active'],
      firstName: user['firstName'],
      isContractor: user['isContractor'],
      lastName: user['lastName'],
      personId: user['personId'],
      userId: user['userId'],
      userName: user['userName'],
      userTerms: user['userTerms'],
      userTermsStatus: user['userTermsStatus'],
      userTermsVersion: user['userTermsVersion'],
      userType: user['userType'],
      phoneNumber: user['phoneNumber'],
      userPrefLocale: userPreflocale != null && userPreflocale != '' ? userPreflocale : user['userPrefLocale'],
    );
    return u;
  }

  static Future<dynamic> getUserParamsdata() async {
    var filter;

    var user = await getUserdata();
    debugPrint('>>>>>>>>>>>>>>>>${jsonEncode(user)}');

    String? cntry = SharedPrefUtils.readPrefStr(ConstHelper.countryCodevar);
    String? curClientId = SharedPrefUtils.readPrefStr(ConstHelper.clientIdvar);
    String? curBrandId = SharedPrefUtils.readPrefStr(ConstHelper.brandIdvar);
    String? curBuId = SharedPrefUtils.readPrefStr(ConstHelper.buIdvar);
    curBrandId = curBrandId == '' ? null : curBrandId;
    curBuId = curBuId == '' ? null : curBuId;
    String curContryCode = (cntry != null && cntry != 'null') ? cntry : 'xxxx';
    String? isBrandEnabled = SharedPrefUtils.readPrefStr(ConstHelper.isBrandEnabledvar);
    String? isBuEnabled = SharedPrefUtils.readPrefStr(ConstHelper.isBuEnabledvar);
    String? curLanguage = SharedPrefUtils.readPrefStr(ConstHelper.langCodevar);
    debugPrint(
        '_curClientId: $curClientId    _curBrandId: $curBrandId   _curBuId: $curBuId   _curContryCode: $curContryCode   _isBrandEnabled: $isBrandEnabled   _isBuEnabled: $isBuEnabled   _language: $curLanguage');

    Map<String, dynamic> dynamicMap = {
      'a': ['p_user', 'EXACT', user?.userName],
      'b': ['p_country', 'EXACT', curContryCode ?? 'ABC'],
      'c': ['p_clientId', 'EXACT', curClientId],
      'd': ['p_brandId', 'EXACT', curBrandId ?? 'ABC'],
      'e': ['p_buid', 'EXACT', curBuId ?? 'ABC'],
      'f': ['p_buenabled', 'EXACT', isBuEnabled],
      'g': ['p_isbrandenabled', 'EXACT', isBrandEnabled],
    };
    filter = CommonServices.getFilter(dynamicMap);
    debugPrint('filter---------' + filter);

    return filter;
  }

  static getUserPersonId() {
    Map<String, dynamic>? c = jsonDecode(SharedPrefUtils.readPrefStr('UserInfo')) as Map<String, dynamic>?;

    return c != null ? c['personId'].toString() : '';
  }

  static isTablet(BuildContext context) {
    bool isLargeScreen = false;
    if (MediaQuery.of(context).size.width > 650) {
      isLargeScreen = true;
    } else {
      isLargeScreen = false;
    }
    return isLargeScreen;
  }

  static void reservationCadViewer(
      BuildContext context, String buildingid, String floorid, String spaceid, String startdate, String enddate) {
    debugPrint('startdate>>>>>>>$startdate');
    DateTime startdateimpl = DateFormat("MM/dd/yyyy hh:mm a'").parse(startdate);
    DateTime dt =
        DateTime(startdateimpl.year, startdateimpl.month, startdateimpl.day, startdateimpl.hour, startdateimpl.minute);
    String sdate = DateFormat("yyyy-MM-ddTHH:mm").format(dt);
    debugPrint('reservationCadViwer$sdate');
    debugPrint('enddate>>>>>>>>$enddate');
    DateTime enddateimpl = DateFormat("MM/dd/yyyy hh:mm a'").parse(enddate);
    DateTime enddt =
        DateTime(enddateimpl.year, enddateimpl.month, enddateimpl.day, enddateimpl.hour, enddateimpl.minute);
    String edate = DateFormat("yyyy-MM-ddTHH:mm").format(enddt);
    debugPrint('reservationCadViwer$edate');
    var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
    var host = ApiService.getServerurl();
    var url = host +
        '$cadViewerurl?floorId=$floorid&scenarioId=-1&buildingId=$buildingid&action=reservation&start=$sdate&end=$edate';
    debugPrint('reservationCadViewer>>>>>>$url');

    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => WebViewContainer(
                  url,
                  'View Floor Plan',
                  host,
                  source: 'CADVIEW',
                )));
  }

  static void getSessionid(String sid) {
    String seid = '';
    var setCookies = sid.split(';');
    var keyValue = setCookies[0].split('=');
    var key = keyValue[0].trim();
    if (key == 'JSESSIONID') {
      String value = keyValue[1].trim();
      SharedPrefUtils.saveStr('cookieVal', value);
      int exaloc = value.indexOf("!");
      if (exaloc != -1) {
        seid = value.substring(0, exaloc);
      }
      debugPrint('getSessionid-----------' + seid);
      SharedPrefUtils.saveStr(ConstHelper.sessionIdvar, seid);
    }
  }

  static Future<Uint8List> networkImageToByte(String? path, {var width, var height}) async {
    HttpClient httpClient = HttpClient();
    Map<String, String> headers = SharedPrefUtils.getHeaders();
    debugPrint('icon url>>>>$path');
    var request = await httpClient.getUrl(
      Uri.parse(path ?? ''),
    );
    request.headers.add('Authorization', headers['Authorization']!);
    var response = await request.close();
    Uint8List bytes = await consolidateHttpClientResponseBytes(response);
    ui.Codec codec = await ui.instantiateImageCodec(bytes, targetWidth: width, targetHeight: height);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!.buffer.asUint8List();
    // return bytes;
  }

  static Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(), targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!.buffer.asUint8List();
  }

  static String month(int? num) {
    String month = '';
    switch (num) {
      case 1:
        month = 'January';
        break;
      case 2:
        month = 'February';
        break;
      case 3:
        month = 'March';
        break;
      case 4:
        month = 'April';
        break;
      case 5:
        month = 'May';
        break;
      case 6:
        month = 'June';
        break;
      case 7:
        month = 'July';
        break;
      case 8:
        month = 'August';
        break;
      case 9:
        month = 'September';
        break;
      case 10:
        month = 'October';
        break;
      case 11:
        month = 'November';
        break;
      case 12:
        month = 'December';
        break;
    }
    return month;
  }

  static String rDate({int? day, int? mnth, int? year, int? hour, int? min}) {
    String date = '';

    try {
      if (mnth != null && day != null && year != null) {
        date = '${month(mnth)} $day  $year';

        if (hour != null && min != null) {
          String mm = min < 10 ? '0$min' : min.toString();
          String hh = hour < 10 ? '0$hour' : hour.toString();
          var strDate = '2050-01-01 $hh:$mm:20.000';
          //debugPrint('strDate>>>>>>>$strDate');
          final dateFormat = DateFormat('h:mm a');

          String atime = dateFormat.format(DateTime.parse(strDate));
          if (atime != null) date = date + ', $atime';
        }
      }
    } catch (e) {
      debugPrint('rDate>>>>>$e');
    }
    return date;
  }
}
