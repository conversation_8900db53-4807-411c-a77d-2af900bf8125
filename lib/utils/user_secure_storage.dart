import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/ta_admin/user_info.dart';

class UserContext {
  static final _storage = FlutterSecureStorage();

  static const _keyUsername = 'username';

  static Future setUsername(String? username) async => await _storage.write(key: _keyUsername, value: username);

  static Future<String?> getUsername() async => await _storage.read(key: _keyUsername);

//-----------logged user info---------------------------------------
  static UserInfo? info() {
    UserInfo? userdata;
    var userStr = SharedPrefUtils.readPrefStr('UserInfo');
    if (userStr != null && userStr != '') {
      Map? u = jsonDecode(userStr);
      if (u != null) {
        userdata = UserInfo.fromJson(u as Map<String, dynamic>);
      }
    }
    return userdata;
  }
}
