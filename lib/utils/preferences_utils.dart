import 'dart:collection';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadatatable.dart';
import 'package:tangoworkplace/models/ta_admin/user_info.dart';
import 'dart:convert';
import 'package:flutter/material.dart';

class SharedPrefUtils {
  static SharedPreferences? _prefs;

  static Future<SharedPreferences?> init() async {
    _prefs = await SharedPreferences.getInstance();
    if (readPrefStr(ConstHelper.hostNameVar) == null) {
      saveStr(ConstHelper.hostNameVar, ConstHelper.hostNameVal);
    }
    return _prefs;
  }

  static saveStr(String key, String? message) {
    if (message != null) _prefs!.setString(key, message);
  }

  static readPrefStr(String key) {
    return _prefs!.getString(key);
  }

  static clear() async {
    try {
      _prefs!.remove(ConstHelper.userNamevar);
      _prefs!.remove(ConstHelper.sessionIdvar);
      _prefs!.remove(ConstHelper.authVar);
      _prefs!.remove('cookieVal');
      _prefs!.remove(ConstHelper.clientIdvar);
      _prefs!.remove(ConstHelper.brandIdvar);
      _prefs!.remove(ConstHelper.buIdvar);
      _prefs!.remove(ConstHelper.isBrandEnabledvar);
      _prefs!.remove(ConstHelper.isBuEnabledvar);
      _prefs!.remove(ConstHelper.countryCodevar);
      _prefs!.remove(ConstHelper.langCodevar);
      _prefs!.remove(ConstHelper.isContractorvar);
      _prefs!.remove(ConstHelper.userPreflocale);

//aq data---------------------------------------------------------
      _prefs!.remove(ConstHelper.aqaccname);
      _prefs!.remove(ConstHelper.aqappauthtoken);
      _prefs!.remove(ConstHelper.aqappname);
      _prefs!.remove(ConstHelper.aqforumlist);
      _prefs!.remove(ConstHelper.aqsysidacc);
      _prefs!.remove(ConstHelper.aqsysidapp);
      _prefs!.remove(ConstHelper.aqusertoken);
      _prefs!.remove(ConstHelper.aqbaseurl);
    } catch (e) {
      debugPrint('-----clearing shared pref data Exception------$e');
    } finally {
      debugPrint('-----clearing shared pref data------');
    }
  }

  static getHeaders() {
    Map<String, String> headers = new HashMap<String, String>();
    // var sessionid = readPrefStr('cookieVal');
    var token = readPrefStr(ConstHelper.tokenVal);

    // ignore: prefer_interpolation_to_compose_strings
    headers['Authorization'] = 'Bearer ' + token;
    headers['Content-Type'] = 'application/json;charset=UTF-8';
    headers['Charset'] = 'utf-8';
    //headers['Cookie'] = sessionid;

    return headers;
  }

  static getAqHeaders() async {
    Map<String, String> headers = new HashMap<String, String>();
    var aqauthtoken = await readPrefStr(ConstHelper.aqappauthtoken);
    var token = await readPrefStr(ConstHelper.aqusertoken);

    headers['Authorization'] = token;
    headers['AQOB-AppAuthToken'] = aqauthtoken;
    headers['Content-Type'] = 'application/json';
    headers['Accept'] = 'application/json';

    return headers;
  }

  static setContextUserInfo(Map<String, dynamic> c, {String? sfrom, String? userPrefLocale}) {
    SharedPrefUtils.saveStr(ConstHelper.clientIdvar, c['clientId'].toString());
    SharedPrefUtils.saveStr(ConstHelper.brandIdvar, c['brandId'] != null ? c['brandId'].toString() : '');
    SharedPrefUtils.saveStr(ConstHelper.buIdvar, c['buId'] != null ? c['buId'].toString() : '');
    SharedPrefUtils.saveStr(ConstHelper.isBrandEnabledvar, c['isBrandEnabled'] != null ? c['isBrandEnabled'].toString() : 'N');
    SharedPrefUtils.saveStr(ConstHelper.isBuEnabledvar, c['isBuEnabled'] != null ? c['isBuEnabled'].toString() : 'N');
    SharedPrefUtils.saveStr(ConstHelper.countryCodevar, c['countryCode'].toString());
    SharedPrefUtils.saveStr(ConstHelper.langCodevar, c['langCode'].toString());
    SharedPrefUtils.saveStr(ConstHelper.isContractorvar, c['isContractor'].toString());

    if (c['aqBaseUrl'] != null && c['aqBaseUrl'] != '') {
      SharedPrefUtils.saveStr(ConstHelper.aqbaseurl, c['aqBaseUrl']?.toString());
    } else if (sfrom == 'change_context') {
      SharedPrefUtils.saveStr(ConstHelper.aqbaseurl, '');
    }

    if (userPrefLocale != null && userPrefLocale != '' && sfrom == 'change_context') {
      SharedPrefUtils.saveStr(ConstHelper.userPreflocale, userPrefLocale);
    }
  }
}
