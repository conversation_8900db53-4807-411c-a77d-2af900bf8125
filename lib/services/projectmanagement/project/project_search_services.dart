import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

class ProjectSearchServices {
  static Future<Map<String, dynamic>?> fetchProjectsView({dynamic payloadObj}) async {
    debugPrint('--------fetchProjectsView------------');

    Map<String, dynamic>? tagObjs;
    try {
      tagObjs = await CommonServices.fetchDynamicApi(projectsviewurl, payloadObj: payloadObj);
    } catch (e) {
      debugPrint('fetchProjects>>>>>>>$e');
    }
    return tagObjs;
  }
}
