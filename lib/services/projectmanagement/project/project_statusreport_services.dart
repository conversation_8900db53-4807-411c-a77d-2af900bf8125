import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/models/project/statusreport/statusreport.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

class StatusReportServices {
  static Future saveStatusReport(StatusReport sr, String at_mode) async {
    int? id = 0;
    try {
      String bodyjson = json.encode(sr);
      var apival = at_mode == 'edit' ? projectstatusreportupdateurl : projectstatusreportcreateurl;
      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          id = at_mode == 'edit' ? 1 : (resMap['id'] as int?)!;
        }
      }
    } catch (error) {
      id = 0;
      debugPrint('$error');
    }

    return id;
  }

  static Future saveSrDetailsdata(Map m) async {
    var result = 'error';
    try {
      String bodyjson = json.encode(m);
      var apival = projectstatusreportupdatedetailsurl;
      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    }

    return result;
  }
}
