import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/models/comments.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/person_list.dart';
import 'package:tangoworkplace/models/pictures.dart';
import 'package:tangoworkplace/models/recent_reservations.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';
import '../models/property.dart';
import '../models/space.dart';
import '../utils/custom_exception.dart';
import '../utils/preferences_utils.dart';
import '../utils/connections.dart';
import '../models/reservations.dart';
import 'common_services.dart';

Future<List<Reservations>?> getReservations(context) async {
  String apival = reservationsurl + '?status=SCHEDULED';
  List<Reservations>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Reservations'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Reservations.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('getReservations>>>>>>> $e');
  }
  return tagObjs;
}

Future<List<Reservations>?> getCompletedReservationsWS(context) async {
  String apival = reservationsurl + '?status=COMPLETED';
  List<Reservations>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Reservations'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Reservations.fromJson(tagJson)).toList();
      //  }
      // }
    }
  } catch (e) {
    debugPrint('getCompletedReservationsWS>>>>>>>$e');
  }
  return tagObjs;
}

Future<Reservations> addReservationImpl(Reservations reserv) async {
  try {
    String bodyjson = json.encode({
      'reservationId': reserv.reservationId,
      'reservationNumber': reserv.reservationNumber,
      'reservationType': reserv.reservationType,
      'reservationTitle': reserv.reservationTitle,
      'startDate': reserv.startDate,
      'endDate': reserv.endDate,
      'status': reserv.status,
      'propertyId': reserv.propertyId,
      'buildingId': reserv.buildingId,
      'spaceId': reserv.spaceId,
      'floorId': reserv.floorId,
      'description': reserv.description,
      'rating': reserv.rating,
      'selfReserve': reserv.selfReserve,
      'allDay': reserv.allDay,
      'serviceItems': reserv.serviceItems ?? '',
      'isRecurring': reserv.isRecurring,
      'createdBy': reserv.createdBy,
      'propertyName': reserv.propertyName,
      'buildingName': reserv.buildingName,
      'floorName': reserv.floorName,
      'spaceDisplayName': reserv.spaceDisplayName,
      'assignees': reserv.assignees,
    });
    debugPrint(bodyjson);
    bodyjson = '{"Reservations": $bodyjson }';

    String apival = reservationsaddurl;

    var resMap = await ApiService.post(apival, payloadObj: bodyjson);
    var tagObjsJson = jsonDecode(resMap) as Map<String, dynamic>;

    if (tagObjsJson['status'] == 0) {
      var requestId = tagObjsJson['requestId'] as int?;

      print(requestId);

      Reservations _reservationObj = Reservations(
        reservationId: requestId,
        reservationNumber: '00$requestId',
        reservationType: reserv.reservationType,
        reservationTitle: reserv.reservationTitle,
        startDate: reserv.startDate,
        endDate: reserv.endDate,
        status: 'SCHEDULED',
        propertyId: reserv.propertyId,
        buildingId: reserv.buildingId,
        spaceId: reserv.spaceId,
        floorId: reserv.floorId,
        description: '',
        rating: -1,
        selfReserve: reserv.selfReserve,
        allDay: reserv.allDay,
        serviceItems: reserv.serviceItems,
        isRecurring: reserv.isRecurring,
        createdBy: reserv.createdBy,
        propertyName: reserv.propertyName,
        buildingName: reserv.buildingName,
        floorName: reserv.floorName,
        spaceDisplayName: reserv.spaceDisplayName,
        assignees: reserv.assignees,
      );

      return _reservationObj;
    } else {
      throw CustomException(tagObjsJson['statusmessage']);
    }
  } catch (error) {
    print(error);
    throw error;
  }
}

Future<String?> updateReservationImpl(Reservations reserv, int? requestId) async {
  String? result = '';
  try {
    String apival = reservationsupdateurl + '/$requestId';

    String bodyjson = json.encode({
      'reservationId': reserv.reservationId,
      'reservationNumber': reserv.reservationNumber,
      'reservationType': reserv.reservationType,
      'reservationTitle': reserv.reservationTitle,
      'startDate': reserv.startDate,
      'endDate': reserv.endDate,
      'status': reserv.status,
      'propertyId': reserv.propertyId,
      'buildingId': reserv.buildingId,
      'spaceId': reserv.spaceId,
      'floorId': reserv.floorId,
      'description': reserv.description,
      'rating': reserv.rating,
      'selfReserve': reserv.selfReserve,
      'allDay': reserv.allDay,
      'serviceItems': reserv.serviceItems,
      'isRecurring': reserv.isRecurring,
      'createdBy': reserv.createdBy,
      'propertyName': reserv.propertyName,
      'buildingName': reserv.buildingName,
      'floorName': reserv.floorName,
      'spaceDisplayName': reserv.spaceDisplayName,
      'assignees': reserv.assignees,
    });
    bodyjson = '{"Reservations": $bodyjson }';
    debugPrint(bodyjson);
    var resMap = await ApiService.post(apival, payloadObj: bodyjson);
    var tagObjsJson = jsonDecode(resMap) as Map<String, dynamic>;

    if (tagObjsJson['status'] == 0) {
      var requestId = tagObjsJson['requestId'] as int?;
      result = 'success';
    } else {
      result = tagObjsJson['statusmessage'];
    }
  } catch (error) {
    debugPrint('$error');
  }
  debugPrint(result);
  return result;
}

Future<List<Property>?> getPropertyLovObj(context) async {
  String apival = propertylovurl;
  List<Property>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    //if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Properties'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Property.fromJson(tagJson)).toList();
    }
    //}
    // }
  } catch (e) {
    debugPrint('getPropertyLovObj>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<Space>?> getSpaceLovObj(context, buildingid, floorid, startdate, enddate, String? selectedAttributes,
    {String? requestId}) async {
  debugPrint('--------------------getSpaceLovObj------------------------');

  debugPrint('selectedAttributes $selectedAttributes');
  String apival = "";
  String reqId = requestId != null ? requestId : "-1";
  String floor = floorid == null ? '0000' : floorid.toString();
  if (selectedAttributes != null && selectedAttributes.isNotEmpty) {
    apival = spaceurl +
        '/list' +
        "/" +
        buildingid.toString() +
        "/" +
        floor +
        "/" +
        // reqId +
        // "/" +
        startdate +
        "/" +
        enddate +
        "/" +
        selectedAttributes;
  } else {
    apival = spaceurl +
        '/list' +
        "/" +
        buildingid.toString() +
        "/" +
        floor +
        "/" +
        // reqId +
        // "/" +
        startdate +
        "/" +
        enddate +
        "/null";
  }
  debugPrint('-----------$apival');

  List<Space>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    // if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Space'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Space.fromJson(tagJson)).toList();
    }
    //}
    // }
  } catch (e) {
    debugPrint('getSpaceLovObj>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<RecentReservations>?> getRecentReserLocInfo(context, String startdate, String enddate) async {
  String apival = recentreservationslocurl + "/" + startdate + "/" + enddate;
  List<RecentReservations>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    //if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['RecentReservtions'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => RecentReservations.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('getRecentReserLocInfo>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<RecentReservations>?> getPropertyBuildingFloorLocInfo(
    context, String? startdate, String? enddate, String searchtext, String? selectedAttributes) async {
  String apival;
  List<RecentReservations>? tagObjs;
  if (selectedAttributes != null) {
    apival = propertybuildingfloorurl + '/$startdate' + '/$enddate' + '/$searchtext' + '/$selectedAttributes';
  } else {
    apival = propertybuildingfloorurl + '/$startdate' + '/$enddate' + '/$searchtext' + '/null';
  }
  try {
    var resMap = await ApiService.get(apival);

    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['LocInfo'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => RecentReservations.fromJson(tagJson)).toList();
    }
    //}
    //}
  } catch (e) {
    debugPrint('getPropertyBuildingFloorLocInfo>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<PersonList>?> getPersonListInfo(context, String searchtext) async {
  String apival = personListurl + '/$searchtext';
  List<PersonList>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    //if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['PersonList'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => PersonList.fromJson(tagJson)).toList();
    }
    //}
    // }
  } catch (e) {
    debugPrint('getPersonListInfo>>>>>>>$e');
  }
  return tagObjs;
}

Future<String?> updateReservationStatus(context, String status, int? reservationid) async {
  String? responseStr = "";
  var data = new Map<String, dynamic>();
  data['reservationid'] = reservationid.toString();
  data['status'] = status;
  String apival = reservationupdatestatusurl;
  var resMap = await ApiService.post(apival, payloadObj: data, type: 'form');
  var tagObjsJson = jsonDecode(resMap) as Map<String, dynamic>;
  if (tagObjsJson['status'] == 0) {
    responseStr = tagObjsJson['statusmessage'];
  }
  return responseStr;
}

Future<String?> updateReservationComments(context, int? reservationid, String comments, int rating, int? spaceid) async {
  String? responseStr = "";
  print(comments);
  if (reservationid != null && rating != null) {
    String bodyjson = '{"Comments": "$comments"}';
    String apival = reservationupdatecommentsurl + '?reservationid=$reservationid&spaceid=$spaceid&rating=$rating';
    var resMap = await ApiService.post(apival, payloadObj: bodyjson);
    var tagObjsJson = jsonDecode(resMap) as Map<String, dynamic>;
    if (tagObjsJson['status'] == 0) {
      responseStr = tagObjsJson['statusmessage'];
    }
  }
  return responseStr;
}

Future<List?> getAssigneesImpl(context, int? reservationid) async {
  String apival = reservationassigneeurl + '?reservationid=$reservationid';
  List? tagObjsJson;
  try {
    var resMap = await ApiService.get(apival);

    if (resMap.isNotEmpty) {
      tagObjsJson = jsonDecode(resMap) as List?;
    }
  } catch (e) {
    debugPrint('getAssigneesImpl>>>>>>>$e');
  }
  return tagObjsJson;
}

Future<List?> getPicturesImpl(context, int? entityid, String entitytype) async {
  String apival = picturesurl + '/$entityid' + '/$entitytype';
  List<Pictures>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    //if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Pictures'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Pictures.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('getPicturesImpl>>>>>>>$e');
  }
  return tagObjs;
}

Future<List?> getCommentsImpl1(context, int entityid, String entitytype) async {
  String apival = commentsurl + '/$entityid' + '/$entitytype';
  List<Comments>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Comments'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Comments.fromJson(tagJson)).toList();
    }
    //}
    // }
  } catch (e) {
    debugPrint('getCommentsImpl>>>>>>>$e');
  }
  return tagObjs;
}

Future<List?> getCommentsImpl(context, int? entityid, String entitytype) async {
  List<Comments>? comm;
  var payloadObj;
  Map<String, dynamic>? resMap;
  Map<String, dynamic> dynamicMap = {
    'a': ['entity_type', 'EXACT', entitytype],
    'b': ['entity_id', 'EXACT', entityid.toString()],
  };

  payloadObj = CommonServices.getFilter(dynamicMap);
  try {
    resMap = await CommonServices.fetchDynamicApi(entitycommentsurl, payloadObj: payloadObj);
    if (resMap != null) {
      var status = resMap['status'] as int?;
      if (status == 0) {
        var tagObjsJson = resMap['records'] as List?;
        if (tagObjsJson != null) {
          comm = tagObjsJson.map((tagJson) => Comments.fromJson(tagJson)).toList();
          debugPrint(' list Count------ ${comm.length}');
        }
      }
    }
  } catch (e) {
    debugPrint('getCommentsImpl>>>>>>>$e');
  }
  return comm;
}

Future<List<PersonList>?> getRecentPersonImpl(context) async {
  String apival = recentpersonListurl;
  List<PersonList>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    //if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['PersonList'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => PersonList.fromJson(tagJson)).toList();
    }
    //}
    //}
  } catch (e) {
    debugPrint('getRecentPersonImpl>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<LookupValues>?> getLookupValuesImpl(context, String lookupcode) async {
  String apival = commondataurl + '/lov?lookupcode=' + lookupcode;
  List<LookupValues>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    // if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Lov'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
      // }
      //}
    }
  } catch (e) {
    debugPrint('getLookupValuesImpl>>>>>>>$e');
  }
  return tagObjs;
}

class ReservationServices {
  static Future<String> getPersonId(String userid) async {
    String apival = personuidurl + userid;
    String uid = '';
    var obj;
    try {
      var resMap = await ApiService.get(apival);
      debugPrint('getPersonId id11>>>>>>>');
      resMap = jsonDecode(resMap);

      if (resMap.isNotEmpty) {
        if (resMap['status'] == 0) {
          var id = resMap['id'];
          uid = (id != null && id != 0) ? '$id' : '';
        }
      }
    } catch (e) {
      uid = '';
      debugPrint('getPersonId serv>>>>>>>$e');
    }
    return uid;
  }
}
