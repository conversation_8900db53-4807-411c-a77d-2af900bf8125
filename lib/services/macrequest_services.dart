import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';
import '../models/macrequests.dart';
import '../models/property.dart';
import '../models/space.dart';
import '../utils/preferences_utils.dart';

import '../utils/connections.dart';

Future<List<MacRequest?>?> fetchMacRequests(context) async {
  String apival = macrequesturl;
  List<MacRequest?>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Mac'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => MacRequest.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchMacRequests>>>>>>>$e');
  }
  return tagObjs;
}

Future<MacRequest?> addMacRequestImpl(MacRequest macRequest) async {
  MacRequest? tagObjs;
  return tagObjs;
}

Future<List<Property>?> getPropertyLovObj(context) async {
  String apival = propertylovurl;
  List<Property>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Properties'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Property.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('getPropertyLovObj>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<Space>?> getSpaceLovObj(context, buildingid, floorid, startdate, enddate) async {
  startdate = '01-01-2021';
  enddate = '01-01-2021';

  String apival = personspacelovurl + "/" + buildingid.toString() + "/" + floorid.toString() + "/" + startdate + "/" + enddate;
  List<Space>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Space'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Space.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('getSpaceLovObj>>>>>>>$e');
  }
  return tagObjs;
}
