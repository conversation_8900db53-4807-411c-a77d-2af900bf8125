import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/common/common_import.dart';

class UserPendingTaskServices {
  static Future<Map<String, dynamic>?> fetchUserpendingtasks({dynamic payloadObj, required var url}) async {
    debugPrint('--------fetchUserpendingtasks------------');

    Map<String, dynamic>? tagObjs;
    try {
      tagObjs = await CommonServices.fetchDynamicApi(url, payloadObj: payloadObj);
    } catch (e) {
      debugPrint('fetchUserpendingtasks>>>>>>>$e');
    }
    return tagObjs;
  }

  static Future<Map<String, dynamic>?> fetchUserpendingtaskList(String? entitytype, {dynamic payloadObj}) async {
    debugPrint('--------fetchUserpendingtasks------------');

    Map<String, dynamic>? tagObjs;
    late String url;
    debugPrint('entitytype------------$entitytype');
    try {
      if (entitytype == 'PROJECT') {
        url = projecttasksurl;
      } else if (entitytype == 'BUDGET') {
        url = budgettasksurl;
      } else if (entitytype == 'BUDGETCO') {
        url = budgetcotasksurl;
      } else if (entitytype == 'PO') {
        url = potasksurl;
      } else if (entitytype == 'CO') {
        url = cotasksurl;
      } else if (entitytype == 'INVOICE') {
        url = invoicetasksurl;
      } else if (entitytype == 'DOCUMENT') {
        url = documenttasksurl;
      } else if (entitytype == 'LEASE') {
        url = leasetasksurl;
      } else if (entitytype == 'LEASEOTP') {
        url = leaseotptasksurl;
      } else if (entitytype == 'LEASEBATCH') {
        url = userleasebatchrenttasksurl;
      } else if (entitytype == 'LEASEREC') {
        url = userleaserecurringcosttasksurl;
      } else if (entitytype == 'SITE') {
        url = sitetaskslisturl;
      }
      tagObjs = await CommonServices.fetchDynamicApi(url, payloadObj: payloadObj);
    } catch (e) {
      debugPrint('fetchUserpendingtasks>>>>>>>$e');
    }
    return tagObjs;
  }

  static Future<Map<String, dynamic>?> fetchWfHistoryList(String? entitytype, String? entityid) async {
    debugPrint('--------fetchWfHistoryList------------');

    Map<String, dynamic>? tagObjs;
    String url = wfhistoryurl;
    try {
      var filter;
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityid],
        'b': ['entity_type', 'EXACT', entitytype],
      };
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      tagObjs = await CommonServices.fetchDynamicApi(url, payloadObj: filter);
    } catch (e) {
      debugPrint('fetchWfHistoryList>>>>>>>$e');
    }
    return tagObjs;
  }

  static Future<Map<String, dynamic>?> submitwftask(String status, String? assignee, int taskid, String comments) async {
    debugPrint('--------submitwftask------------');
    Map<String, dynamic>? tagObjs;
    String url = submitwftaskurl + '/$status/$taskid/$assignee';
    String response = 'error';
    try {
      var resMap = await ApiService.post(url, payloadObj: comments);
      tagObjs = (jsonDecode(resMap) as Map?) as Map<String, dynamic>?;
      debugPrint('$tagObjs');
    } catch (e) {
      debugPrint('$e');
    }
    return tagObjs;
  }

  static Future<void> callbaselineBudgetByWF(String projectid, String comments) async {
    debugPrint('--------callbaselineBudgetByWF------------');
    Map<String, dynamic>? tagObjs;
    String url = baselinebudgetbywfurl;
    Map m = {"project_id": projectid, "comments": comments};

    try {
      var resMap = await ApiService.post(url, payloadObj: jsonEncode(m));
      tagObjs = (jsonDecode(resMap) as Map?) as Map<String, dynamic>?;
      debugPrint('callbaselineBudgetByWF>>>>>>>>>>>>>>>>$tagObjs');
    } catch (e) {
      debugPrint('$e');
    }
    return;
  }
}
