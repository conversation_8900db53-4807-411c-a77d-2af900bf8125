import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/agilequest/location/aq_search_locations_views_data.dart';
import 'package:tangoworkplace/models/agilequest/reserve/aq_reservation_group.dart';
import 'package:tangoworkplace/models/agilequest/resource/aq_resource_data.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/auth/aq_app_credentials_request.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/auth/aq_user_credentials_request.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/location/aq_search_locations_request.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/reserve/aq_reserve_view_criteria_request.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/review/aq_reviews_request_body.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/user/aq_search_user_view_body.dart';
import 'package:tangoworkplace/services/agilequest/backendData/response/aq_application_properties_response.dart';
import 'package:tangoworkplace/services/agilequest/backendData/response/aq_currency_types_response.dart';
import 'package:tangoworkplace/services/agilequest/backendData/response/aq_language_tags_response.dart';
import 'package:tangoworkplace/services/agilequest/backendData/response/aq_reserve_views_response.dart';
import 'package:tangoworkplace/services/agilequest/backendData/response/aq_resource_categories_response.dart';
import 'package:tangoworkplace/services/agilequest/backendData/response/aq_reviews_response.dart';
import 'package:tangoworkplace/services/agilequest/backendData/response/aq_search_user_view_response.dart';
import 'package:tangoworkplace/utils/agilquest/aq_connections.dart';

import '../../models/agilequest/auth/app_auth_data.dart';
import '../../models/agilequest/auth/aq_user_login_data.dart';
import 'aq_app_interceptors.dart';
import 'backendData/requests/reserve/aq_reservation_action_request.dart';
import 'backendData/requests/reserve/aq_reservation_dategap_request.dart';
import 'backendData/requests/review/aq_review_request_body.dart';
import 'backendData/response/aq_resource_views_response.dart';

class AqApiClient {
  static int _timeoutSeconds = 60;
  static Dio? _dioClient;
  static final dio = getDio();

  static AqAppInterceptors? _interceptor;

  static Dio getDio() {
    if (_dioClient == null) {
      String baseUrl = SharedPrefUtils.readPrefStr(ConstHelper.aqbaseurl);
      _dioClient = Dio(BaseOptions(
        baseUrl: baseUrl,
        receiveTimeout: Duration(seconds: _timeoutSeconds),
        connectTimeout: Duration(seconds: _timeoutSeconds),
        sendTimeout: Duration(seconds: _timeoutSeconds),
      ));

      _interceptor = AqAppInterceptors(_dioClient!);

      _dioClient?.interceptors.addAll({
        _interceptor!,
        LogInterceptor(
          request: true,
          requestBody: true,
          responseBody: true,
          logPrint: (object) {
            assert(() {
              debugPrint('$object');
              return true;
            }());
          },
        )
      });
    }

    return _dioClient!;
  }

  //APP ENVIRONMENT
  static Future<AqApplicationPropertiesResponse> appProperties() async {
    final response = await dio.get(AqConn.appPropertiesUrl);
    if (response.statusCode == 200) {
      return AqApplicationPropertiesResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  static Future<AqLanguageTagsResponse> languageTags(languageId) async {
    final response = await dio.get("${AqConn.languageCodesUrl}/$languageId");
    if (response.statusCode == 200) {
      return AqLanguageTagsResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  static Future<AqCurrencyTypesResponse> currencyTypes() async {
    final response = await dio.get(AqConn.currencyTypeUrl);
    if (response.statusCode == 200) {
      return AqCurrencyTypesResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  // AUTH
  static Future<AppAuthData> loginApp() async {
    final response = await dio.post(AqConn.appLoginUrl, data: AqAppCredentialsRequest.mobileAppCredentials().toJson());
    if (response.statusCode == 200) {
      final appData = AppAuthData.fromJson(response.data);
      AqApiClient._interceptor?.updateAppToken(appData.authToken);
      SharedPrefUtils.saveStr(ConstHelper.aqsysidapp, appData.sysidApplication.toString());
      SharedPrefUtils.saveStr(ConstHelper.aqsysidacc, appData.sysidAccount.toString());
      SharedPrefUtils.saveStr(ConstHelper.aqappauthtoken, appData.authToken.toString());
      SharedPrefUtils.saveStr(ConstHelper.aqappname, appData.appName.toString());
      SharedPrefUtils.saveStr(ConstHelper.aqaccname, appData.accountName.toString());
      return appData;
    } else {
      throw Exception("Api error");
    }
  }

  static Future<AqUserLoginData> loginUser(String email, String password) async {
    final response = await dio.post(AqConn.userLoginUrl, data: AqUserCredentialsRequest(email, password).toJson());
    if (response.statusCode == 200) {
      final userData = AqUserLoginData.fromJson(response.data);
      AqApiClient._interceptor?.updateUserToken(userData.userData.authToken);
      await SharedPrefUtils.saveStr(ConstHelper.aqusertoken, userData.userData.authToken);
      return userData;
    } else {
      throw Exception("Api error: ${response.data.toString()}");
    }
  }

  // RESERVATIONS

  static Future<AqReserveViewsResponse> reservationViews(AqReserveViewCriteriaRequest body) async {
    final response = await dio.post(AqConn.reserveViewsUrl, data: body.toJson());
    if (response.statusCode == 200) {
      return AqReserveViewsResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  // LOCATIONS

  static Future<AqSearchLocationsViewsData> searchLocations(AqSearchLocationsRequest body) async {
    final response = await dio.post(AqConn.locationsSearchUrl, data: body.toJson());
    if (response.statusCode == 200) {
      return AqSearchLocationsViewsData.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  // CATEGORIES

  static Future<AqResourceCategoriesResponse> categoryTypes() async {
    final response = await dio.get(AqConn.resourceCategoriesUrl);
    if (response.statusCode == 200) {
      return AqResourceCategoriesResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  // DELEGATES
  static Future<AqSearchUserViewResponse> delegatesUsers(int userId) async {
    final response = await dio.get(AqConn.delegatesUsersUrl, queryParameters: {"sysidUser": "$userId"});
    if (response.statusCode == 200) {
      return AqSearchUserViewResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  //Reservation Actions

  static Future<void> resrveAction(AqReservationActionList body) async {
    final response = await dio.put(AqConn.reservationChangeStatusUrl, data: body.toJson());

    if (response.statusCode == 200) {
      return;
    } else {
      throw Exception("resrveAction >>>>>>>>>>>>>>>Api error");
    }
  }

  //Reservation Actions

  static Future<void> reserveGapCancel(AqReservationDateGapReq body) async {
    final response = await dio.post(AqConn.reservationGapsUrl, data: body.toJson());

    if (response.statusCode == 200 || response.statusCode == 204) {
      return;
    } else {
      throw Exception("resrveGapCancel >>>>>>>>>>>>>>>Api error");
    }
  }

  static Future<bool> deleteReserve(String id) async {
    debugPrint('deleteReserve>>>>>>>>>>>>>>>>$id');
    final response = await dio.delete(AqConn.deleteDraftReserveUrl + id);

    if (response.statusCode == 200 || response.statusCode == 204) {
      return true;
    } else {
      throw Exception("deleteReserve >>>>>>>>>>>>>>>Api error");
    }
  }

  // Resources
  static Future<AqResourceData> fetchResourceData(int resourceId) async {
    var url = AqConn.resorceUrl + resourceId.toString();
    final response = await dio.get(url);
    if (response.statusCode == 200) {
      return AqResourceData.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  // Event
  static Future<AqReservationGroup> getEvent(int confirmationId) async {
    var url = AqConn.getEventUrl + confirmationId.toString();
    final response = await dio.get(url);
    if (response.statusCode == 200) {
      return AqReservationGroup.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  // Review
  static Future<AqReviewsResponse> getReviews(AqReviewsRequestBody body) async {
    final response = await dio.post(AqConn.getReviews, data: body.toJson());
    if (response.statusCode == 200) {
      return AqReviewsResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  static Future<void> deleteReviews(int reviewId) async {
    var url = AqConn.deleteReview + reviewId.toString();
    final response = await dio.delete(url);
    if (response.statusCode == 204) {
      return;
    } else {
      throw Exception("Api error");
    }
  }

  static Future<AqReviewRequestBody> createReviews(AqReviewRequestBody body) async {
    final response = await dio.post(AqConn.createReview, data: body.toJson());
    if (response.statusCode == 200) {
      return AqReviewRequestBody.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  static Future<AqReviewRequestBody> updateReviews(AqReviewRequestBody body) async {
    final response = await dio.put(AqConn.createReview, data: body.toJson());
    if (response.statusCode == 200) {
      return AqReviewRequestBody.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }

  //Users
  static Future<AqSearchUserViewResponse> searchUserViews(AqSearchUserViewBody body) async {
    final response = await dio.post(AqConn.searchByName, data: body.toJson());
    if (response.statusCode == 200) {
      return AqSearchUserViewResponse.fromJson(response.data);
    } else {
      throw Exception("Api error");
    }
  }
}
