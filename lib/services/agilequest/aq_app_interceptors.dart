import 'package:dio/dio.dart';

class AqAppInterceptors extends Interceptor {
  final Dio dio;
  String? _userAuthToken;
  String? _appAuthToken;

  AqAppInterceptors(this.dio);

  void updateAppToken(String? token) {
    _appAuthToken = token;
  }

  void updateUserToken(String? token) {
    _userAuthToken = token;
  }

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    if (_userAuthToken != null && _appAuthToken != null) {
      options.headers.addAll({
        "AQOB-AppAuthToken": _appAuthToken ?? "",
        "Authorization": _userAuthToken ?? "",
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      });
    } else if (_appAuthToken != null) {
      options.headers.addAll({
        "AQOB-AppAuthToken": _appAuthToken ?? "",
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      });
    } else {
      options.headers.addAll({
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      });
    }

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {


    super.onResponse(response, handler);
  }
}
