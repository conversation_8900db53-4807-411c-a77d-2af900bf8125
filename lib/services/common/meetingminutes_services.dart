import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_followup.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_minute.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

import '../common_services.dart';

class MeetingMinutesServices {
  static Future<dynamic> saveMeetingMinute(MeetingMinute mm, String at_mode) async {
    var resMap;
    try {
      String bodyjson = json.encode(mm);
      var apival = at_mode == 'edit' ? updatemeetingminuteurl : createmeetingminuteurl;
      //debugPrint(bodyjson);

      resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        debugPrint(resMap);
        resMap = jsonDecode(resMap);

        // var status = resMap['status'] as int;

      }
    } catch (error) {
      debugPrint('$error');
    }
    return resMap;
  }

  static Future<String> saveAttendees(Map m) async {
    String result = 'error';
    try {
      String bodyjson = json.encode(m);
      var resMap = await ApiService.post(saveattendeesurl, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    }
    debugPrint(result);
    return result;
  }

  static Future saveFollowup(MeetingFollowup mf) async {
    int? id = 0;
    try {
      String bodyjson = json.encode(mf);

      debugPrint(bodyjson);

      var resMap = await ApiService.post(createmeetingfollowupurl, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          id = resMap['id'] as int?;
        }
      }
    } catch (error) {
      id = 0;
      debugPrint('$error');
    }

    return id;
  }
}
