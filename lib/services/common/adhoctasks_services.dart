import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

import '../common_services.dart';

class AdhocTaskServices {
  static Future<Map<String, dynamic>?> fetchDynamicApi({dynamic payloadObj, String? url}) async {
    Map<String, dynamic>? resMap;
    try {
      resMap = await CommonServices.fetchDynamicApi(url ?? adhoctaskslisturl, payloadObj: payloadObj);
      debugPrint('$resMap');
    } catch (e) {
      debugPrint('fetchDynamicApi AdhocTasks>>>>>>>$e');
    }
    return resMap;
  }

  static Future<int?> getAdhocTaskId() async {
    int? id = 0;
    try {
      var apival = getadhoctaskidurl;

      var resMap = await ApiService.post(apival);

      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          id = resMap['id'] as int?;
        }
      }
    } catch (e) {
      debugPrint('$e');
    }

    return id;
  }

  static Future<String> generatefromtemplate(Map m, {String? url}) async {
    String result = 'error';
    try {
      var apival = url ?? generateadhocfromtemplateurl;
      String bodyjson = jsonEncode(m);

      debugPrint('bodyjson    ' + bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);

      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (e) {
      debugPrint('$e');
    }
    return result;
  }

  static Future<int?> saveDetAdhocTaskImpl(Map m, String at_mode) async {
    int? id = 0;
    try {
      String bodyjson = json.encode(m);
      var apival = at_mode == 'edit' ? updateadhoctaskurl : createadhoctaskurl;
      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          id = at_mode == 'edit' ? 1 : (resMap['id'] as int?)!;
        }
      }
    } catch (error) {
      id = 0;
      debugPrint('$error');
    }

    return id;
  }

  static Future<String> saveNotificationUsers(Map m) async {
    String result = 'error';
    try {
      var apival = saveadhocnotifiusersurl;
      String bodyjson = json.encode(m);
      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    }
    debugPrint(result);
    return result;
  }

  static Future<String> saveDocAttachemnts(Map m) async {
    String result = 'error';
    try {
      var apival = saveadhoctaskdocsurl;
      String bodyjson = json.encode(m);
      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    }
    debugPrint(result);
    return result;
  }
}
