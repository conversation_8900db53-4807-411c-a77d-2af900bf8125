import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/common/document.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

class DocumentsServices {
  static Future<Map<String, dynamic>?> fetchDocuments(int? folderid) async {
    debugPrint('--------fetchDocuments------------');

    Map<String, dynamic>? tagObjs;
    String url = documentsurl;
    try {
      var filter;
      Map<String, dynamic> dynamicMap = {
        'a': ['parent_folder_id', 'EXACT', folderid.toString()],
      };
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      tagObjs = await CommonServices.fetchDynamicApi(url, payloadObj: filter);
    } catch (e) {
      debugPrint('fetchDocuments>>>>>>>$e');
    }
    return tagObjs;
  }

  static Future<String> uploadFiles({String? entityType, int? entityId, int? folderId, List<String>? paths}) async {
    String result = 'error';
    String apival = adddmsurl + '/$entityType' + '/$entityId' + '/$folderId';
    try {
      await ApiService.postuploaddata(apival: apival, paths: paths);
      result = 'success';
    } catch (error) {
      debugPrint('$error');
    }
    return result;
  }

  static Future<String> deletefilesbyid(Map m) async {
    String result = 'error';
    try {
      var apival = deletefilesurl;
      String bodyjson = json.encode(m);
      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    } finally {}
    debugPrint(result);
    return result;
  }
}
