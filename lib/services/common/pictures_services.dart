import 'dart:math';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/common/photo.dart';

class PhotoServices {
  static Future<List<Photo>> fetchPhotos(String entityType, int? entityId) async {
    String apival = picturesurl + '/' + entityId.toString() + '/' + entityType;
    List<Photo> tagObjs = <Photo>[];
    try {
      var resMap = await ApiService.get(apival);
      //var tagObjsJson;
      //if (resMap.isNotEmpty) {
      //if (resMap['status'] == 0) {
      debugPrint(resMap);
      if (resMap != null) {
        var tagObjsJson = jsonDecode(resMap)['Pictures'] as List?;
        if (tagObjsJson != null) {
          tagObjs = tagObjsJson.map((tagJson) => Photo.fromJson(tagJson)).toList();
        }
      }
      // }
      //}
    } catch (e) {
      debugPrint('fetchPhotos>>>>>>>$e');
    }
    return tagObjs;
  }

  static Future<String> addPhoto({required String entityType, int? entityId, var path, String? name, String? description}) async {
    String result = 'error';

    StringBuffer apiVal = StringBuffer();
    apiVal.write('$uploadpictureurl/$entityId/$entityType');
    if ((name != null && name != '') || (description != null && description != '')) {
      apiVal.write('?');
      if (name == null || name == '') {
        String tempName = DateTime.now().toString();
        name = tempName.replaceAll(' ', '_');
        name = '$entityType' + '_' + '$entityId' + '_' + name;
      }
      apiVal.write('picName=$name');
      if (description != null && description != '') {
        apiVal.write('&picDesc=$description');
      }
    }

    debugPrint('apiVal>>>>>>>$apiVal');
    try {
      await ApiService.postuploaddata(apival: apiVal.toString(), imgfrom: 'camera', camFile: path);
      result = 'success';
    } catch (error) {
      debugPrint('$error');
    }
    return result;
  }

  static Future<String> addPhotos(
      {required String entityType, int? entityId, required List<String> pathFiles, String? name, String? description}) async {
    String result = 'error';
    StringBuffer apiVal = StringBuffer();
    apiVal.write('$uploadpictureurl/$entityId/$entityType');
    if ((name != null && name != '') || (description != null && description != '')) {
      apiVal.write('?');
      if (name == null || name == '') {
        String tempName = DateTime.now().toString();
        name = tempName.replaceAll(' ', '_');
        name = '$entityType' + '_' + '$entityId' + '_' + name;
      }
      apiVal.write('picName=$name');
      if (description != null && description != '') {
        apiVal.write('&picDesc=$description');
      }
    }

    debugPrint('apiVal>>>>>>>$apiVal');

    try {
      await ApiService.postuploaddata(apival: apiVal.toString(), paths: pathFiles);
      result = 'success';
    } catch (error) {
      debugPrint('$error');
    }
    return result;
  }

  static Future<String> updatePhotoData(int picId, {String? name, String? description}) async {
    String result = 'error';

    Map<String, dynamic> dynamicMap = {'pic_id': picId, 'pic_name': name, 'pic_desc': description};
    String obj = json.encode(dynamicMap);
    try {
      await ApiService.post(editphotourl, payloadObj: obj);
      result = 'success';
    } catch (error) {
      result = 'error';
      debugPrint('$error');
    }
    return result;
  }

  static Future<String> uploadphotos({required String entityType, int? entityId, List<String>? paths}) async {
    String result = 'error';
    String apival = '$uploadphotosurl/$entityId/$entityType';
    try {
      await ApiService.postuploaddata(apival: apival, paths: paths);
      result = 'success';
    } catch (error) {
      debugPrint('$error');
    }
    return result;
  }

  static Future<String> deletePhotos(Map m) async {
    String result = 'error';
    try {
      var apival = deletepicsurl;

      String bodyjson = json.encode(m);

      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    }
    debugPrint(result);
    return result;
  }
}
