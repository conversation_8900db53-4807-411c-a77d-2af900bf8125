import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/common/entity_data.dart';
import 'package:tangoworkplace/models/ta_admin/entitystatus.dart';
import '../models/lookup_values.dart';
import '../models/mytasks/workflow/user_pending_tasks.dart';
import '../utils/common_utils.dart';
import '../utils/preferences_utils.dart';
import '../utils/connections.dart';
import '../utils/request_api_utils.dart';
import 'mytasks/workflow/userpendingtasks_services.dart';

class CommonServices {
  static Future<Map<String, dynamic>?> fetchDynamicApi(
  String apiPath, {
  dynamic payloadObj,
  String? size,
  String? offset,
}) async {
  debugPrint('--------fetchDynamicApi------------');

  final String apiUrl = '$dynamicsearchrul$apiPath';
  Map<String, dynamic>? responseMap;

  try {
    // Prepare default payload if none is provided
    payloadObj ??= jsonEncode({
      "pagination": {
        "offset": offset ?? ConstHelper.pageOffset.toString(),
        "size": size ?? ConstHelper.pageSize.toString(),
      },
    });

    debugPrint('Request Payload: $payloadObj');

    final String response = await ApiService.post(apiUrl, payloadObj: payloadObj);

    if (response.isNotEmpty) {
      responseMap = jsonDecode(response) as Map<String, dynamic>?;
    }
  } catch (e, stackTrace) {
    debugPrint('Error in fetchDynamicApi: $e');
    debugPrint('StackTrace: $stackTrace');
  }

  return responseMap;
}

  static Future<dynamic> postItem({Map? m, String? url}) async {
    String result = 'error';
    try {
      //debugPrint('url    ' + url);
      String bodyjson = jsonEncode(m);

      debugPrint('bodyjson    ' + bodyjson);

      var resMap = await ApiService.post(url, payloadObj: bodyjson);
      //debugPrint('resMap    $resMap');
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (e) {
      debugPrint('postItem -----$e');
    }
    return result;
  }

  static String getFilter(Map<String, dynamic> map, {int? offset, int? size, String? sortby, String? sortorder}) {
    StringBuffer filtstr = new StringBuffer();
    StringBuffer str = new StringBuffer();
    String? obj;
    String pagindata = jsonEncode({
      "pagination": {"offset": offset ?? 1, "size": size ?? 100}
    });
    map.forEach((k, v) {
      debugPrint('${k}: ${v}');
      List<dynamic> list = List.castFrom(v);
      if (obj != null) str.write(',');
      str.write('{"field":"' + list[0]);
      str.write('","operator": "' + list[1]);
      str.write('","value": "' + list[2] + '"}');
      obj = 'init';
    });
    filtstr.write('{ "filters": [');
    filtstr.write(str.toString());
    filtstr.write('],');
    filtstr.write('"pagination": {');
    filtstr.write('"offset": ${offset ?? 1},');
    filtstr.write('"size": ${size ?? 100} }');
    if (sortby != null) {
      sortorder = sortorder ?? 'ASC';
      filtstr.write(',"sortby": [ {');
      filtstr.write('"field": "$sortby",');
      filtstr.write('"order": "$sortorder" }');
      filtstr.write(' ]');
    }

    //filtstr.write(pagindata);
    filtstr.write('}');
    return filtstr.toString();
  }

  static Future<List<EntityStatus>> getStatuses(String entitytype) async {
    Map<String, dynamic>? resMap;
    List<EntityStatus> statuslov = <EntityStatus>[];
    try {
      // statuslov.clear();
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_type', 'EXACT', entitytype],
      };
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(entitystatusesurl, payloadObj: payloadObj);
      //debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        statuslov = tagObjsJson.map((tagJson) => EntityStatus.fromJson(tagJson)).toList();
        statuslov.insert(0, EntityStatus(statusCode: '', status: 'Select', displayFlag: 'Y'));
      }
    } catch (e) {
      debugPrint('fetchDynamicApi getStatusLov>>>>>>>$e');
    }
    return statuslov;
  }

  static Future<RxList<LookupValues>> getLookups(String lovtype, RxList<LookupValues> lov, {String? tag, bool? ext}) async {
    try {
      lov.value.clear();
      lov.value = await (fetchLovs(lovtype, tag ?? '', ext ?? false) as Future<List<LookupValues>>);
      lov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));
    } catch (error) {
      debugPrint('$error');
    }
    return lov;
  }

  //calling rest api for Multiple Lookups
  static Future<dynamic> fetchMultiLovs(String lookupcodes, {bool? ext}) async {
    String apival = commondataurl + '/multilov';
    //if (ext) apival = commondataurl + '/extlov';
    apival = apival + '?lookupcode=' + lookupcodes;
    var tagObjsJson;
    try {
      var resMap = await ApiService.get(apival);

      tagObjsJson = jsonDecode(resMap) as Map?;
    } catch (e) {
      debugPrint('fetchLovs>>>>>>>$e');
    }
    return tagObjsJson;
  }

  static Future<String> getTaskCount({var taskname, dynamic payloadObj, required var url}) async {
    Map<String, dynamic>? resMap;
    List<UserPendingTask> utasks;
    var taskcnt;
    debugPrint('--------getting $taskname task count------ ');
    try {
      resMap = await CommonServices.fetchDynamicApi(url, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            utasks = tagObjsJson.map((tagJson) => UserPendingTask.fromJson(tagJson)).toList();
            debugPrint('User Tasks Count------ ${utasks?.length}');
            if (utasks != null && utasks.length > 0) {
              debugPrint('cccccc------ ${tagObjsJson}');
              taskcnt = utasks?.first?.count?.toString();

              debugPrint('$taskname  count------ $taskcnt');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('$e');
      taskcnt = null;
    }
    return taskcnt;
  }

  static Future<Map?> userEntityEditAccessService({required String entitytype, required String entityid}) async {
    String apival;
    Map? res;

    List<EntityData> tagObjs;
    try {
      apival = userentityeditaccessurl + '?entitytype=' + entitytype + '&entityid=' + entityid;
      var resMap = await ApiService.get(apival);

      var tagObjsJson = jsonDecode(resMap);
      if (tagObjsJson != null) {
        debugPrint('tagObjsJson>>>>>>> ${tagObjsJson.toString()}');
        var status = tagObjsJson['status'] as int?;
        if (status == 0) {
          res = tagObjsJson['params'] as Map?;

          // if (vmap['result'] != null && vmap['result'] == 'Y' && vmap['role_flag'] != null && vmap['role_flag'] == 'Y')
          //   res = 'Y';
          // else if (vmap['result'] != null && vmap['result'] == 'CN') res = 'error';
        }
      }
    } catch (e) {
      debugPrint('userEntityEditAccessService>>>>>>>$e');
    }

    return res;
  }

  static Future<Map?> userRoleAccessService({required String entitytype, required String module}) async {
    Map? res;
    Map m = {
      'module': module,
      'entitytype': entitytype,
    };
    try {
      var resMap = await ApiService.post(userroleaccessurl, payloadObj: jsonEncode(m));

      var tagObjsJson = jsonDecode(resMap);
      if (tagObjsJson != null) {
        debugPrint('tagObjsJson>>>>>>> ${tagObjsJson.toString()}');
        var status = tagObjsJson['status'] as int?;
        if (status == 0) {
          res = tagObjsJson['params'] as Map?;
        }
      }
    } catch (e) {
      debugPrint('userRoleAccessService>>>>>>>$e');
    }

    return res;
  }
}

//calling rest api for Lookups
Future<List<LookupValues>?> fetchLovs(String lookupcode, String tag, bool ext) async {
  String apival = commondataurl + '/lov';
  if (ext) apival = commondataurl + '/extlov';
  apival = apival + '?lookupcode=' + lookupcode + '&tag=' + Uri.encodeComponent(tag);
  List<LookupValues>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);

    var tagObjsJson = jsonDecode(resMap)['Lov'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
    }
  } catch (e) {
    debugPrint('fetchLovs>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<EntityData>?> fetchEntities(String entityType, String filter) async {
  String? apival;
  if (entityType == 'STORE') apival = commondataurl + '/stores' + filter;
  if (entityType == 'SR_LOCATION') apival = getsrlocationsurl + filter;
  List<EntityData>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['EntityData'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => EntityData.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchEntities>>>>>>>$e');
  }
  return tagObjs;
}
