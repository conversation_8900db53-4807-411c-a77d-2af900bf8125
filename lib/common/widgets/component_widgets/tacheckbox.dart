import 'package:flutter/material.dart';

import '../../common_import.dart';
import '../../component_utils.dart';
import '../components.dart';

/// Use CheckboxListTile as part of Form
class CheckboxListTileFormField extends FormField<bool> {
  CheckboxListTileFormField({
    Key? key,
    Widget? title,
    BuildContext? context,
    FormFieldSetter<bool>? onSaved,
    FormFieldValidator<bool>? validator,
    bool initialValue = false,
    ValueChanged<bool?>? onChanged,
    AutovalidateMode? autovalidateMode,
    bool enabled = true,
    bool dense = false,
    Color? errorColor,
    Color? activeColor,
    Color? checkColor,
    ListTileControlAffinity controlAffinity = ListTileControlAffinity.leading,
    EdgeInsetsGeometry? contentPadding,
    bool autofocus = false,
    Widget? secondary,
  }) : super(
          key: key,
          onSaved: onSaved,
          validator: validator,
          initialValue: initialValue,
          autovalidateMode: autovalidateMode,
          builder: (FormFieldState<bool> state) {
            errorColor ??= (context == null ? Colors.red : Theme.of(context).colorScheme.error);

            return CheckboxListTile(
              title: title,
              dense: dense,
              activeColor: activeColor,
              checkColor: checkColor,
              value: state.value,
              onChanged: enabled
                  ? (value) {
                      state.didChange(value);
                      if (onChanged != null) onChanged(value);
                    }
                  : null,
              subtitle: state.hasError
                  ? Text(
                      state.errorText!,
                      style: TextStyle(color: errorColor),
                    )
                  : null,
              controlAffinity: controlAffinity,
              secondary: secondary,
              contentPadding: contentPadding,
              autofocus: autofocus,
            );
          },
        );
}

/// Use Icon as checkbox
class TaFormCheckBoxicon extends FormField<bool> {
  TaFormCheckBoxicon({
    Key? key,
    BuildContext? context,
    FormFieldSetter<bool>? onSaved,
    bool value = false,
    ValueChanged<bool?>? onChanged,
    AutovalidateMode? autovalidateMode,
    bool enabled = true,
    IconData trueIcon = Icons.check_box_outlined,
    IconData falseIcon = Icons.check_box_outline_blank,
    Color? trueIconColor,
    Color? falseIconColor,
    Color? disabledColor,
    double padding = 1.0,
    double? iconSize,
    String? label,
  }) : super(
          key: key,
          onSaved: onSaved,
          initialValue: value,
          autovalidateMode: autovalidateMode,
          builder: (FormFieldState<bool> state) {
            trueIconColor = ComponentUtils.primecolor;
            falseIconColor = CommonUtils.createMaterialColor(Color(0XFF737373));
            //trueIconColor ??= (context == null ? null : Theme.of(context).colorScheme.secondary);

            return Container(
              padding: EdgeInsets.only(left: 15, right: 11),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(label!),
                  Padding(
                    padding: EdgeInsets.zero,
                    child: state.value!
                        ? _createTappableIcon(
                            state, enabled, enabled ? trueIcon : falseIcon, onChanged, trueIconColor, disabledColor, iconSize, value)
                        : _createTappableIcon(state, enabled, falseIcon, onChanged, falseIconColor, disabledColor, iconSize, value),
                  ),
                ],
              ),
            );
          },
        );

  static Widget _createTappableIcon(
    FormFieldState<bool> state,
    bool enabled,
    IconData icon,
    ValueChanged<bool?>? onChanged,
    Color? iconColor,
    Color? disabledColor,
    double? iconSize,
    bool value,
  ) {
    return IconButton(
      onPressed: enabled
          ? () {
              state.didChange(!state.value!);
              if (onChanged != null) onChanged(state.value);
            }
          : null,
      icon: Icon(icon, color: enabled ? iconColor : disabledColor, size: iconSize),
    );
  }
}
