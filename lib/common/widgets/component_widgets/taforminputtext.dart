import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'dart:math' as math;
import 'package:flutter/services.dart';

class TaFormInputText extends StatelessWidget {
  final label;
  final value;
  final icon;
  final int? maxLines;
  final int? minLines;
  final textInputAct;
  bool? readOnly;
  final TextEditingController? controller;
  final String? errortext;
  final TextInputType? textInputType;
  final String? hintText;
  final Icon? prefixIcon;
  final Icon? suffixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  final validate;
  final String? validateMsg;
  final TextInputType? keyboard;
  final TextInputAction? actionKeyboard;
  final Function? onChanged;
  final Function? onSubmitField;
  final Function? onFieldTap;
  final Function? onSaved;
  final inputFormatters;
  final suffixbutton;
  final inputtype;
  TaFormInputText({
    Key? key,
    this.label,
    this.value,
    this.icon,
    this.maxLines,
    this.controller,
    this.textInputAct,
    this.readOnly,
    this.keyboard,
    this.minLines,
    this.onChanged,
    this.actionKeyboard,
    this.defaultText,
    this.errortext,
    this.focusNode,
    this.hintText,
    this.obscureText,
    this.onFieldTap,
    this.onSaved,
    this.onSubmitField,
    this.prefixIcon,
    this.suffixIcon,
    this.textInputType,
    this.validate,
    this.validateMsg,
    this.inputFormatters,
    this.suffixbutton,
    this.inputtype,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    readOnly = readOnly ?? false;
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextFormField(
          controller: controller,
          initialValue: value,
          minLines: minLines ?? 1,
          maxLines: maxLines ?? 1,
          readOnly: readOnly ?? false,
          cursorColor: Theme.of(context).primaryColor,
          textInputAction: textInputAct ?? TextInputAction.done,
          style: TextStyle(color: Colors.black, fontSize: 14),
          keyboardType: keyboard,
          inputFormatters: inputFormatters,
          obscureText: obscureText ?? false,

          //inputFormatters: [DecimalTextInputFormatter(decimalRange: 2)],
          // keyboardType: TextInputType.numberWithOptions(decimal: true),
          onSaved: onSaved as void Function(String?)?,
          onChanged: onChanged as void Function(String)?,
          validator: validate,
          decoration: InputDecoration(
            errorText: errortext ?? null,
            suffixIcon: suffixbutton,
            labelText: label,
            filled: true,
            fillColor: readOnly! && inputtype != 'inputlov' ? Colors.grey.shade200 : Colors.white70,
            floatingLabelBehavior: FloatingLabelBehavior.always,

            enabledBorder: readOnly! && inputtype != 'inputlov'
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                  ),
            focusedBorder: readOnly! && inputtype != 'inputlov'
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                  ),

            contentPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            isDense: true,
            errorStyle: TextStyle(
              //color: Colors.red,
              // fontSize: 12.0,
              //fontWeight: FontWeight.w300,
              fontStyle: FontStyle.normal,
              //letterSpacing: 1.2,
            ),
            // errorBorder: OutlineInputBorder(
            //   borderSide: BorderSide.none,
            // ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFb10c00)), width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFb10c00)), width: 2),
            ),
          ),
        ),
      ),
    );
  }
}

class TaInputTextField extends StatelessWidget {
  final title;
  final value;
  final icon;
  final dateSelect;
  final int? maxLines;
  final int? minLines;
  final textInputAct;
  bool? readOnly;
  final Function(String)? onChanged;
  final TextInputType? keyboard;
  final TextEditingController? controller;
  final String? errortext;
  TaInputTextField(
      {Key? key,
      this.title,
      this.value,
      this.icon,
      this.dateSelect,
      this.maxLines,
      this.controller,
      this.textInputAct,
      this.readOnly,
      this.keyboard,
      this.minLines,
      this.onChanged,
      this.errortext})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    readOnly = readOnly ?? false;
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextField(
          controller: controller,
          minLines: minLines ?? 1,
          maxLines: maxLines,
          readOnly: readOnly ?? false,
          cursorColor: Theme.of(context).primaryColor,
          textInputAction: textInputAct ?? TextInputAction.done,
          style: TextStyle(color: Colors.black, fontSize: 14),
          keyboardType: keyboard,
          onChanged: (text) {
            if (onChanged != null) onChanged!(text);
          },
          decoration: InputDecoration(
            errorText: errortext ?? null,
            //prefixIcon: widget.prefixIcon,
            //hintText: widget.hintText,
            labelText: title,
            filled: true,
            fillColor: readOnly! ? Colors.grey.shade200 : Colors.white70,
            floatingLabelBehavior: FloatingLabelBehavior.always,

            enabledBorder: readOnly!
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                  ),
            focusedBorder: readOnly!
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
                  ),

            contentPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            isDense: true,
            errorStyle: TextStyle(
              //color: Colors.red,
              // fontSize: 12.0,
              //fontWeight: FontWeight.w300,
              fontStyle: FontStyle.normal,
              //letterSpacing: 1.2,
            ),
            // errorBorder: OutlineInputBorder(
            //   borderSide: BorderSide.none,
            // ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFb10c00)), width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFb10c00)), width: 2),
            ),
          ),
        ),
      ),
    );
  }
}

class DecimalTextInputFormatter extends TextInputFormatter {
  DecimalTextInputFormatter({this.decimalRange}) : assert(decimalRange == null || decimalRange > 0);

  final int? decimalRange;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue, // unused.
    TextEditingValue newValue,
  ) {
    TextSelection newSelection = newValue.selection;
    String truncated = newValue.text;

    if (decimalRange != null) {
      String value = newValue.text;

      if (value.contains(".") && value.substring(value.indexOf(".") + 1).length > decimalRange!) {
        truncated = oldValue.text;
        newSelection = oldValue.selection;
      } else if (value == ".") {
        truncated = "0.";

        newSelection = newValue.selection.copyWith(
          baseOffset: math.min(truncated.length, truncated.length + 1),
          extentOffset: math.min(truncated.length, truncated.length + 1),
        );
      }

      return TextEditingValue(
        text: truncated,
        selection: newSelection,
        composing: TextRange.empty,
      );
    }
    return newValue;
  }
}
