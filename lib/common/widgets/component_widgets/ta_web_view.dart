import 'dart:io';

import 'package:dio/dio.dart';
import 'package:external_path/external_path.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tangoworkplace/common/component_utils.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

class TaWebView extends StatefulWidget {
  final String url;
  final String title;
  final String host;
  final String? source;

  const TaWebView(
      {required this.url,
      required this.title,
      required this.host,
      super.key,
      this.source});

  @override
  State<StatefulWidget> createState() => _TaWebViewState();
}

class _TaWebViewState extends State<TaWebView> {
  
  final _key = UniqueKey();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final cookieManager = CookieManager.instance();
  int _position = 1;
  String _host = ''; 

  _TaWebViewState();

  @override
  void initState() {
    super.initState();

    debugPrint(widget.url);
    cookieManager.deleteAllCookies();
    _host = widget.host.replaceAll('https://', '');
  }

  @override
  dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    cookieManager.deleteAllCookies();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final messengerState = ScaffoldMessenger.of(context);
    
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(widget.title, style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 4,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: SafeArea(
        child: IndexedStack(
          index: _position,
          children: <Widget>[
            Stack(
              children: <Widget>[
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(),
                ),
                Positioned(
                    top: 0,
                    right: 0,
                    left: 0,
                    bottom: 0,
                    child: ClipRRect(
                        child: Column(
                      children: [
                        Expanded(
                          child: InAppWebView(
                            gestureRecognizers: {
                              Factory<VerticalDragGestureRecognizer>(
                                () => VerticalDragGestureRecognizer(),
                              ),
                              Factory<HorizontalDragGestureRecognizer>(() => HorizontalDragGestureRecognizer()),
                              Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
                            },
                            key: _key,
                            initialUrlRequest: URLRequest(
                                url: WebUri.uri(Uri.parse(widget.url))),
                            initialSettings: InAppWebViewSettings(
                              javaScriptEnabled: true,
                              transparentBackground: true,
                              supportMultipleWindows: true,
                              useOnDownloadStart: true,
                              builtInZoomControls: true,
                              supportZoom: true,
                              allowsLinkPreview: true,
                              allowFileAccess: true,
                              allowUniversalAccessFromFileURLs: true,
                              allowContentAccess: true,
                            ),
                            onWebViewCreated: (webViewController) async {
                              String? sessionid =
                                  await SharedPrefUtils.readPrefStr(
                                      ConstHelper.sessionIdvar);
                              debugPrint('sessionid: $sessionid');

                              await webViewController.evaluateJavascript(
                                  source:
                                      'sessionStorage.setItem("JSESSIONID", "$sessionid");');

                              await cookieManager.setCookie(
                                url: WebUri.uri(Uri.parse("https://$_host")),
                                name: "JSESSIONID",
                                value: sessionid!,
                                domain: _host,
                                expiresDate: DateTime.now()
                                    .add(const Duration(days: 1))
                                    .millisecond,
                                isHttpOnly: false,
                              );

                              webViewController.loadUrl(
                                  urlRequest: URLRequest(
                                      url: WebUri.uri(Uri.parse(widget.url))));
                            },
                            onDownloadStartRequest:
                                (controller, downloadStartRequest) async {
                              debugPrint(
                                  'Download file: ${downloadStartRequest.suggestedFilename} - ${downloadStartRequest.contentLength} - ${downloadStartRequest.url}');
                              messengerState.clearSnackBars();    
                              messengerState.showSnackBar(
                                SnackBar(
                                    content: Text(
                                        'Downloading file: ${downloadStartRequest.suggestedFilename}')),
                              );

                              setState(() {
                                _position = 1;
                              });

                              await _downloadFile(
                                downloadStartRequest.url.toString(),
                                downloadStartRequest.suggestedFilename,
                                messengerState,
                              );

                              setState(() {
                                _position = 0;
                              });
                            },
                            onLoadStart: (_, __) {
                              setState(() {
                                _position = 1;
                              });
                            },
                            onLoadStop: (_, __) {
                              setState(() {
                                _position = 0;
                              });
                            },
                          ),
                        ),
                      ],
                    )))
              ],
            ),
            const ProgressIndicatorCust(),
          ],
        ),
      ),
    );
  }

  Future<String?> _getDownloadPath() async {
    try {
      if (Platform.isIOS) {
        final directory = await getApplicationDocumentsDirectory();
        return directory.path;
      } else {
        await Permission.manageExternalStorage.request();
        return await ExternalPath.getExternalStoragePublicDirectory(
            ExternalPath.DIRECTORY_DOWNLOADS);
      }
    } catch (err, _) {
      debugPrint('Cannot get download folder path');
    }
    return null;
  }

  Future<void> _downloadFile(String url, String? fileName,
      ScaffoldMessengerState messengerState) async {
    try {
      String? path = await _getDownloadPath() ?? (await getExternalStorageDirectory())?.path ?? '';
      String name = fileName ?? url.split('/').last;
      String filePath = '$path/$name';
      Dio dio = Dio();
      await dio.download(url, filePath);

      debugPrint("File downloaded to: $filePath");
      messengerState.clearSnackBars();
      messengerState.showSnackBar(
        SnackBar(content: Text('Downloaded to: $filePath')),
      );
    } catch (e) {
      debugPrint("Download failed: $e");
      messengerState.clearSnackBars();
      messengerState.showSnackBar(
        SnackBar(content: Text('Download failed: $e')),
      );
    }
  }
}