import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../component_utils.dart';

class TaTable extends StatelessWidget {
  final List<String?>? columns;
  final List<DataRow>? rows;
  final ScrollController? scrollController;
  TaTable({Key? key, this.columns, this.rows,this.scrollController}) : super(key: key);

  List<DataColumn> getColumns(List<String?> columns) => columns
      .map((String? column) => DataColumn(
            label: Text(column!),
            // onSort: onSort,
          ))
      .toList();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller: scrollController,
      physics: BouncingScrollPhysics(),
      scrollDirection: Axis.vertical,
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        scrollDirection: Axis.horizontal,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          //width: double.infinity,
          //height: 110,
          margin: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          padding: EdgeInsets.only(right: 1, left: 1, top: 1),
          child: DataTable(
            //border: TableBorder.all(color: Colors.black26, width: 1),
            columns: getColumns(columns!),
            headingRowColor: MaterialStateProperty.all(Color(0xFFe0e1eb)),
            rows: rows!,
            dataRowHeight: 22,
            dataTextStyle: TextStyle(color: ComponentUtils.primary, fontSize: 12),
            dataRowColor: MaterialStateProperty.all<Color>(Colors.white),
            dividerThickness: 1,
            columnSpacing: 20,
            headingRowHeight: 25,
            headingTextStyle: TextStyle(color: ComponentUtils.primary, fontSize: 14, fontWeight: FontWeight.bold),
            horizontalMargin: 10,
            showBottomBorder: true,
          ),
        ),
      ),
    );
  }
}


