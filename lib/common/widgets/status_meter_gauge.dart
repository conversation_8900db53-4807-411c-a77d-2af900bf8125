import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/component_utils.dart';

class StatusMeterGauge extends StatelessWidget {
  var size;
  var statusvalue;
  StatusMeterGauge({super.key, this.size, this.statusvalue});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: size, height: size,
        //child: TweenAnimationBuilder(
        // tween: Tween(begin: 0.0, end: 0.5),
        // duration: Duration(seconds: 4),
        // builder: (context, value, child) {
        //   int percentage = (value * 100).ceil();
        //   return Container(
        //     width: size,
        //     height: size,
        child: Stack(
          children: [
            ShaderMask(
              shaderCallback: (rect) {
                return SweepGradient(
                    startAngle: 0.0,
                    endAngle: 3.14 * 2,
                    stops: [statusvalue, statusvalue],
                    // 0.0 , 0.5 , 0.5 , 1.0
                    center: Alignment.center,
                    colors: [ComponentUtils.primecolor, Colors.grey.withAlpha(55)]).createShader(rect);
              },
              child: Container(
                width: size,
                height: size,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            ),
            Center(
              child: Container(
                width: size - 20,
                height: size - 20,
                decoration: const BoxDecoration(color: Colors.white, shape: BoxShape.circle),
                // child: Center(
                //     child: Text(
                //   "$percentage",
                //   style: TextStyle(fontSize: 40),
                // )),
              ),
            )
          ],
        ),
        //);
        //  },
        // ),
      ),
    );
  }
}
