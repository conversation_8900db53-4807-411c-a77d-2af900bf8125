import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';
import '../../utils/common_utils.dart';
import '../../utils/constvariables.dart';
import '../../utils/preferences_utils.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '../component_utils.dart';
import '../progess_indicator_cust.dart';

class WebViewContainer extends StatefulWidget {
  final url;
  final title;
  final source;
  final host;
  WebViewContainer(this.url, this.title, this.host, {this.source});
  @override
  createState() => _WebViewContainerState(this.url, this.title, this.source, this.host);
}

class _WebViewContainerState extends State<WebViewContainer> {
  var _url;
  var _title;
  var _source;
  var _host;
  final _key = UniqueKey();

  WebViewController? _controller;
  _WebViewContainerState(
    this._url,
    this._title,
    this._source,
    this._host,
  );
  num position = 1;
  bool _hasError = false;

  @override
  initState() {
    super.initState();
    _host = _host.replaceAll('https://', '');
    debugPrint('$_host');
    debugPrint('url   $_url');
    debugPrint('_source   $_source');
    if (Platform.isAndroid) {
      WebView.platform = SurfaceAndroidWebView();
    }
  }

  @override
  dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(
          _title,
          style: ComponentUtils.appbartitlestyle,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            // Get.off(() => HomeScreen());
            Get.back();
          },
        ),
        // actions: [
        //   IconButton(
        //     icon: const Icon(Icons.refresh),
        //     color: ComponentUtils.primecolor,
        //     onPressed: ()

        //   ),
        // ],
      ),
      body: IndexedStack(
        index: position as int?,
        children: <Widget>[
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: WebView(
                  zoomEnabled: true,
                  gestureNavigationEnabled: true,
                  gestureRecognizers: [
                    Factory(
                      () => VerticalDragGestureRecognizer(),
                    ),
                    Factory(() => HorizontalDragGestureRecognizer()),
                    Factory(() => TapGestureRecognizer()),
                  ].toSet(),
                  key: _key,
                  javascriptMode: JavascriptMode.unrestricted,
                  onWebViewCreated: (WebViewController webViewController) async {
                    var sessionid = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);

                    await WebviewCookieManager().setCookies([
                      Cookie('JSESSIONID', '$sessionid')
                        ..domain = _host
                        ..httpOnly = false
                        ..expires = DateTime.now().add(const Duration(minutes: 30))
                    ]);
                    webViewController.loadUrl(_url);
                    //_controller = webViewController;
                  },
                  onPageStarted: (value) {
                    setState(() {
                      position = 1;
                    });
                  },
                  onPageFinished: (value) {
                    setState(() {
                      position = 0;
                    });
                  },
                ),
              ),
            ],
          ),
          const ProgressIndicatorCust(),
        ],
      ),
    );
  }
}

class WebViewContainer1 extends StatefulWidget {
  final url;
  final title;
  final source;
  final host;
  WebViewContainer1(this.url, this.title, this.host, {this.source});
  @override
  createState() => _WebViewContainerState1(this.url, this.title, this.source, this.host);
}

class _WebViewContainerState1 extends State<WebViewContainer1> {
  var _url;
  var _title;
  var _source;
  String _host;
  final _key = UniqueKey();
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  _WebViewContainerState1(this._url, this._title, this._source, this._host);
  WebViewController? _controller;
  final cookieManager = WebviewCookieManager();

  num position = 1;
  @override
  void initState() {
    super.initState();
    _host = _host.replaceAll('https://', '');
    debugPrint('$_host');
    debugPrint(_url);
    debugPrint('---------------- $_source');

    if (Platform.isAndroid) {
      WebView.platform = SurfaceAndroidWebView();
    }
    cookieManager.clearCookies();
  }

  @override
  dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    cookieManager.clearCookies();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //if (_source == 'CADVIEW') SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);

    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(_title, style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 4,
        leading: new IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');

            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: SafeArea(
        //bottom: false,
        child: IndexedStack(
          index: position as int?,
          children: <Widget>[
            Stack(
              children: <Widget>[
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(),
                ),
                Positioned(
                    top: 00,
                    right: 0,
                    left: 0,
                    bottom: 0,
                    child: ClipRRect(
                        // borderRadius: BorderRadius.circular(9),
                        child: Container(
                            // color: Colors.white,
                            //padding: EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 0.0),
                            child: Column(
                      children: [
                        // LinearProgressIndicator(
                        //   value: progress,
                        //   backgroundColor: Colors.grey,
                        //   color: Colors.red,
                        // ),
                        Expanded(
                          child: WebView(
                            zoomEnabled: true,
                            gestureNavigationEnabled: true,
                            gestureRecognizers: [
                              Factory(
                                () => VerticalDragGestureRecognizer(),
                              ),
                              Factory(() => HorizontalDragGestureRecognizer()),
                              Factory(() => TapGestureRecognizer()),
                            ].toSet(),
                            key: _key,
                            javascriptMode: JavascriptMode.unrestricted,
                            onWebViewCreated: (webViewController) async {
                              var sessionid = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);

                              debugPrint('sessionid      $sessionid');
                              webViewController.runJavascript('sessionStorage.setItem("JSESSIONID", "$sessionid");');

                              await cookieManager.setCookies([
                                Cookie('JSESSIONID', '$sessionid')
                                  ..domain = _host
                                  ..expires = DateTime.now().add(const Duration(minutes: 30))
                                  ..httpOnly = false
                              ]);
                              webViewController.loadUrl(
                                _url,
                              );
                            },
                            onPageStarted: (value) {
                              setState(() {
                                position = 1;
                              });
                            },
                            onPageFinished: (value) {
                              setState(() {
                                position = 0;
                              });
                            },
                          ),
                        ),
                      ],
                    ))))
              ],
            ),
            const ProgressIndicatorCust(),
          ],
        ),
      ),
    );
  }
}
