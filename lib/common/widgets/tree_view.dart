import 'package:flutter/material.dart';

import '../../screens/common/utils/tree/tree_building.dart';
import '../../screens/common/utils/tree/tree_node.dart';

class TreeView extends StatelessWidget {
  final List<TreeNode> nodes;
  final double? indent;
  final double? iconSize;

  TreeView({
    Key? key,
    required List<TreeNode> nodes,
    this.indent = 20,
    this.iconSize,
  })  : nodes = copyTreeNodes(nodes),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return buildNodes(
      nodes,
      indent,
      iconSize,
    );
  }
}