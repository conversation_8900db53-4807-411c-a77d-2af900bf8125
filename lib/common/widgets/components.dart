import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import '../../providers/ta_admin/home_controller.dart';
import '../common_import.dart';
import '../component_utils.dart';

class TaHdrTemplate extends StatelessWidget {
  final String? title;
  final List<Widget>? actions;
  final double? toolbarHeight;
  final double? elevation;
  final Widget? body;
  final BottomAppBar? bottomAppBar;
  final Drawer? drawer;
  TaHdrTemplate({this.title, this.actions, this.toolbarHeight, this.elevation, this.body, this.bottomAppBar, this.drawer});
  @override
  Widget build(BuildContext context) {
    bool isLargeScreen = CommonUtils.isTablet(context);
    return Scaffold(
      appBar: AppBar(
          backgroundColor: Colors.grey,
          actions: actions,
          title: Text(title ?? '', style: TextStyle(fontSize: isLargeScreen ? 16 : 12, fontWeight: FontWeight.bold)),
          toolbarHeight: toolbarHeight,
          elevation: elevation ?? 0),
      body: body,
      drawer: drawer,
      bottomNavigationBar: bottomAppBar,
    );
  }
}

class TaSearchInputText extends StatelessWidget {
  final makeSearch;
  final searchController;
  final hintSearch;
  final plachold;
  final textInputAction;
  final autofocus;
  final height;

  TaSearchInputText(
      {Key? key, this.makeSearch, this.searchController, this.hintSearch, this.plachold, this.textInputAction, this.autofocus, this.height})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: plachold == 'appbar' ? EdgeInsets.symmetric(horizontal: 1, vertical: 1) : EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Material(
        elevation: plachold == 'appbar' ? 0.1 : 2.0,
        borderRadius: BorderRadius.all(Radius.circular(15)),
        child: SizedBox(
          height: height ?? 45.0,
          child: TextField(
            autofocus: autofocus ?? false,
            controller: searchController,
            cursorColor: Theme.of(context).primaryColor,
            textInputAction: textInputAction ?? TextInputAction.search,
            style: const TextStyle(color: Colors.black, fontSize: 14),
            decoration: InputDecoration(
                hintText: hintSearch,
                hintStyle: const TextStyle(color: Colors.black38, fontSize: 14),
                prefixIcon: Material(
                  elevation: 0.0,
                  borderRadius: BorderRadius.all(Radius.circular(30)),
                  child: Icon(Icons.search),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 12)),
            onSubmitted: makeSearch,
          ),
        ),
      ),
    );
  }
}

class TaButton extends StatelessWidget {
  final String? type;
  final onPressed;
  final String? buttonText;
  final Color? color;
  final bool? readOnly;
  final ButtonStyle? btnStyle;

  const TaButton({
    super.key,
    this.type,
    this.onPressed,
    this.buttonText,
    this.color,
    this.readOnly = false,
    this.btnStyle,
  });

  @override
  Widget build(BuildContext context) {
    //ElevatedButton
    if (type == 'elevate') {
      return ElevatedButton(
          style: btnStyle ??
              // readOnly
              //     ? ButtonStyle(
              //         backgroundColor: MaterialStateProperty.resolveWith<Color>(
              //           (Set<MaterialState> states) {
              //             if (states.contains(MaterialState.pressed))
              //               return Theme.of(context).colorScheme.primary.withOpacity(0.5);
              //             else if (states.contains(MaterialState.disabled)) return Colors.green;
              //             return null; // Use the component's default.
              //           },
              //         ),
              //       )
              //     :
              ButtonStyle(
                backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                    if (states.contains(MaterialState.pressed)) return Colors.redAccent;
                    return readOnly == true ? Colors.grey : color ?? Theme.of(context).primaryColor;
                    // Use the component's default.
                  },
                ),
              ),
          onPressed: readOnly == true ? null : onPressed,
          child: Text(
            buttonText ?? '',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ));
      //textbutton
    } else if (type == 'textbtn') {
      return TextButton(
        onPressed: () => onPressed,
        style: TextButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
        child: Text(
          buttonText ?? '',
          style: TextStyle(color: HexColor('#ffffff'), fontWeight: FontWeight.bold),
        ),
      );
    }

    return Container();
  }
}

class TaInputText extends StatefulWidget {
  final Function(String)? onChanged;
  final String? title;
  final String? value;
  final String? hint;
  final IconData? icon;
  final bool? readOnly;
  final bool? dateSelect;
  final int? maxLines;
  final int? minLines;
  final TextInputType? keyboard;
  final TextEditingController? controller;
  final bool withDebounce;

  const TaInputText(
      {super.key,
      this.title,
      this.value,
      this.icon,
      this.hint,
      this.readOnly = false,
      this.onChanged,
      this.controller,
      this.dateSelect = true,
      this.maxLines,
      this.keyboard,
      this.minLines,
      this.withDebounce = false});

  @override
  _StateInputTextField createState() => _StateInputTextField();
}

class _StateInputTextField extends State<TaInputText> {
  TextEditingController ctrl = TextEditingController();
  Timer? _debounce;

  @override
  void initState() {
    if (widget.controller != null) {
      ctrl = widget.controller!;
    } else {
      ctrl.text = widget.value ?? '';
    }

    // ctrl.text = widget.controller?.text != null ? widget.controller.text : widget.value ?? '';

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextField(
          controller: ctrl,
          minLines: widget.minLines ?? 1,
          maxLines: widget.maxLines,
          readOnly: widget.readOnly!,
          cursorColor: Theme.of(context).primaryColor,
          keyboardType: widget.keyboard,

          style: const TextStyle(
            fontSize: 12.0,
          ),
          onChanged: (text) {
            if (widget.onChanged != null) {
              if (widget.withDebounce) {
                if (_debounce?.isActive ?? false) _debounce?.cancel();
                _debounce = Timer(const Duration(milliseconds: 500), () {
                  widget.onChanged!(text);
                });
              } else {
                widget.onChanged!(text);
              }
            }
          },
          // decoration: InputDecoration(
          //   suffixIcon: Container(
          //     height: 0,
          //     width: 0,
          //   ),
          //   labelText: widget.title,
          //   border: widget.readOnly
          //       ? InputBorder.none
          //       : new OutlineInputBorder(
          //           borderRadius: BorderRadius.all(Radius.circular(17)), borderSide: new BorderSide(color: Colors.red)),
          //   contentPadding: EdgeInsets.symmetric(horizontal: 25, vertical: 13),
          //   hintStyle: TextStyle(color: Colors.black87, fontSize: 15),
          //   //floatingLabelBehavior:
          // ),

          decoration: InputDecoration(
            //prefixIcon: widget.prefixIcon,
            //hintText: widget.hintText,
            floatingLabelBehavior: FloatingLabelBehavior.always,
            labelText: widget.title,
            filled: true,
            hintText: widget.hint,
            fillColor: widget.readOnly! ? Colors.grey.shade200 : Colors.white70,

            enabledBorder: widget.readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),
            focusedBorder: widget.readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),

            contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            isDense: true,
            errorStyle: const TextStyle(
              //color: Colors.red,
              // fontSize: 12.0,
              //fontWeight: FontWeight.w300,
              fontStyle: FontStyle.normal,
              //letterSpacing: 1.2,
            ),
            // errorBorder: OutlineInputBorder(
            //   borderSide: BorderSide.none,
            // ),
            errorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
          ),
        ),
      ),
    );
  }
}

class TaInputNumber extends StatefulWidget {
  final Function(String)? onChanged;
  final String? title;
  final String? value;
  final bool? readOnly;
  final int? maxCharacters;
  final int minRange;
  final int maxRange;
  final TextEditingController? controller;

  const TaInputNumber({
    super.key,
    this.title,
    this.value,
    this.readOnly = false,
    this.onChanged,
    this.controller,
    this.maxCharacters,
    this.minRange = 0,
    this.maxRange = 10000000,
  });

  @override
  _StateInputNumberField createState() => _StateInputNumberField();
}

class _StateInputNumberField extends State<TaInputNumber> {
  TextEditingController ctrl = TextEditingController();
  String oldValue = '';

  @override
  void initState() {
    oldValue = widget.value ?? '';
    if (widget.controller != null) {
      ctrl = widget.controller!;
    } else {
      ctrl.text = widget.value ?? '';
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        child: TextFormField(
          controller: ctrl,
          readOnly: widget.readOnly!,
          cursorColor: Theme.of(context).primaryColor,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
          ],
          onChanged: (value) {
            if (int.parse(value) < widget.minRange || int.parse(value) > widget.maxRange) {
              ctrl.text = oldValue;
            } else {
              oldValue = value;
            }
          },
          validator: (value) {
            if (value!.isEmpty) {
              return 'Please enter the value';
            } else if (int.parse(value) < widget.minRange || int.parse(value) > widget.maxRange) {
              return 'The value must be between ${widget.minRange} and ${widget.maxRange}';
            }
            return null;
          },
          style: const TextStyle(
            fontSize: 12.0,
          ),
          decoration: InputDecoration(
            //prefixIcon: widget.prefixIcon,
            //hintText: widget.hintText,
            floatingLabelBehavior: FloatingLabelBehavior.always,
            labelText: widget.title,
            filled: true,
            fillColor: widget.readOnly! ? Colors.grey.shade200 : Colors.white70,

            enabledBorder: widget.readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),
            focusedBorder: widget.readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),

            contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            isDense: true,
            errorStyle: const TextStyle(
              //color: Colors.red,
              // fontSize: 12.0,
              //fontWeight: FontWeight.w300,
              fontStyle: FontStyle.normal,
              //letterSpacing: 1.2,
            ),
            // errorBorder: OutlineInputBorder(
            //   borderSide: BorderSide.none,
            // ),
            errorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
          ),
        ),
      ),
    );
  }
}

class TaTextFormField extends StatefulWidget {
  final TextInputType? textInputType;
  final String? hintText;
  final Widget? prefixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  final TextEditingController? controller;
  final Function? validate;
  final String? validateMsg;
  final TextInputAction? actionKeyboard;
  final Function? onSubmitField;
  final Function? onFieldTap;
  final Function? onSaved;
  final String? labelText;
  final bool? readOnly;
  final Widget? suffixIcon;
  final int? minLines;
  final int? maxLines;

  const TaTextFormField(
      {required this.hintText,
      this.focusNode,
      this.textInputType,
      this.defaultText,
      this.obscureText = false,
      this.controller,
      this.validate,
      this.validateMsg,
      this.actionKeyboard = TextInputAction.next,
      this.onSubmitField,
      this.onFieldTap,
      this.prefixIcon,
      this.onSaved,
      this.labelText,
      this.readOnly = false,
      this.suffixIcon,
      this.minLines = 1,
      this.maxLines});

  @override
  _TaTextFormFieldState createState() => _TaTextFormFieldState();
}

class _TaTextFormFieldState extends State<TaTextFormField> {
  double bottomPaddingToError = 12;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      readOnly: widget.readOnly!,
      obscureText: widget.obscureText ?? false,
      keyboardType: widget.textInputType,
      textInputAction: widget.actionKeyboard,
      focusNode: widget.focusNode,
      minLines: widget.minLines,
      maxLines: widget.maxLines,
      style: TextStyle(
        fontSize: 12.0,
        // fontWeight: FontWeight.w200,
        //fontStyle: FontStyle.normal,
        // letterSpacing: 1.2,
      ),
      initialValue: widget.defaultText,
      decoration: InputDecoration(
        prefixIcon: widget.prefixIcon,
        hintText: widget.hintText,
        labelText: widget.labelText,
        suffixIcon: widget.suffixIcon,
        filled: true,
        fillColor: widget.readOnly! ? Colors.grey.shade200 : Colors.white70,
        floatingLabelBehavior: FloatingLabelBehavior.always,

        enabledBorder: widget.readOnly!
            ? OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 0.5),
              )
            : OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
              ),
        focusedBorder: widget.readOnly!
            ? OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 0.5),
              )
            : OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10.0)),
                borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
              ),
        // hintStyle: TextStyle(
        //   color: Colors.grey,
        //   fontSize: 14.0,
        //   fontWeight: FontWeight.w300,
        //   fontStyle: FontStyle.normal,
        //   letterSpacing: 1.2,
        // ),
        contentPadding: EdgeInsets.only(top: 18, bottom: bottomPaddingToError, left: 15.0, right: 12.0),
        isDense: true,
        errorStyle: TextStyle(
          //color: Colors.red,
          // fontSize: 12.0,
          //fontWeight: FontWeight.w300,
          fontStyle: FontStyle.normal,
          //letterSpacing: 1.2,
        ),
        // errorBorder: OutlineInputBorder(
        //   borderSide: BorderSide.none,
        // ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(10.0)),
          borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFb10c00)), width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(10.0)),
          borderSide: BorderSide(color: CommonUtils.createMaterialColor(Color(0XFFb10c00)), width: 2),
        ),
      ),
      controller: widget.controller,
      validator: (value) {
        if (widget.validate != null) {
          String? resultValidate = widget.validate!(value, widget.validateMsg);
          if (resultValidate != null) {
            return resultValidate;
          }
        }
        return null;
      },
      // onFieldSubmitted: (value) {
      //   if (widget.onSubmitField != null) widget.onSubmitField();
      // },
      // onTap: () {
      //   if (widget.onFieldTap != null) widget.onFieldTap();
      // },
      onSaved: (value) {
        if (widget.onSaved != null) widget.onSaved!(value);
      },
    );
  }
}

String? commonValidation(String value, String messageError) {
  var required = requiredValidator(value, messageError);
  if (required != null) {
    return required;
  }
  return null;
}

String? requiredValidator(value, messageError) {
  if (value.isEmpty) {
    return messageError;
  }
  return null;
}

void changeFocus(BuildContext context, FocusNode currentFocus, FocusNode nextFocus) {
  currentFocus.unfocus();
  FocusScope.of(context).requestFocus(nextFocus);
}

class SpeedDialFloatingButton extends StatefulWidget {
  final List<FloatingIconButton>? iconButton;
  final IconData? icon;
  AnimationController? controller;

  SpeedDialFloatingButton({Key? key, this.iconButton, this.icon, this.controller}) : super(key: key);
  @override
  _SpeedDialFloatingButtonState createState() => _SpeedDialFloatingButtonState();
}

class _SpeedDialFloatingButtonState extends State<SpeedDialFloatingButton> with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Color backgroundColor = Theme.of(context).cardColor;
    Color foregroundColor = Theme.of(context).hintColor;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.iconButton!.length, (int index) {
        Widget child = Container(
          height: 70.0,
          width: 56.0,
          alignment: FractionalOffset.topCenter,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: _controller,
              curve: Interval(0.0, 1.0 - index / widget.iconButton!.length / 2.0, curve: Curves.easeOut),
            ),
            child: FloatingActionButton(
              heroTag: null,
              backgroundColor: backgroundColor,
              mini: true,
              child: Icon(widget.iconButton![index].icon, color: ComponentUtils.primecolor),
              onPressed: widget.iconButton![index].onPressed,
            ),
          ),
        );
        return child;
      }).toList()
        ..add(
          FloatingActionButton(
            heroTag: null,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (BuildContext context, Widget? child) {
                return Transform(
                  transform: new Matrix4.rotationZ(_controller.value * 1 * math.pi),
                  alignment: FractionalOffset.center,
                  child: new Icon(_controller.isDismissed ? widget.icon : Icons.close),
                );
              },
            ),
            onPressed: () {
              if (_controller.isDismissed) {
                _controller.forward();
              } else {
                _controller.reverse();
              }
            },
          ),
        ),
    );
  }
}

class FloatingIconButton {
  final VoidCallback? onPressed;
  final IconData? icon;

  FloatingIconButton({this.onPressed, this.icon});
}

class TaCheckBox extends StatelessWidget {
  final String? label;
  final bool? value;
  final Function(bool?)? onChanged;
  const TaCheckBox({Key? key, this.label, this.value, this.onChanged}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      //width: 400,
      // height: 40,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(0.0),
          child: Container(
            // decoration: BoxDecoration(
            //   //border: Border.all(color: Colors.greenAccent),
            // ),
            child: CheckboxListTile(
                //contentPadding: EdgeInsets.all(0),

                title: Text(
                  label!,
                  style: TextStyle(color: Colors.black, fontSize: 14),
                ),
                value: value ?? false,
                //selected: value ?? false,
                onChanged: (bool? value) {
                  debugPrint('value changed to $value');
                  onChanged!(value);
                }),
          ),
        ),
      ),
    );
  }
}

class CheckboxFormField extends FormField<bool> {
  CheckboxFormField(
      {Widget? title,
      FormFieldSetter<bool>? onSaved,
      FormFieldValidator<bool>? validator,
      bool initialValue = false,
      bool autovalidate = false})
      : super(
            onSaved: onSaved,
            validator: validator,
            initialValue: initialValue,
            builder: (FormFieldState<bool> state) {
              return CheckboxListTile(
                dense: state.hasError,
                title: title,
                value: state.value,
                onChanged: state.didChange,
                subtitle: state.hasError
                    ? Builder(
                        builder: (BuildContext context) => Text(
                          state.errorText!,
                          style: TextStyle(color: Theme.of(context).colorScheme.error),
                        ),
                      )
                    : null,
                controlAffinity: ListTileControlAffinity.leading,
              );
            });
}

// class ComponentUtils1 {
//   static bool equalsIgnoreCase(String? a, String b) =>
//       (a == null && b == null) || (a != null && b != null && a.toLowerCase() == b.toLowerCase());
//   static String platform = GetPlatform.isIOS ? 'ios' : 'android';
//   static Color primecolor = HexColor('#b10c00');
//   static Color tablabelcolor = HexColor('#3953A4');
//   static Color tabunselectedLabelColor = HexColor('#4C4B5D').withOpacity(0.85);
//   static Color tabindicatorColor = HexColor('#b10c00'); //  .withOpacity(0.85);
//   static Color primary = const Color(0xff696b9e);
//   static Color secondary = const Color(0xfff29a94);
//   static Color bodybackground = CommonUtils.createMaterialColor(const Color(0XFFf2f2f2));
//   static Color darkBackground = CommonUtils.createMaterialColor(Color.fromARGB(255, 41, 41, 41));
//   static Icon backpageIcon = GetPlatform.isIOS ? const Icon(Icons.arrow_back_ios) : const Icon(Icons.arrow_back);

//   static TextStyle appbartitlestyle =
//       TextStyle(color: CommonUtils.createMaterialColor(Color(0XFFb10c00)), fontWeight: FontWeight.bold, fontSize: 16);

//   static Widget? apiImage(var picId, {double? height, double? width}) {
//     try {
//       Map<String, String> headers = SharedPrefUtils.getHeaders();
//       String pId = picId != null ? picId.toString() : '0';
//       String url = imageapiurl + '?picId=' + pId;
//       String imageurl = ApiService.getRestPathurl(url);
//       debugPrint('imageurl>>>>>$imageurl');

//       return FadeInImage(
//           height: height ?? 30.0,
//           width: width ?? 30.0,
//           image: NetworkImage(imageurl, headers: headers),
//           placeholder: const AssetImage('lib/icons/no_image_icon.gif'),
//           imageErrorBuilder: (context, error, stackTrace) {
//             return Image.asset('lib/icons/no_image_icon.gif', height: height ?? 30.0, width: height ?? 30.0, fit: BoxFit.fitWidth);
//           });
//     } catch (e) {
//       debugPrint(e?.toString());
//     }
//     //placeholder: AssetImage('lib/icons/no_image_icon.gif');
//   }

//   static Widget labelLoadScaffold() {
//     return Scaffold(
//       appBar: AppBar(
//         iconTheme: Theme.of(Get.context!).appBarTheme.iconTheme,
//         title: Text('.....', style: appbartitlestyle),
//         backgroundColor: Colors.white,
//         elevation: 5,
//         leading: IconButton(
//           icon: ComponentUtils.backpageIcon,
//           color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
//           onPressed: () {
//             debugPrint('------back-------');
//             Get.back();
//           },
//         ),
//       ),
//       body: const ProgressIndicatorCust(),
//     );
//   }

//   static bool strbool(String str) {
//     if (str != null && (str.toUpperCase() == 'TRUE')) {
//       return true;
//     } else {
//       return false;
//     }
//   }

//   static String dateToString(String? dt) {
//     String formatted = '';
//     if (dt != null && dt.isNotEmpty)
//       try {
//         DateFormat formatter = DateFormat('MM/dd/yyyy');
//         formatted = formatter.format(DateTime.parse(dt));
//       } catch (e) {
//         debugPrint('exception  $e');
//       }
//     return formatted;
//   }

//   static int? compareDates(String dt1, String dt2) {
//     //0 --unknown
//     //1--- dt1>dt2
//     //2--- dt2>dt1

//     int res = 0;

//     var d1 = dateToString(dt1);
//     var d2 = dateToString(dt2);
//     if (d1 != null && d1 != '' && d2 != null && d2 != '') {
//       DateTime t1 = DateTime.parse(d1);
//       DateTime t2 = DateTime.parse(d2);
//       if (t1.isAfter(t2) || t1.isAtSameMomentAs(t2)) {
//         res = 1;
//       } else {
//         res = 2;
//       }
//       return res;
//     }
//   }

//   static double? StrToDouble(String val) {
//     double? db = null;
//     if (val != null && val != '') db = int.tryParse(val) == null ? double.tryParse(val) : int.tryParse(val)! + .0;
//     return db;
//   }

//   static scrollToIndex(index, ScrollController ctrl) {
//     final double height = 92;
//     ctrl.animateTo(height * index, duration: const Duration(seconds: 2), curve: Curves.easeIn);
//   }

//   static void documentviewer(BuildContext context, String documentid) {
//     var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
//     Navigator.push(
//         context,
//         MaterialPageRoute(
//             builder: (context) => WebViewContainer1(
//                   ApiService.getServerurl() + '$documentviewerurl?documentId=$documentid&JSESSIONID=$sessionid',
//                   'View Document',
//                   source: 'DOCVIEW',
//                 )));
//   }

//   static showsnackbar({String? heading, required String text, int? seconds}) {
//     Get.snackbar(
//       heading ?? 'info:',
//       text,
//       icon: Icon(Icons.info, color: Colors.white),
//       backgroundColor: Colors.black87,
//       colorText: Colors.white,
//       snackPosition: SnackPosition.BOTTOM,
//       duration: Duration(seconds: seconds ?? 2),
//       isDismissible: true,
//       //dismissDirection: SnackDismissDirection.HORIZONTAL,
//       forwardAnimationCurve: Curves.easeOutBack,
//     );
//   }

//   static showpopup({String? type, required String msg}) {
//     Get.defaultDialog(
//         title: type ?? 'Info',
//         titleStyle: type == 'Error'
//             ? TextStyle(fontSize: 16, color: primecolor)
//             : const TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//               ),
//         barrierDismissible: true,
//         content: Container(
//           padding: const EdgeInsets.fromLTRB(15.0, 0.0, 15.0, 0.0),
//           child: Text(
//             msg,
//             style: const TextStyle(fontSize: 12),
//           ),
//         ),
//         actions: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             children: [
//               TaButton(
//                 type: 'elevate',
//                 buttonText: 'Ok',
//                 onPressed: () {
//                   Get.back();
//                 },
//               ),
//               const SizedBox(
//                 width: 7.0,
//               ),
//             ],
//           ),
//         ]);
//   }

//   static showwarnpopup({String? title, String? contenttext, String? confirmBtn, Function? confirmfunction}) {
//     Get.defaultDialog(
//         title: title ?? 'Delete',
//         titleStyle: TextStyle(fontSize: 16, color: primecolor),
//         content: Container(
//             // height: 250,
//             child: Text(contenttext ?? 'Are you sure?')),
//         actions: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             children: [
//               TaButton(
//                 type: 'elevate',
//                 buttonText: 'Cancel',
//                 onPressed: () {
//                   Get.back();
//                 },
//               ),
//               SizedBox(
//                 width: 4.0,
//               ),
//               TaButton(
//                 type: 'elevate',
//                 buttonText: confirmBtn ?? 'Yes',
//                 onPressed: () {
//                   confirmfunction!();
//                 },
//               ),
//               SizedBox(
//                 width: 7.0,
//               ),
//             ],
//           ),
//         ]);
//   }

//   static String convertNumberFormat(String val) {
//     String? locale = Get.find<HomeController>().userinfo.value.userPrefLocale;
//     locale = (locale != null && locale != '') ? locale.replaceAll('-', '_') : 'en_US';

//     final nf = NumberFormat.currency(
//         //customPattern: '#,###.##',
//         locale: locale,
//         symbol: '');
//     return nf.format(double.tryParse(val));
//   }

//   static Widget listVertRow(String label, String val, {String? dtype}) {
//     final primary = ComponentUtils.primary;
//     final nf = NumberFormat.currency(
//       customPattern: '#,###.##',
//       //locale: 'en_US',
//     );
//     return Container(
//         margin: EdgeInsets.symmetric(vertical: 3, horizontal: 5),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Text(
//               label,
//               style: TextStyle(
//                   color: primary, //fontWeight: FontWeight.bold,
//                   fontSize: 12),
//               overflow: TextOverflow.ellipsis,
//             ),
//             Text(
//               //dtype == 'num' ? nf.format(double.tryParse(val)) : val,
//               dtype == 'num' ? convertNumberFormat(val) : val,
//               style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
//             ),
//           ],
//         ));
//   }

//   static void mapviewer(BuildContext context) {
//     var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
//     var user = UserContext.info()!.userName;
//     var url = ApiService.getServerurl() + '$mappingurl&username=$user';
//     //var url = 'http://localhost:7001/mapv4services/?context=true&map_locale=en&isApp=true&username=$user';
//     //url = 'https://dev.tangoanalytics.com/spacemgmt/?floorId=579769&spaceId=160&buildingId=579744&action=MAC';

//     Get.to(
//       () => WebViewContainer1(
//         url,
//         'Maps',
//         source: 'MAPVIEW',
//       ),
//     );

//     //Get.to(() => InappWebView('Maps', url));
//   }

//   static Future<void> biddingviewer(BuildContext context, String pgname) async {
//     var sessionid = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
//     var biddinghosturl = await SharedPrefUtils.readPrefStr(ConstHelper.biddingurl);
//     var user = UserContext.info()!.userName;
//     var url = '$biddinghosturl?username=$user&jsessionid=$sessionid';
//     Get.to(
//       () => WebViewContainer1(
//         url,
//         pgname,
//         source: 'BIDVIEW',
//       ),
//     );
//   }
// }
