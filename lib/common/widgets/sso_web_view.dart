import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'package:tangoworkplace/common/widgets/login_session.dart';
import 'package:tangoworkplace/screens/login_screen.dart';

import 'package:tangoworkplace/utils/preferences_utils.dart';

class SSOWebView extends StatefulWidget {
  final url;
  final source;
  final username;

  SSOWebView(this.url, this.source, this.username);
  @override
  createState() => _SSOWebViewState(this.url, this.source, this.username);
}

class _SSOWebViewState extends State<SSOWebView> {
  var _url;
  var _source;
  var _username;
  final _key = UniqueKey();
  final CookieManager cookieManager = CookieManager();
  WebViewController? webViewController;
  _SSOWebViewState(this._url, this._source, this._username);
  final Completer<WebViewController> _controller = Completer<WebViewController>();

  final cookiemngr = WebviewCookieManager();

  @override
  void initState() {
    super.initState();
    if (Platform.isAndroid) {
      WebView.platform = SurfaceAndroidWebView();
    }
  }

  @override
  void dispose() async {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(
          title: Text("SSO Login",
              style: new TextStyle(
                fontSize: 15.0,
                color: Colors.white,
              )),
        ),
        body: new Container(
          child: WebView(
              zoomEnabled: true,
              gestureNavigationEnabled: true,
              gestureRecognizers: [
                Factory(
                  () => VerticalDragGestureRecognizer(),
                ),
                Factory(() => HorizontalDragGestureRecognizer()),
                Factory(() => TapGestureRecognizer()),
              ].toSet(),
              key: _key,
              javascriptMode: JavascriptMode.unrestricted,
              onWebViewCreated: (WebViewController webViewController) async {
                webViewController.loadUrl(_url);
              },
              navigationDelegate: (NavigationRequest request) async {
                debugPrint(request.url);
                debugPrint("WebView url ${request.url}");
                if (_source == "login") {
                  if (request.url.contains("tmcadfv01/faces/TMCHomePG.jspx")) {
                    debugPrint("Webview done Inside!!!!!!!!!!!!!!!!");
                    String? seid = await getcookies(request.url);
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginSessionImpl(_username, seid: seid),
                      ),
                    );
                    return NavigationDecision.prevent;
                  } else {
                    return NavigationDecision.navigate;
                  }
                } else {
                  return NavigationDecision.navigate;
                }
              },
              onPageFinished: (String url) async {
                debugPrint("WebView url $url");
                if (_source == "logout") {
                  SharedPrefUtils.saveStr("SSOEnabled", null);
                  SharedPrefUtils.saveStr("SSOLogoutURL", "");
                  SharedPrefUtils.clear();
                  //clearCookies(webViewController, context);
                  Navigator.pushNamedAndRemoveUntil(context, LoginPage.routName, (Route<dynamic> route) => false);
                }
              }),
        ));
  }

  Future<String?> getcookies(String url) async {
    //debugPrint('finish url   ' + url);
    String? seid;
    final gotCookies = await cookiemngr.getCookies(url);
    debugPrint('$gotCookies');
    for (var item in gotCookies) {
      if (item.name.startsWith('JSESSIONID')) seid = item.toString();
    }
    //cookiemngr.removeCookie(url);
    return seid;
  }

  void clearCookies(WebViewController controller, BuildContext context) async {
    final bool hadCookies = await cookieManager.clearCookies();
  }
}
