

import 'package:flutter/material.dart';

import 'aq_widget_constants.dart';

class AqSpace extends StatelessWidget {

  final double customSpace;
  
  const AqSpace({
    Key? key,
    this.customSpace = AqWidgetConstants.spaceHeight
  })  : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
          height: customSpace,
          width: customSpace,
        );
  }
}