
import 'package:flutter/material.dart';

import 'aq_widget_constants.dart';

class AqDevider extends StatelessWidget {

  final double customSpace;
  
  const AqDevider({
    Key? key,
    this.customSpace = AqWidgetConstants.spaceHeight
  })  : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
          height: customSpace,
          child: const Divider(
            thickness: 1,
            indent: 0,
            endIndent: 0,
            color: Colors.grey,
          ),
        );
  }
}